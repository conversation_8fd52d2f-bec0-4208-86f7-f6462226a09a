const fs = require('fs');
const path = require('path');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');
const { performance } = require('perf_hooks');

// PS5 Batch Processing Configuration
const PS5_BATCH_CONFIG = {
  MAX_WORKERS: 8, // PS5 CPU cores
  MEMORY_LIMIT: 16 * 1024 * 1024 * 1024, // 16GB RAM
  BATCH_SIZE: 16, // Optimal batch size for PS5
  QUEUE_SIZE: 64,
  PRIORITY_LEVELS: 3,
  AUTO_SCALING: true,
  MEMORY_MONITORING: true,
  PERFORMANCE_TRACKING: true
};

// PS5 Batch Processing Manager
class PS5BatchManager {
  constructor(config = PS5_BATCH_CONFIG) {
    this.config = config;
    this.workers = [];
    this.taskQueue = [];
    this.completedTasks = [];
    this.failedTasks = [];
    this.isProcessing = false;
    this.stats = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageTime: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      startTime: null,
      endTime: null
    };
    this.memoryMonitor = null;
    this.performanceTracker = new PS5PerformanceTracker();
  }

  // PS5 Worker Pool oluştur
  async initializeWorkerPool() {
    console.log('🎮 PS5 Worker Pool başlatılıyor...');
    console.log(`⚡ CPU Çekirdekleri: ${this.config.MAX_WORKERS}`);
    console.log(`💾 Bellek Limiti: ${(this.config.MEMORY_LIMIT / 1024 / 1024 / 1024).toFixed(1)}GB`);
    
    for (let i = 0; i < this.config.MAX_WORKERS; i++) {
      const worker = new Worker(__filename, {
        workerData: { workerId: i, config: this.config }
      });
      
      worker.on('message', (result) => this.handleWorkerMessage(result));
      worker.on('error', (error) => this.handleWorkerError(error, i));
      worker.on('exit', (code) => this.handleWorkerExit(code, i));
      
      this.workers.push({
        worker: worker,
        id: i,
        busy: false,
        tasksCompleted: 0,
        averageTime: 0,
        memoryUsage: 0
      });
    }
    
    if (this.config.MEMORY_MONITORING) {
      this.startMemoryMonitoring();
    }
    
    console.log(`✅ ${this.workers.length} worker hazır`);
  }

  // Bellek izleme başlat
  startMemoryMonitoring() {
    this.memoryMonitor = setInterval(() => {
      const memUsage = process.memoryUsage();
      this.stats.memoryUsage = memUsage.heapUsed;
      
      // Bellek limiti kontrolü
      if (memUsage.heapUsed > this.config.MEMORY_LIMIT * 0.9) {
        console.warn('⚠️  Bellek kullanımı yüksek, garbage collection tetikleniyor...');
        if (global.gc) {
          global.gc();
        }
      }
    }, 1000);
  }

  // Batch task ekle
  addBatchTasks(tasks, priority = 1) {
    const batchedTasks = this.createBatches(tasks, this.config.BATCH_SIZE);
    
    batchedTasks.forEach(batch => {
      this.taskQueue.push({
        id: this.generateTaskId(),
        batch: batch,
        priority: priority,
        createdAt: Date.now(),
        attempts: 0,
        maxAttempts: 3
      });
    });
    
    this.stats.totalTasks += batchedTasks.length;
    console.log(`📦 ${batchedTasks.length} batch task eklendi (toplam: ${this.taskQueue.length})`);
    
    if (!this.isProcessing) {
      this.startProcessing();
    }
  }

  // Task'ları batch'lere böl
  createBatches(tasks, batchSize) {
    const batches = [];
    for (let i = 0; i < tasks.length; i += batchSize) {
      batches.push(tasks.slice(i, i + batchSize));
    }
    return batches;
  }

  // İşleme başlat
  async startProcessing() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.stats.startTime = performance.now();
    
    console.log('🚀 PS5 Batch Processing başlatıldı');
    
    while (this.taskQueue.length > 0 || this.hasActiveTasks()) {
      await this.assignTasks();
      await this.sleep(100); // CPU'yu rahatlatmak için kısa bekleme
    }
    
    this.stats.endTime = performance.now();
    this.isProcessing = false;
    
    await this.generateReport();
  }

  // Task'ları worker'lara ata
  async assignTasks() {
    const availableWorkers = this.workers.filter(w => !w.busy);
    
    if (availableWorkers.length === 0 || this.taskQueue.length === 0) {
      return;
    }
    
    // Priority sıralaması
    this.taskQueue.sort((a, b) => b.priority - a.priority);
    
    for (const worker of availableWorkers) {
      if (this.taskQueue.length === 0) break;
      
      const task = this.taskQueue.shift();
      worker.busy = true;
      
      const startTime = performance.now();
      
      worker.worker.postMessage({
        type: 'PROCESS_BATCH',
        task: task,
        startTime: startTime
      });
    }
  }

  // Worker mesajlarını işle
  handleWorkerMessage(result) {
    const worker = this.workers.find(w => w.id === result.workerId);
    if (!worker) return;
    
    worker.busy = false;
    worker.tasksCompleted++;
    
    const taskTime = performance.now() - result.startTime;
    worker.averageTime = (worker.averageTime * (worker.tasksCompleted - 1) + taskTime) / worker.tasksCompleted;
    
    if (result.success) {
      this.completedTasks.push(result);
      this.stats.completedTasks++;
      console.log(`✅ Batch ${result.taskId} tamamlandı (Worker ${result.workerId}) - ${taskTime.toFixed(2)}ms`);
    } else {
      this.handleFailedTask(result);
    }
    
    // Performance tracking
    this.performanceTracker.recordTask(result);
  }

  // Başarısız task'ları işle
  handleFailedTask(result) {
    const task = result.task;
    task.attempts++;
    
    if (task.attempts < task.maxAttempts) {
      console.log(`🔄 Batch ${task.id} yeniden deneniyor (${task.attempts}/${task.maxAttempts})`);
      this.taskQueue.unshift(task); // Öncelik ver
    } else {
      console.error(`❌ Batch ${task.id} başarısız (${task.attempts} deneme)`);
      this.failedTasks.push(result);
      this.stats.failedTasks++;
    }
  }

  // Worker hata işleme
  handleWorkerError(error, workerId) {
    console.error(`❌ Worker ${workerId} hatası:`, error.message);
    
    // Worker'ı yeniden başlat
    this.restartWorker(workerId);
  }

  // Worker çıkış işleme
  handleWorkerExit(code, workerId) {
    if (code !== 0) {
      console.warn(`⚠️  Worker ${workerId} beklenmedik şekilde kapandı (kod: ${code})`);
      this.restartWorker(workerId);
    }
  }

  // Worker'ı yeniden başlat
  async restartWorker(workerId) {
    const workerInfo = this.workers[workerId];
    if (workerInfo) {
      workerInfo.worker.terminate();
      
      const newWorker = new Worker(__filename, {
        workerData: { workerId: workerId, config: this.config }
      });
      
      newWorker.on('message', (result) => this.handleWorkerMessage(result));
      newWorker.on('error', (error) => this.handleWorkerError(error, workerId));
      newWorker.on('exit', (code) => this.handleWorkerExit(code, workerId));
      
      workerInfo.worker = newWorker;
      workerInfo.busy = false;
      
      console.log(`🔄 Worker ${workerId} yeniden başlatıldı`);
    }
  }

  // Aktif task kontrolü
  hasActiveTasks() {
    return this.workers.some(w => w.busy);
  }

  // Rapor oluştur
  async generateReport() {
    const totalTime = this.stats.endTime - this.stats.startTime;
    const avgTime = totalTime / this.stats.completedTasks;
    
    console.log('\n🎮 PS5 Batch Processing Raporu');
    console.log('================================');
    console.log(`⏱️  Toplam Süre: ${(totalTime / 1000).toFixed(2)} saniye`);
    console.log(`📊 Toplam Task: ${this.stats.totalTasks}`);
    console.log(`✅ Tamamlanan: ${this.stats.completedTasks}`);
    console.log(`❌ Başarısız: ${this.stats.failedTasks}`);
    console.log(`📈 Başarı Oranı: ${((this.stats.completedTasks / this.stats.totalTasks) * 100).toFixed(1)}%`);
    console.log(`⚡ Ortalama Task Süresi: ${avgTime.toFixed(2)}ms`);
    console.log(`💾 Maksimum Bellek: ${(this.stats.memoryUsage / 1024 / 1024).toFixed(1)}MB`);
    
    // Worker istatistikleri
    console.log('\n👥 Worker İstatistikleri:');
    this.workers.forEach(worker => {
      console.log(`Worker ${worker.id}: ${worker.tasksCompleted} task, avg: ${worker.averageTime.toFixed(2)}ms`);
    });
    
    // Performance insights
    this.performanceTracker.generateInsights();
  }

  // Cleanup
  async cleanup() {
    console.log('🧹 PS5 Batch Manager temizleniyor...');
    
    if (this.memoryMonitor) {
      clearInterval(this.memoryMonitor);
    }
    
    await Promise.all(this.workers.map(w => w.worker.terminate()));
    this.workers = [];
    
    console.log('✅ Cleanup tamamlandı');
  }

  // Utility functions
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// PS5 Performance Tracker
class PS5PerformanceTracker {
  constructor() {
    this.taskTimes = [];
    this.memorySnapshots = [];
    this.cpuUsage = [];
  }

  recordTask(result) {
    this.taskTimes.push({
      taskId: result.taskId,
      workerId: result.workerId,
      duration: result.duration,
      timestamp: Date.now()
    });
  }

  generateInsights() {
    if (this.taskTimes.length === 0) return;
    
    const times = this.taskTimes.map(t => t.duration);
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    console.log('\n📊 Performance Insights:');
    console.log(`⚡ En Hızlı Task: ${minTime.toFixed(2)}ms`);
    console.log(`🐌 En Yavaş Task: ${maxTime.toFixed(2)}ms`);
    console.log(`📈 Ortalama: ${avgTime.toFixed(2)}ms`);
    console.log(`📊 Standart Sapma: ${this.calculateStandardDeviation(times).toFixed(2)}ms`);
  }

  calculateStandardDeviation(values) {
    const avg = values.reduce((a, b) => a + b, 0) / values.length;
    const squareDiffs = values.map(value => Math.pow(value - avg, 2));
    const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
    return Math.sqrt(avgSquareDiff);
  }
}

// Worker Thread Implementation
if (!isMainThread) {
  const { workerId, config } = workerData;
  
  parentPort.on('message', async (message) => {
    if (message.type === 'PROCESS_BATCH') {
      const startTime = performance.now();
      
      try {
        // Batch processing logic burada
        const results = await processBatch(message.task.batch);
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        parentPort.postMessage({
          workerId: workerId,
          taskId: message.task.id,
          success: true,
          results: results,
          duration: duration,
          startTime: message.startTime,
          memoryUsage: process.memoryUsage().heapUsed
        });
        
      } catch (error) {
        parentPort.postMessage({
          workerId: workerId,
          taskId: message.task.id,
          success: false,
          error: error.message,
          task: message.task,
          startTime: message.startTime
        });
      }
    }
  });
  
  // Batch processing function
  async function processBatch(batch) {
    const { generatePS5NFTs } = require('./ps5-production-system');
    
    // Her batch item için NFT üret
    const results = [];
    for (const item of batch) {
      try {
        const result = await generateSingleNFT(item);
        results.push(result);
      } catch (error) {
        results.push({ error: error.message, item: item });
      }
    }
    
    return results;
  }
  
  async function generateSingleNFT(nftConfig) {
    // Single NFT generation logic
    const { createPS5Art, makePS5Traits } = require('./ps5-production-system');
    
    const seed = nftConfig.seed || Math.floor(Math.random() * 1e9);
    const traits = makePS5Traits(seed);
    
    // Memory-optimized art creation
    const canvas = createPS5Art(seed, traits);
    
    return {
      seed: seed,
      traits: traits,
      success: true,
      timestamp: Date.now()
    };
  }
}

module.exports = {
  PS5BatchManager,
  PS5PerformanceTracker,
  PS5_BATCH_CONFIG
};
