import { r as rendererGL, p as primitives3D, l as light, m as material, c as camera, f as framebuffer, s as shader, t as texture } from '../rendering-CpHn8PfG.js';
import interaction from './interaction.js';
import loading from './loading.js';
import text from './text.js';
import renderBuffer from './p5.RenderBuffer.js';
import quat from './p5.Quat.js';
import matrix from '../math/p5.Matrix.js';
import geometry from './p5.Geometry.js';
import dataArray from './p5.DataArray.js';
import shadergenerator from './ShaderGenerator.js';
import '../constants-C2DVjshm.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../dom/p5.Element.js';
import '../dom/p5.File.js';
import '../io/p5.XML.js';
import '../p5.Renderer-DO9wIL55.js';
import '../image/filters.js';
import '../math/p5.Vector.js';
import '../shape/custom_shapes.js';
import '../core/States.js';
import '../io/utilities.js';
import 'file-saver';
import '../dom/p5.MediaElement.js';
import '../shape/2d_primitives.js';
import '../core/helpers.js';
import '../shape/attributes.js';
import '../shape/curves.js';
import '../shape/vertex.js';
import '../color/setting.js';
import 'omggif';
import '../io/csv.js';
import 'gifenc';
import '../image/pixels.js';
import '../core/transform.js';
import './GeometryBuilder.js';
import '../math/Matrices/Matrix.js';
import '../math/Matrices/MatrixInterface.js';
import './ShapeBuilder.js';
import 'libtess';
import './GeometryBufferCache.js';
import '../image/const.js';
import '../math/trigonometry.js';
import '../type/p5.Font.js';
import '../type/textCore.js';
import '@japont/unicode-range';
import '../type/unicodeRanges.js';
import '../type/lib/Typr.js';
import 'pako';
import '@davepagurek/bezier-path';
import 'acorn';
import 'acorn-walk';
import 'escodegen';

function webgl(p5){
  rendererGL(p5, p5.prototype);
  primitives3D(p5, p5.prototype);
  interaction(p5, p5.prototype);
  light(p5, p5.prototype);
  loading(p5, p5.prototype);
  material(p5, p5.prototype);
  text(p5, p5.prototype);
  renderBuffer(p5, p5.prototype);
  quat(p5, p5.prototype);
  matrix(p5, p5.prototype);
  geometry(p5, p5.prototype);
  camera(p5, p5.prototype);
  framebuffer(p5, p5.prototype);
  dataArray(p5, p5.prototype);
  shader(p5, p5.prototype);
  texture(p5, p5.prototype);
  shadergenerator(p5, p5.prototype);
}

export { webgl as default };
