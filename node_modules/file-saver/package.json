{"_from": "file-saver@1.3.3", "_id": "file-saver@1.3.3", "_inBundle": false, "_integrity": "sha1-zdTETTqiZOrC9o7BZbx5HDSvEjI=", "_location": "/file-saver", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "file-saver@1.3.3", "name": "file-saver", "escapedName": "file-saver", "rawSpec": "1.3.3", "saveSpec": null, "fetchSpec": "1.3.3"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/file-saver/-/file-saver-1.3.3.tgz", "_shasum": "cdd4c44d3aa264eac2f68ec165bc791c34af1232", "_spec": "file-saver@1.3.3", "_where": "/Users/<USER>/git/FileSaver.js", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/eligrey/FileSaver.js/issues"}, "bundleDependencies": false, "deprecated": false, "description": "An HTML5 saveAs() FileSaver implementation", "devDependencies": {"uglify-js": "^2.6.2"}, "homepage": "https://github.com/eligrey/FileSaver.js#readme", "keywords": ["filesaver", "saveas", "blob"], "license": "MIT", "main": "FileSaver.js", "name": "file-saver", "repository": {"type": "git", "url": "git+https://github.com/eligrey/FileSaver.js.git"}, "scripts": {"build": "uglifyjs FileSaver.js --mangle --comments /@source/ > FileSaver.min.js", "test": "echo \"Error: no test specified\" && exit 0"}, "version": "1.3.8"}