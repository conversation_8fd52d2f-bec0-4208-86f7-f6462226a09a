{"version": 3, "sources": ["../src/constants.js", "../src/stream.js", "../src/lzwEncode.js", "../src/rgb-packing.js", "../src/pnnquant2.js", "../src/color.js", "../src/palettize.js", "../src/index.js"], "sourcesContent": ["export default {\n  signature: \"G<PERSON>\",\n  version: \"89a\",\n  trailer: 0x3B,\n  extensionIntroducer: 0x21,\n  applicationExtensionLabel: 0xFF,\n  graphicControlExtensionLabel: 0xF9,\n  imageSeparator: 0x2C,\n  // Header\n  signatureSize: 3,\n  versionSize: 3,\n  globalColorTableFlagMask: 0b10000000,\n  colorResolutionMask: 0b01110000,\n  sortFlagMask: 0b00001000,\n  globalColorTableSizeMask: 0b00000111,\n  // Application extension\n  applicationIdentifierSize: 8,\n  applicationAuthCodeSize: 3,\n  // Graphic control extension\n  disposalMethodMask: 0b00011100,\n  userInputFlagMask: 0b00000010,\n  transparentColorFlagMask: 0b00000001,\n  // Image descriptor\n  localColorTableFlagMask: 0b10000000,\n  interlaceFlagMask: 0b01000000,\n  idSortFlagMask: 0b00100000,\n  localColorTableSizeMask: 0b00000111\n}\n", "export default function createStream(initialCapacity = 256) {\n  let cursor = 0;\n  let contents = new Uint8Array(initialCapacity);\n\n  return {\n    get buffer() {\n      return contents.buffer;\n    },\n    reset() {\n      cursor = 0;\n    },\n    bytesView() {\n      return contents.subarray(0, cursor);\n    },\n    bytes() {\n      return contents.slice(0, cursor);\n    },\n    writeByte(byte) {\n      expand(cursor + 1);\n      contents[cursor] = byte;\n      cursor++;\n    },\n    writeBytes(data, offset = 0, byteLength = data.length) {\n      expand(cursor + byteLength);\n      for (let i = 0; i < byteLength; i++) {\n        contents[cursor++] = data[i + offset];\n      }\n    },\n    writeBytesView(data, offset = 0, byteLength = data.byteLength) {\n      expand(cursor + byteLength);\n      contents.set(data.subarray(offset, offset + byteLength), cursor);\n      cursor += byteLength;\n    },\n  };\n\n  function expand(newCapacity) {\n    var prevCapacity = contents.length;\n    if (prevCapacity >= newCapacity) return; // No need to expand, the storage was already large enough.\n    // Don't expand strictly to the given requested limit if it's only a very small increase, but instead geometrically grow capacity.\n    // For small filesizes (<1MB), perform size*2 geometric increase, but for large sizes, do a much more conservative size*1.125 increase to\n    // avoid overshooting the allocation cap by a very large margin.\n    var CAPACITY_DOUBLING_MAX = 1024 * 1024;\n    newCapacity = Math.max(\n      newCapacity,\n      (prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2.0 : 1.125)) >>>\n        0\n    );\n    if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256); // At minimum allocate 256b for each file when expanding.\n    const oldContents = contents;\n    contents = new Uint8Array(newCapacity); // Allocate new storage.\n    if (cursor > 0) contents.set(oldContents.subarray(0, cursor), 0);\n  }\n}\n", "/*\n  LZWEncoder.js\n  Authors\n  <PERSON> (original Java version - <EMAIL>)\n  <PERSON><PERSON><PERSON><PERSON> (AS3 version - bytearray.org)\n  <PERSON> (JS version - <EMAIL>)\n  Acknowledgements\n  GIFCOMPR.C - GIF Image compression routines\n  Lempel-Ziv compression based on 'compress'. GIF modifications by\n  <PERSON> (<EMAIL>)\n  GIF Image compression - modified 'compress'\n  Based on: compress.c - File compression ala IEEE Computer, June 1984.\n  By Authors: <AUTHORS>\n  <PERSON> (decvax!mcvax!jim)\n  <PERSON> (decvax!vax135!petsd!peora!srd)\n  <PERSON> (decvax!decwrl!turtlevax!ken)\n  <PERSON> (decvax!ihnp4!ames!jaw)\n  <PERSON> (decvax!vax135!petsd!joe)\n  <PERSON> (@mattdesl - V8/JS optimizations)\n  <PERSON><PERSON> (@p01 - JS optimization)\n*/\n\nimport createStream from \"./stream.js\";\n\nconst EOF = -1;\nconst BITS = 12;\nconst DEFAULT_HSIZE = 5003; // 80% occupancy\nconst MASKS = [\n  0x0000,\n  0x0001,\n  0x0003,\n  0x0007,\n  0x000f,\n  0x001f,\n  0x003f,\n  0x007f,\n  0x00ff,\n  0x01ff,\n  0x03ff,\n  0x07ff,\n  0x0fff,\n  0x1fff,\n  0x3fff,\n  0x7fff,\n  0xffff,\n];\n\nfunction lzwEncode(\n  width,\n  height,\n  pixels,\n  colorDepth,\n  outStream = createStream(512),\n  accum = new Uint8Array(256),\n  htab = new Int32Array(DEFAULT_HSIZE),\n  codetab = new Int32Array(DEFAULT_HSIZE)\n) {\n  const hsize = htab.length;\n  const initCodeSize = Math.max(2, colorDepth);\n\n  accum.fill(0);\n  codetab.fill(0);\n  htab.fill(-1);\n\n  let cur_accum = 0;\n  let cur_bits = 0;\n\n  // Algorithm: use open addressing double hashing (no chaining) on the\n  // prefix code / next character combination. We do a variant of Knuth's\n  // algorithm D (vol. 3, sec. 6.4) along with G. Knott's relatively-prime\n  // secondary probe. Here, the modular division first probe is gives way\n  // to a faster exclusive-or manipulation. Also do block compression with\n  // an adaptive reset, whereby the code table is cleared when the compression\n  // ratio decreases, but after the table fills. The variable-length output\n  // codes are re-sized at this point, and a special CLEAR code is generated\n  // for the decompressor. Late addition: construct the table according to\n  // file size for noticeable speed improvement on small files. Please direct\n  // questions about this implementation to ames!jaw.\n\n  // compress and write the pixel data\n  const init_bits = initCodeSize + 1;\n\n  // Set up the globals: g_init_bits - initial number of bits\n  const g_init_bits = init_bits;\n\n  // Set up the necessary values\n\n  // block compression parameters -- after all codes are used up,\n  // and compression rate changes, start over.\n  let clear_flg = false;\n  let n_bits = g_init_bits;\n  let maxcode = (1 << n_bits) - 1;\n\n  const ClearCode = 1 << (init_bits - 1);\n  const EOFCode = ClearCode + 1;\n  let free_ent = ClearCode + 2;\n  let a_count = 0; // clear packet\n\n  let ent = pixels[0];\n\n  let hshift = 0;\n  for (let fcode = hsize; fcode < 65536; fcode *= 2) {\n    ++hshift;\n  }\n  hshift = 8 - hshift; // set hash code range bound\n\n  outStream.writeByte(initCodeSize); // write \"initial code size\" byte\n\n  output(ClearCode);\n\n  const length = pixels.length;\n  for (let idx = 1; idx < length; idx++) {\n    next_block: {\n      const c = pixels[idx];\n      const fcode = (c << BITS) + ent;\n      let i = (c << hshift) ^ ent; // xor hashing\n      if (htab[i] === fcode) {\n        ent = codetab[i];\n        break next_block;\n      }\n\n      const disp = i === 0 ? 1 : hsize - i; // secondary hash (after G. Knott)\n      while (htab[i] >= 0) {\n        // non-empty slot\n        i -= disp;\n        if (i < 0) i += hsize;\n        if (htab[i] === fcode) {\n          ent = codetab[i];\n          break next_block;\n        }\n      }\n      output(ent);\n      ent = c;\n      if (free_ent < 1 << BITS) {\n        codetab[i] = free_ent++; // code -> hashtable\n        htab[i] = fcode;\n      } else {\n        // Clear out the hash table\n        // table clear for block compress\n        htab.fill(-1);\n        free_ent = ClearCode + 2;\n        clear_flg = true;\n        output(ClearCode);\n      }\n    }\n  }\n\n  // Put out the final code.\n  output(ent);\n  output(EOFCode);\n\n  outStream.writeByte(0); // write block terminator\n  return outStream.bytesView();\n\n  function output(code) {\n    cur_accum &= MASKS[cur_bits];\n\n    if (cur_bits > 0) cur_accum |= code << cur_bits;\n    else cur_accum = code;\n\n    cur_bits += n_bits;\n\n    while (cur_bits >= 8) {\n      // Add a character to the end of the current packet, and if it is 254\n      // characters, flush the packet to disk.\n      accum[a_count++] = cur_accum & 0xff;\n      if (a_count >= 254) {\n        outStream.writeByte(a_count);\n        outStream.writeBytesView(accum, 0, a_count);\n        a_count = 0;\n      }\n      cur_accum >>= 8;\n      cur_bits -= 8;\n    }\n\n    // If the next entry is going to be too big for the code size,\n    // then increase it, if possible.\n    if (free_ent > maxcode || clear_flg) {\n      if (clear_flg) {\n        n_bits = g_init_bits;\n        maxcode = (1 << n_bits) - 1;\n        clear_flg = false;\n      } else {\n        ++n_bits;\n        maxcode = n_bits === BITS ? (1 << n_bits) : (1 << n_bits) - 1;\n      }\n    }\n\n    if (code == EOFCode) {\n      // At EOF, write the rest of the buffer.\n      while (cur_bits > 0) {\n        // Add a character to the end of the current packet, and if it is 254\n        // characters, flush the packet to disk.\n        accum[a_count++] = cur_accum & 0xff;\n        if (a_count >= 254) {\n          outStream.writeByte(a_count);\n          outStream.writeBytesView(accum, 0, a_count);\n          a_count = 0;\n        }\n        cur_accum >>= 8;\n        cur_bits -= 8;\n      }\n      // Flush the packet to disk, and reset the accumulator\n      if (a_count > 0) {\n        outStream.writeByte(a_count);\n        outStream.writeBytesView(accum, 0, a_count);\n        a_count = 0;\n      }\n    }\n  }\n}\n\nexport default lzwEncode;\n", "export function uint32_to_rgba(color) {\n  var a = (color >> 24) & 0xff;\n  var b = (color >> 16) & 0xff;\n  var g = (color >> 8) & 0xff;\n  var r = color & 0xff;\n  return [r, g, b, a];\n}\n\nexport function rgba_to_uint32(r, g, b, a) {\n  return (a << 24) | (b << 16) | (g << 8) | r;\n}\n\nexport function rgb888_to_rgb565(r, g, b) {\n  return ((r << 8) & 0xf800) | ((g << 2) & 0x03e0) | (b >> 3);\n}\n\nexport function rgba8888_to_rgba4444(r, g, b, a) {\n  return (r >> 4) | (g & 0xf0) | ((b & 0xf0) << 4) | ((a & 0xf0) << 8);\n}\n\nexport function rgb888_to_rgb444(r, g, b) {\n  return ((r >> 4) << 8) | (g & 0xf0) | (b >> 4);\n}\n\n// Alternative 565 ?\n// return ((r & 0xf8) << 8) + ((g & 0xfc) << 3) + (b >> 3);\n\n// Alternative 4444 ?\n// ((a & 0xf0) << 8) | ((r & 0xf0) << 4) | (g & 0xf0) | (b >> 4);\n", "// Modified from:\n// https://github.com/mcychan/PnnQuant.js/blob/master/src/pnnquant.js\n\n/* Fast pairwise nearest neighbor based algorithm for multilevel thresholding\nCopyright (C) 2004-2019 <PERSON> and <PERSON>\nCopyright (c) 2018-2021 <PERSON>\n* error measure; time used is proportional to number of bins squared - WJ */\n\nimport {\n  rgb888_to_rgb565,\n  rgb888_to_rgb444,\n  rgba8888_to_rgba4444,\n} from \"./rgb-packing.js\";\n\nfunction clamp(value, min, max) {\n  return value < min ? min : value > max ? max : value;\n}\n\nfunction sqr(value) {\n  return value * value;\n}\n\nfunction find_nn(bins, idx, hasAlpha) {\n  var nn = 0;\n  var err = 1e100;\n\n  const bin1 = bins[idx];\n  const n1 = bin1.cnt;\n  const wa = bin1.ac;\n  const wr = bin1.rc;\n  const wg = bin1.gc;\n  const wb = bin1.bc;\n  for (var i = bin1.fw; i != 0; i = bins[i].fw) {\n    const bin = bins[i];\n    const n2 = bin.cnt;\n    const nerr2 = (n1 * n2) / (n1 + n2);\n    if (nerr2 >= err) continue;\n\n    var nerr = 0;\n    if (hasAlpha) {\n      nerr += nerr2 * sqr(bin.ac - wa);\n      if (nerr >= err) continue;\n    }\n\n    nerr += nerr2 * sqr(bin.rc - wr);\n    if (nerr >= err) continue;\n\n    nerr += nerr2 * sqr(bin.gc - wg);\n    if (nerr >= err) continue;\n\n    nerr += nerr2 * sqr(bin.bc - wb);\n    if (nerr >= err) continue;\n    err = nerr;\n    nn = i;\n  }\n  bin1.err = err;\n  bin1.nn = nn;\n}\n\nfunction create_bin() {\n  return {\n    ac: 0,\n    rc: 0,\n    gc: 0,\n    bc: 0,\n    cnt: 0,\n    nn: 0,\n    fw: 0,\n    bk: 0,\n    tm: 0,\n    mtm: 0,\n    err: 0,\n  };\n}\n\nfunction bin_add_rgb(bin, r, g, b) {\n  bin.rc += r;\n  bin.gc += g;\n  bin.bc += b;\n  bin.cnt++;\n}\n\nfunction create_bin_list(data, format) {\n  const bincount = format === \"rgb444\" ? 4096 : 65536;\n  const bins = new Array(bincount);\n  const size = data.length;\n\n  /* Build histogram */\n  // Note: Instead of introducing branching/conditions\n  // within a very hot per-pixel iteration, we just duplicate the code\n  // for each new condition\n  if (format === \"rgba4444\") {\n    for (let i = 0; i < size; ++i) {\n      const color = data[i];\n      const a = (color >> 24) & 0xff;\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n\n      // reduce to rgb4444 16-bit uint\n      const index = rgba8888_to_rgba4444(r, g, b, a);\n      let bin = index in bins ? bins[index] : (bins[index] = create_bin());\n      bin.rc += r;\n      bin.gc += g;\n      bin.bc += b;\n      bin.ac += a;\n      bin.cnt++;\n    }\n  }\n  \n  else if (format === \"rgb444\") {\n    for (let i = 0; i < size; ++i) {\n      const color = data[i];\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n\n      // reduce to rgb444 12-bit uint\n      const index = rgb888_to_rgb444(r, g, b);\n      let bin = index in bins ? bins[index] : (bins[index] = create_bin());\n      bin.rc += r;\n      bin.gc += g;\n      bin.bc += b;\n      bin.cnt++;\n    }\n  } else {\n    for (let i = 0; i < size; ++i) {\n      const color = data[i];\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n\n      // reduce to rgb565 16-bit uint\n      const index = rgb888_to_rgb565(r, g, b);\n      let bin = index in bins ? bins[index] : (bins[index] = create_bin());\n      bin.rc += r;\n      bin.gc += g;\n      bin.bc += b;\n      bin.cnt++;\n    }\n  }\n  return bins;\n}\n\nexport default function quantize(rgba, maxColors, opts = {}) {\n  const {\n    format = \"rgb565\",\n    clearAlpha = true,\n    clearAlphaColor = 0x00,\n    clearAlphaThreshold = 0,\n    oneBitAlpha = false,\n  } = opts;\n\n  if (!rgba || !rgba.buffer) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  if (!(rgba instanceof Uint8Array) && !(rgba instanceof Uint8ClampedArray)) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  \n  const data = new Uint32Array(rgba.buffer);\n\n  let useSqrt = opts.useSqrt !== false;\n\n  // format can be:\n  // rgb565 (default)\n  // rgb444\n  // rgba4444\n\n  const hasAlpha = format === \"rgba4444\";\n  const bins = create_bin_list(data, format);\n  const bincount = bins.length;\n  const bincountMinusOne = bincount - 1;\n  const heap = new Uint32Array(bincount + 1);\n\n  /* Cluster nonempty bins at one end of array */\n  var maxbins = 0;\n  for (var i = 0; i < bincount; ++i) {\n    const bin = bins[i];\n    if (bin != null) {\n      var d = 1.0 / bin.cnt;\n      if (hasAlpha) bin.ac *= d;\n      bin.rc *= d;\n      bin.gc *= d;\n      bin.bc *= d;\n      bins[maxbins++] = bin;\n    }\n  }\n\n  if (sqr(maxColors) / maxbins < 0.022) {\n    useSqrt = false;\n  }\n\n  var i = 0;\n  for (; i < maxbins - 1; ++i) {\n    bins[i].fw = i + 1;\n    bins[i + 1].bk = i;\n    if (useSqrt) bins[i].cnt = Math.sqrt(bins[i].cnt);\n  }\n  if (useSqrt) bins[i].cnt = Math.sqrt(bins[i].cnt);\n\n  var h, l, l2;\n  /* Initialize nearest neighbors and build heap of them */\n  for (i = 0; i < maxbins; ++i) {\n    find_nn(bins, i, false);\n    /* Push slot on heap */\n    var err = bins[i].err;\n    for (l = ++heap[0]; l > 1; l = l2) {\n      l2 = l >> 1;\n      if (bins[(h = heap[l2])].err <= err) break;\n      heap[l] = h;\n    }\n    heap[l] = i;\n  }\n\n  /* Merge bins which increase error the least */\n  var extbins = maxbins - maxColors;\n  for (i = 0; i < extbins; ) {\n    var tb;\n    /* Use heap to find which bins to merge */\n    for (;;) {\n      var b1 = heap[1];\n      tb = bins[b1]; /* One with least error */\n      /* Is stored error up to date? */\n      if (tb.tm >= tb.mtm && bins[tb.nn].mtm <= tb.tm) break;\n      if (tb.mtm == bincountMinusOne)\n        /* Deleted node */ b1 = heap[1] = heap[heap[0]--];\n      /* Too old error value */ else {\n        find_nn(bins, b1, false);\n        tb.tm = i;\n      }\n      /* Push slot down */\n      var err = bins[b1].err;\n      for (l = 1; (l2 = l + l) <= heap[0]; l = l2) {\n        if (l2 < heap[0] && bins[heap[l2]].err > bins[heap[l2 + 1]].err) l2++;\n        if (err <= bins[(h = heap[l2])].err) break;\n        heap[l] = h;\n      }\n      heap[l] = b1;\n    }\n\n    /* Do a merge */\n    var nb = bins[tb.nn];\n    var n1 = tb.cnt;\n    var n2 = nb.cnt;\n    var d = 1.0 / (n1 + n2);\n    if (hasAlpha) tb.ac = d * (n1 * tb.ac + n2 * nb.ac);\n    tb.rc = d * (n1 * tb.rc + n2 * nb.rc);\n    tb.gc = d * (n1 * tb.gc + n2 * nb.gc);\n    tb.bc = d * (n1 * tb.bc + n2 * nb.bc);\n    tb.cnt += nb.cnt;\n    tb.mtm = ++i;\n\n    /* Unchain deleted bin */\n    bins[nb.bk].fw = nb.fw;\n    bins[nb.fw].bk = nb.bk;\n    nb.mtm = bincountMinusOne;\n  }\n\n  // let palette = new Uint32Array(maxColors);\n  let palette = [];\n\n  /* Fill palette */\n  var k = 0;\n  for (i = 0; ; ++k) {\n    let r = clamp(Math.round(bins[i].rc), 0, 0xff);\n    let g = clamp(Math.round(bins[i].gc), 0, 0xff);\n    let b = clamp(Math.round(bins[i].bc), 0, 0xff);\n\n    let a = 0xff;\n    if (hasAlpha) {\n      a = clamp(Math.round(bins[i].ac), 0, 0xff);\n      if (oneBitAlpha) {\n        const threshold = typeof oneBitAlpha === \"number\" ? oneBitAlpha : 127;\n        a = a <= threshold ? 0x00 : 0xff;\n      }\n      if (clearAlpha && a <= clearAlphaThreshold) {\n        r = g = b = clearAlphaColor;\n        a = 0x00;\n      }\n    }\n\n    const color = hasAlpha ? [r, g, b, a] : [r, g, b];\n    const exists = existsInPalette(palette, color);\n    if (!exists) palette.push(color);\n    if ((i = bins[i].fw) == 0) break;\n  }\n\n  return palette;\n}\n\nfunction existsInPalette(palette, color) {\n  for (let i = 0; i < palette.length; i++) {\n    const p = palette[i];\n    let matchesRGB =\n      p[0] === color[0] && p[1] === color[1] && p[2] === color[2];\n    let matchesAlpha =\n      p.length >= 4 && color.length >= 4 ? p[3] === color[3] : true;\n    if (matchesRGB && matchesAlpha) return true;\n  }\n  return false;\n}\n\n// TODO: Further 'clean' palette by merging nearly-identical colors?\n", "function rgb2y(r, g, b) {\n  return r * 0.29889531 + g * 0.58662247 + b * 0.11448223;\n}\nfunction rgb2i(r, g, b) {\n  return r * 0.59597799 - g * 0.2741761 - b * 0.32180189;\n}\nfunction rgb2q(r, g, b) {\n  return r * 0.21147017 - g * 0.52261711 + b * 0.31114694;\n}\n\nexport function colorDifferenceYIQSquared(yiqA, yiqB) {\n  const y = yiqA[0] - yiqB[0];\n  const i = yiqA[1] - yiqB[1];\n  const q = yiqA[2] - yiqB[2];\n  const a = alpha(yiqA) - alpha(yiqB);\n  return y * y * 0.5053 + i * i * 0.299 + q * q * 0.1957 + a * a;\n}\n\nfunction alpha(array) {\n  return array[3] != null ? array[3] : 0xff;\n}\n\nexport function colorDifferenceYIQ(yiqA, yiqB) {\n  return Math.sqrt(colorDifferenceYIQSquared(yiqA, yiqB));\n}\n\nexport function colorDifferenceRGBToYIQSquared(rgb1, rgb2) {\n  const [r1, g1, b1] = rgb1;\n  const [r2, g2, b2] = rgb2;\n  const y = rgb2y(r1, g1, b1) - rgb2y(r2, g2, b2),\n    i = rgb2i(r1, g1, b1) - rgb2i(r2, g2, b2),\n    q = rgb2q(r1, g1, b1) - rgb2q(r2, g2, b2);\n  const a = alpha(rgb1) - alpha(rgb2);\n  return y * y * 0.5053 + i * i * 0.299 + q * q * 0.1957 + a * a;\n}\n\nexport function colorDifferenceRGBToYIQ(rgb1, rgb2) {\n  return Math.sqrt(colorDifferenceRGBToYIQSquared(rgb1, rgb2));\n}\n\nexport function euclideanDistanceSquared(a, b) {\n  var sum = 0;\n  var n;\n  for (n = 0; n < a.length; n++) {\n    const dx = a[n] - b[n];\n    sum += dx * dx;\n  }\n  return sum;\n}\n\nexport function euclideanDistance(a, b) {\n  return Math.sqrt(euclideanDistanceSquared(a, b));\n}\n", "import {\n  rgb888_to_rgb444,\n  rgb888_to_rgb565,\n  rgba8888_to_rgba4444,\n} from \"./rgb-packing.js\";\n\nimport { euclideanDistanceSquared } from \"./color.js\";\n\nfunction roundStep(byte, step) {\n  return step > 1 ? Math.round(byte / step) * step : byte;\n}\n\nexport function prequantize(\n  rgba,\n  { roundRGB = 5, roundAlpha = 10, oneBitAlpha = null } = {}\n) {\n  const data = new Uint32Array(rgba.buffer);\n  for (let i = 0; i < data.length; i++) {\n    const color = data[i];\n    let a = (color >> 24) & 0xff;\n    let b = (color >> 16) & 0xff;\n    let g = (color >> 8) & 0xff;\n    let r = color & 0xff;\n\n    a = roundStep(a, roundAlpha);\n    if (oneBitAlpha) {\n      const threshold = typeof oneBitAlpha === \"number\" ? oneBitAlpha : 127;\n      a = a <= threshold ? 0x00 : 0xff;\n    }\n    r = roundStep(r, roundRGB);\n    g = roundStep(g, roundRGB);\n    b = roundStep(b, roundRGB);\n\n    data[i] = (a << 24) | (b << 16) | (g << 8) | (r << 0);\n  }\n}\n\nexport function applyPalette(rgba, palette, format = \"rgb565\") {\n  if (!rgba || !rgba.buffer) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  if (!(rgba instanceof Uint8Array) && !(rgba instanceof Uint8ClampedArray)) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  if (palette.length > 256) {\n    throw new Error('applyPalette() only works with 256 colors or less');\n  }\n\n  const data = new Uint32Array(rgba.buffer);\n  const length = data.length;\n  const bincount = format === \"rgb444\" ? 4096 : 65536;\n  const index = new Uint8Array(length);\n  const cache = new Array(bincount);\n  const hasAlpha = format === \"rgba4444\";\n\n  // Some duplicate code below due to very hot code path\n  // Introducing branching/conditions shows some significant impact\n  if (format === \"rgba4444\") {\n    for (let i = 0; i < length; i++) {\n      const color = data[i];\n      const a = (color >> 24) & 0xff;\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n      const key = rgba8888_to_rgba4444(r, g, b, a);\n      const idx = key in cache ? cache[key] : (cache[key] = nearestColorIndexRGBA(r, g, b, a, palette));\n      index[i] = idx;\n    }\n  } else {\n    const rgb888_to_key = format === \"rgb444\" ? rgb888_to_rgb444 : rgb888_to_rgb565;\n    for (let i = 0; i < length; i++) {\n      const color = data[i];\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n      const key = rgb888_to_key(r, g, b);\n      const idx = key in cache ? cache[key] : (cache[key] = nearestColorIndexRGB(r, g, b, palette));\n      index[i] = idx;\n    }\n  }\n\n  return index;\n}\n\nfunction nearestColorIndexRGBA(r, g, b, a, palette) {\n  let k = 0;\n  let mindist = 1e100;\n  for (let i = 0; i < palette.length; i++) {\n    const px2 = palette[i];\n    const a2 = px2[3];\n    let curdist = sqr(a2 - a);\n    if (curdist > mindist) continue;\n    const r2 = px2[0];\n    curdist += sqr(r2 - r);\n    if (curdist > mindist) continue;\n    const g2 = px2[1];\n    curdist += sqr(g2 - g);\n    if (curdist > mindist) continue;\n    const b2 = px2[2];\n    curdist += sqr(b2 - b);\n    if (curdist > mindist) continue;\n    mindist = curdist;\n    k = i;\n  }\n  return k;\n}\n\nfunction nearestColorIndexRGB(r, g, b, palette) {\n  let k = 0;\n  let mindist = 1e100;\n  for (let i = 0; i < palette.length; i++) {\n    const px2 = palette[i];\n    const r2 = px2[0];\n    let curdist = sqr(r2 - r);\n    if (curdist > mindist) continue;\n    const g2 = px2[1];\n    curdist += sqr(g2 - g);\n    if (curdist > mindist) continue;\n    const b2 = px2[2];\n    curdist += sqr(b2 - b);\n    if (curdist > mindist) continue;\n    mindist = curdist;\n    k = i;\n  }\n  return k;\n}\n\nexport function snapColorsToPalette(palette, knownColors, threshold = 5) {\n  if (!palette.length || !knownColors.length) return;\n\n  const paletteRGB = palette.map((p) => p.slice(0, 3));\n  const thresholdSq = threshold * threshold;\n  const dim = palette[0].length;\n  for (let i = 0; i < knownColors.length; i++) {\n    let color = knownColors[i];\n    if (color.length < dim) {\n      // palette is RGBA, known is RGB\n      color = [color[0], color[1], color[2], 0xff];\n    } else if (color.length > dim) {\n      // palette is RGB, known is RGBA\n      color = color.slice(0, 3);\n    } else {\n      // make sure we always copy known colors\n      color = color.slice();\n    }\n    const r = nearestColorIndexWithDistance(\n      paletteRGB,\n      color.slice(0, 3),\n      euclideanDistanceSquared\n    );\n    const idx = r[0];\n    const distanceSq = r[1];\n    if (distanceSq > 0 && distanceSq <= thresholdSq) {\n      palette[idx] = color;\n    }\n  }\n}\n\nfunction sqr(a) {\n  return a * a;\n}\n\nexport function nearestColorIndex(\n  colors,\n  pixel,\n  distanceFn = euclideanDistanceSquared\n) {\n  let minDist = Infinity;\n  let minDistIndex = -1;\n  for (let j = 0; j < colors.length; j++) {\n    const paletteColor = colors[j];\n    const dist = distanceFn(pixel, paletteColor);\n    if (dist < minDist) {\n      minDist = dist;\n      minDistIndex = j;\n    }\n  }\n  return minDistIndex;\n}\n\nexport function nearestColorIndexWithDistance(\n  colors,\n  pixel,\n  distanceFn = euclideanDistanceSquared\n) {\n  let minDist = Infinity;\n  let minDistIndex = -1;\n  for (let j = 0; j < colors.length; j++) {\n    const paletteColor = colors[j];\n    const dist = distanceFn(pixel, paletteColor);\n    if (dist < minDist) {\n      minDist = dist;\n      minDistIndex = j;\n    }\n  }\n  return [minDistIndex, minDist];\n}\n\nexport function nearestColor(\n  colors,\n  pixel,\n  distanceFn = euclideanDistanceSquared\n) {\n  return colors[nearestColorIndex(colors, pixel, distanceFn)];\n}\n", "import constants from \"./constants.js\";\nimport lzwEncode from \"./lzwEncode.js\";\nimport createStream from \"./stream.js\";\nimport quantize from \"./pnnquant2.js\";\n\nimport {\n  prequantize,\n  applyPalette,\n  nearestColorIndex,\n  nearestColor,\n  nearestColorIndexWithDistance,\n  snapColorsToPalette,\n} from \"./palettize.js\";\n\nfunction GIFEncoder(opt = {}) {\n  const { initialCapacity = 4096, auto = true } = opt;\n\n  // Stream all encoded data into this buffer\n  const stream = createStream(initialCapacity);\n\n  // Shared array data across all frames\n  const HSIZE = 5003; // 80% occupancy\n  const accum = new Uint8Array(256);\n  const htab = new Int32Array(HSIZE);\n  const codetab = new Int32Array(HSIZE);\n\n  let hasInit = false;\n\n  return {\n    reset() {\n      stream.reset();\n      hasInit = false;\n    },\n    finish() {\n      stream.writeByte(constants.trailer);\n    },\n    bytes() {\n      return stream.bytes();\n    },\n    bytesView() {\n      return stream.bytesView();\n    },\n    get buffer() {\n      return stream.buffer;\n    },\n    get stream() {\n      return stream;\n    },\n    writeHeader,\n    writeFrame(index, width, height, opts = {}) {\n      const {\n        transparent = false,\n        transparentIndex = 0x00,\n        delay = 0,\n        palette = null,\n        repeat = 0, // -1=once, 0=forever, >0=count\n        colorDepth = 8,\n        dispose = -1,\n      } = opts;\n\n      let first = false;\n      if (auto) {\n        // In 'auto' mode, the first time we write a frame\n        // we will write LSD/GCT/EXT\n        if (!hasInit) {\n          // have not yet init, we can consider this our first frame\n          first = true;\n          // in 'auto' mode, we also encode a header on first frame\n          // this is different than manual mode where you must encode\n          // header yoursef (or perhaps not write header altogether)\n          writeHeader();\n          hasInit = true;\n        }\n      } else {\n        // in manual mode, the first frame is determined by the options only\n        first = Boolean(opts.first);\n      }\n\n      width = Math.max(0, Math.floor(width));\n      height = Math.max(0, Math.floor(height));\n\n      // Write pre-frame details such as repeat count and global palette\n      if (first) {\n        if (!palette) {\n          throw new Error(\"First frame must include a { palette } option\");\n        }\n        encodeLogicalScreenDescriptor(\n          stream,\n          width,\n          height,\n          palette,\n          colorDepth\n        );\n        encodeColorTable(stream, palette);\n        if (repeat >= 0) {\n          encodeNetscapeExt(stream, repeat);\n        }\n      }\n\n      const delayTime = Math.round(delay / 10);\n      encodeGraphicControlExt(\n        stream,\n        dispose,\n        delayTime,\n        transparent,\n        transparentIndex\n      );\n\n      const useLocalColorTable = Boolean(palette) && !first;\n      encodeImageDescriptor(\n        stream,\n        width,\n        height,\n        useLocalColorTable ? palette : null\n      );\n      if (useLocalColorTable) encodeColorTable(stream, palette);\n      encodePixels(\n        stream,\n        index,\n        width,\n        height,\n        colorDepth,\n        accum,\n        htab,\n        codetab\n      );\n    },\n  };\n\n  function writeHeader() {\n    writeUTFBytes(stream, \"GIF89a\");\n  }\n}\n\nfunction encodeGraphicControlExt(\n  stream,\n  dispose,\n  delay,\n  transparent,\n  transparentIndex\n) {\n  stream.writeByte(0x21); // extension introducer\n  stream.writeByte(0xf9); // GCE label\n  stream.writeByte(4); // data block size\n\n  if (transparentIndex < 0) {\n    transparentIndex = 0x00;\n    transparent = false;\n  }\n\n  var transp, disp;\n  if (!transparent) {\n    transp = 0;\n    disp = 0; // dispose = no action\n  } else {\n    transp = 1;\n    disp = 2; // force clear if using transparent color\n  }\n\n  if (dispose >= 0) {\n    disp = dispose & 7; // user override\n  }\n\n  disp <<= 2;\n\n  const userInput = 0;\n\n  // packed fields\n  stream.writeByte(\n    0 | // 1:3 reserved\n      disp | // 4:6 disposal\n      userInput | // 7 user input - 0 = none\n      transp // 8 transparency flag\n  );\n\n  writeUInt16(stream, delay); // delay x 1/100 sec\n  stream.writeByte(transparentIndex || 0x00); // transparent color index\n  stream.writeByte(0); // block terminator\n}\n\nfunction encodeLogicalScreenDescriptor(\n  stream,\n  width,\n  height,\n  palette,\n  colorDepth = 8\n) {\n  const globalColorTableFlag = 1;\n  const sortFlag = 0;\n  const globalColorTableSize = colorTableSize(palette.length) - 1;\n  const fields =\n    (globalColorTableFlag << 7) |\n    ((colorDepth - 1) << 4) |\n    (sortFlag << 3) |\n    globalColorTableSize;\n  const backgroundColorIndex = 0;\n  const pixelAspectRatio = 0;\n  writeUInt16(stream, width);\n  writeUInt16(stream, height);\n  stream.writeBytes([fields, backgroundColorIndex, pixelAspectRatio]);\n}\n\nfunction encodeNetscapeExt(stream, repeat) {\n  stream.writeByte(0x21); // extension introducer\n  stream.writeByte(0xff); // app extension label\n  stream.writeByte(11); // block size\n  writeUTFBytes(stream, \"NETSCAPE2.0\"); // app id + auth code\n  stream.writeByte(3); // sub-block size\n  stream.writeByte(1); // loop sub-block id\n  writeUInt16(stream, repeat); // loop count (extra iterations, 0=repeat forever)\n  stream.writeByte(0); // block terminator\n}\n\nfunction encodeColorTable(stream, palette) {\n  const colorTableLength = 1 << colorTableSize(palette.length);\n  for (let i = 0; i < colorTableLength; i++) {\n    let color = [0, 0, 0];\n    if (i < palette.length) {\n      color = palette[i];\n    }\n    stream.writeByte(color[0]);\n    stream.writeByte(color[1]);\n    stream.writeByte(color[2]);\n  }\n}\n\nfunction encodeImageDescriptor(stream, width, height, localPalette) {\n  stream.writeByte(0x2c); // image separator\n\n  writeUInt16(stream, 0); // x position\n  writeUInt16(stream, 0); // y position\n  writeUInt16(stream, width); // image size\n  writeUInt16(stream, height);\n\n  if (localPalette) {\n    const interlace = 0;\n    const sorted = 0;\n    const palSize = colorTableSize(localPalette.length) - 1;\n    // local palette\n    stream.writeByte(\n      0x80 | // 1 local color table 1=yes\n        interlace | // 2 interlace - 0=no\n        sorted | // 3 sorted - 0=no\n        0 | // 4-5 reserved\n        palSize // 6-8 size of color table\n    );\n  } else {\n    // global palette\n    stream.writeByte(0);\n  }\n}\n\nfunction encodePixels(\n  stream,\n  index,\n  width,\n  height,\n  colorDepth = 8,\n  accum,\n  htab,\n  codetab\n) {\n  lzwEncode(width, height, index, colorDepth, stream, accum, htab, codetab);\n}\n\n// Utilities\n\nfunction writeUInt16(stream, short) {\n  stream.writeByte(short & 0xff);\n  stream.writeByte((short >> 8) & 0xff);\n}\n\nfunction writeUTFBytes(stream, text) {\n  for (var i = 0; i < text.length; i++) {\n    stream.writeByte(text.charCodeAt(i));\n  }\n}\n\nfunction colorTableSize(length) {\n  return Math.max(Math.ceil(Math.log2(length)), 1);\n}\n\nexport {\n  GIFEncoder,\n  quantize,\n  prequantize,\n  applyPalette,\n  nearestColorIndex,\n  nearestColor,\n  nearestColorIndexWithDistance,\n  snapColorsToPalette,\n};\n\nexport default GIFEncoder;\n"], "mappings": "AAAA,GAAO,GAAQ,CACb,UAAW,MACX,QAAS,MACT,QAAS,GACT,oBAAqB,GACrB,0BAA2B,IAC3B,6BAA8B,IAC9B,eAAgB,GAEhB,cAAe,EACf,YAAa,EACb,yBAA0B,IAC1B,oBAAqB,IACrB,aAAc,EACd,yBAA0B,EAE1B,0BAA2B,EAC3B,wBAAyB,EAEzB,mBAAoB,GACpB,kBAAmB,EACnB,yBAA0B,EAE1B,wBAAyB,IACzB,kBAAmB,GACnB,eAAgB,GAChB,wBAAyB,GC1BZ,WAAsB,EAAkB,IAAK,CAC1D,GAAI,GAAS,EACT,EAAW,GAAI,YAAW,GAE9B,MAAO,IACD,SAAS,CACX,MAAO,GAAS,QAElB,OAAQ,CACN,EAAS,GAEX,WAAY,CACV,MAAO,GAAS,SAAS,EAAG,IAE9B,OAAQ,CACN,MAAO,GAAS,MAAM,EAAG,IAE3B,UAAU,EAAM,CACd,EAAO,EAAS,GAChB,EAAS,GAAU,EACnB,KAEF,WAAW,EAAM,EAAS,EAAG,EAAa,EAAK,OAAQ,CACrD,EAAO,EAAS,GAChB,OAAS,GAAI,EAAG,EAAI,EAAY,IAC9B,EAAS,KAAY,EAAK,EAAI,IAGlC,eAAe,EAAM,EAAS,EAAG,EAAa,EAAK,WAAY,CAC7D,EAAO,EAAS,GAChB,EAAS,IAAI,EAAK,SAAS,EAAQ,EAAS,GAAa,GACzD,GAAU,IAId,WAAgB,EAAa,CAC3B,GAAI,GAAe,EAAS,OAC5B,GAAI,GAAgB,EAAa,OAIjC,GAAI,GAAwB,KAAO,KACnC,EAAc,KAAK,IACjB,EACC,EAAgB,GAAe,EAAwB,EAAM,SAC5D,GAEA,GAAgB,GAAG,GAAc,KAAK,IAAI,EAAa,MAC3D,GAAM,GAAc,EACpB,EAAW,GAAI,YAAW,GACtB,EAAS,GAAG,EAAS,IAAI,EAAY,SAAS,EAAG,GAAS,ICzBlE,GAAM,GAAO,GACP,EAAgB,KAChB,GAAQ,CACZ,EACA,EACA,EACA,EACA,GACA,GACA,GACA,IACA,IACA,IACA,KACA,KACA,KACA,KACA,MACA,MACA,OAGF,YACE,EACA,EACA,EACA,EACA,EAAY,EAAa,KACzB,EAAQ,GAAI,YAAW,KACvB,EAAO,GAAI,YAAW,GACtB,EAAU,GAAI,YAAW,GACzB,CACA,GAAM,GAAQ,EAAK,OACb,EAAe,KAAK,IAAI,EAAG,GAEjC,EAAM,KAAK,GACX,EAAQ,KAAK,GACb,EAAK,KAAK,IAEV,GAAI,GAAY,EACZ,EAAW,EAeT,EAAY,EAAe,EAG3B,EAAc,EAMhB,EAAY,GACZ,EAAS,EACT,EAAW,IAAK,GAAU,EAExB,EAAY,GAAM,EAAY,EAC9B,EAAU,EAAY,EACxB,EAAW,EAAY,EACvB,EAAU,EAEV,EAAM,EAAO,GAEb,EAAS,EACb,OAAS,GAAQ,EAAO,EAAQ,MAAO,GAAS,EAC9C,EAAE,EAEJ,EAAS,EAAI,EAEb,EAAU,UAAU,GAEpB,EAAO,GAEP,GAAM,GAAS,EAAO,OACtB,OAAS,GAAM,EAAG,EAAM,EAAQ,IAAO,CACrC,EAAY,CACV,GAAM,GAAI,EAAO,GACX,EAAS,IAAK,GAAQ,EACxB,EAAK,GAAK,EAAU,EACxB,GAAI,EAAK,KAAO,EAAO,CACrB,EAAM,EAAQ,GACd,QAGF,GAAM,GAAO,IAAM,EAAI,EAAI,EAAQ,EACnC,KAAO,EAAK,IAAM,GAIhB,GAFA,GAAK,EACD,EAAI,GAAG,IAAK,GACZ,EAAK,KAAO,EAAO,CACrB,EAAM,EAAQ,GACd,QAGJ,EAAO,GACP,EAAM,EACN,AAAI,EAAW,GAAK,EAClB,GAAQ,GAAK,IACb,EAAK,GAAK,GAIV,GAAK,KAAK,IACV,EAAW,EAAY,EACvB,EAAY,GACZ,EAAO,KAMb,SAAO,GACP,EAAO,GAEP,EAAU,UAAU,GACb,EAAU,YAEjB,WAAgB,EAAM,CAQpB,IAPA,GAAa,GAAM,GAEnB,AAAI,EAAW,EAAG,GAAa,GAAQ,EAClC,EAAY,EAEjB,GAAY,EAEL,GAAY,GAGjB,EAAM,KAAa,EAAY,IAC3B,GAAW,KACb,GAAU,UAAU,GACpB,EAAU,eAAe,EAAO,EAAG,GACnC,EAAU,GAEZ,IAAc,EACd,GAAY,EAgBd,GAXI,GAAW,GAAW,IACxB,CAAI,EACF,GAAS,EACT,EAAW,IAAK,GAAU,EAC1B,EAAY,IAEZ,GAAE,EACF,EAAU,IAAW,EAAQ,GAAK,EAAW,IAAK,GAAU,IAI5D,GAAQ,EAAS,CAEnB,KAAO,EAAW,GAGhB,EAAM,KAAa,EAAY,IAC3B,GAAW,KACb,GAAU,UAAU,GACpB,EAAU,eAAe,EAAO,EAAG,GACnC,EAAU,GAEZ,IAAc,EACd,GAAY,EAGd,AAAI,EAAU,GACZ,GAAU,UAAU,GACpB,EAAU,eAAe,EAAO,EAAG,GACnC,EAAU,KAMlB,GAAO,GAAQ,GCxMR,WAA0B,EAAG,EAAG,EAAG,CACxC,MAAS,IAAK,EAAK,MAAY,GAAK,EAAK,IAAW,GAAK,EAGpD,WAA8B,EAAG,EAAG,EAAG,EAAG,CAC/C,MAAQ,IAAK,EAAM,EAAI,IAAU,GAAI,MAAS,EAAO,GAAI,MAAS,EAG7D,WAA0B,EAAG,EAAG,EAAG,CACxC,MAAS,IAAK,GAAM,EAAM,EAAI,IAAS,GAAK,ECP9C,WAAe,EAAO,EAAK,EAAK,CAC9B,MAAO,GAAQ,EAAM,EAAM,EAAQ,EAAM,EAAM,EAGjD,WAAa,EAAO,CAClB,MAAO,GAAQ,EAGjB,YAAiB,EAAM,EAAK,EAAU,CACpC,GAAI,GAAK,EACL,EAAM,MAEV,GAAM,GAAO,EAAK,GACZ,EAAK,EAAK,IACV,EAAK,EAAK,GACV,EAAK,EAAK,GACV,EAAK,EAAK,GACV,EAAK,EAAK,GAChB,OAAS,GAAI,EAAK,GAAI,GAAK,EAAG,EAAI,EAAK,GAAG,GAAI,CAC5C,GAAM,GAAM,EAAK,GACX,EAAK,EAAI,IACT,EAAS,EAAK,EAAO,GAAK,GAChC,GAAI,KAAS,GAEb,IAAI,GAAO,EACX,AAAI,GACF,IAAQ,EAAQ,EAAI,EAAI,GAAK,GACzB,GAAQ,IAGd,IAAQ,EAAQ,EAAI,EAAI,GAAK,GACzB,KAAQ,IAEZ,IAAQ,EAAQ,EAAI,EAAI,GAAK,GACzB,KAAQ,IAEZ,IAAQ,EAAQ,EAAI,EAAI,GAAK,GACzB,KAAQ,IACZ,GAAM,EACN,EAAK,OAEP,EAAK,IAAM,EACX,EAAK,GAAK,EAGZ,YAAsB,CACpB,MAAO,CACL,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,IAAK,EACL,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,IAAK,EACL,IAAK,GAWT,YAAyB,EAAM,EAAQ,CACrC,GAAM,GAAW,IAAW,SAAW,KAAO,MACxC,EAAO,GAAI,OAAM,GACjB,EAAO,EAAK,OAMlB,GAAI,IAAW,WACb,OAAS,GAAI,EAAG,EAAI,EAAM,EAAE,EAAG,CAC7B,GAAM,GAAQ,EAAK,GACb,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,EAAK,IACnB,EAAI,EAAQ,IAGZ,EAAQ,EAAqB,EAAG,EAAG,EAAG,GACxC,EAAM,IAAS,GAAO,EAAK,GAAU,EAAK,GAAS,IACvD,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,cAIC,IAAW,SAClB,OAAS,GAAI,EAAG,EAAI,EAAM,EAAE,EAAG,CAC7B,GAAM,GAAQ,EAAK,GACb,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,EAAK,IACnB,EAAI,EAAQ,IAGZ,EAAQ,EAAiB,EAAG,EAAG,GACjC,EAAM,IAAS,GAAO,EAAK,GAAU,EAAK,GAAS,IACvD,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,UAGN,QAAS,GAAI,EAAG,EAAI,EAAM,EAAE,EAAG,CAC7B,GAAM,GAAQ,EAAK,GACb,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,EAAK,IACnB,EAAI,EAAQ,IAGZ,EAAQ,EAAiB,EAAG,EAAG,GACjC,EAAM,IAAS,GAAO,EAAK,GAAU,EAAK,GAAS,IACvD,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,MAGR,MAAO,GAGM,WAAkB,EAAM,EAAW,EAAO,GAAI,CAC3D,GAAM,CACJ,SAAS,SACT,aAAa,GACb,kBAAkB,EAClB,sBAAsB,EACtB,cAAc,IACZ,EAEJ,GAAI,CAAC,GAAQ,CAAC,EAAK,OACjB,KAAM,IAAI,OAAM,4CAElB,GAAI,CAAE,aAAgB,cAAe,CAAE,aAAgB,oBACrD,KAAM,IAAI,OAAM,4CAGlB,GAAM,GAAO,GAAI,aAAY,EAAK,QAE9B,EAAU,EAAK,UAAY,GAOzB,EAAW,IAAW,WACtB,EAAO,GAAgB,EAAM,GAC7B,EAAW,EAAK,OAChB,EAAmB,EAAW,EAC9B,EAAO,GAAI,aAAY,EAAW,GAIxC,OADI,GAAU,EACL,EAAI,EAAG,EAAI,EAAU,EAAE,EAAG,CACjC,GAAM,GAAM,EAAK,GACjB,GAAI,GAAO,KAAM,CACf,GAAI,GAAI,EAAM,EAAI,IAClB,AAAI,GAAU,GAAI,IAAM,GACxB,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAI,IAAM,EACV,EAAK,KAAa,GAItB,AAAI,EAAI,GAAa,EAAU,MAC7B,GAAU,IAIZ,OADI,GAAI,EACD,EAAI,EAAU,EAAG,EAAE,EACxB,EAAK,GAAG,GAAK,EAAI,EACjB,EAAK,EAAI,GAAG,GAAK,EACb,GAAS,GAAK,GAAG,IAAM,KAAK,KAAK,EAAK,GAAG,MAE/C,AAAI,GAAS,GAAK,GAAG,IAAM,KAAK,KAAK,EAAK,GAAG,MAE7C,GAAI,GAAG,EAAG,EAEV,IAAK,EAAI,EAAG,EAAI,EAAS,EAAE,EAAG,CAC5B,GAAQ,EAAM,EAAG,IAEjB,GAAI,GAAM,EAAK,GAAG,IAClB,IAAK,EAAI,EAAE,EAAK,GAAI,EAAI,GACtB,GAAK,GAAK,EACN,IAAM,EAAI,EAAK,IAAM,KAAO,IAFP,EAAI,EAG7B,EAAK,GAAK,EAEZ,EAAK,GAAK,EAIZ,GAAI,GAAU,EAAU,EACxB,IAAK,EAAI,EAAG,EAAI,GAAW,CAGzB,OAFI,KAEK,CACP,GAAI,GAAK,EAAK,GAGd,GAFA,EAAK,EAAK,GAEN,EAAG,IAAM,EAAG,KAAO,EAAK,EAAG,IAAI,KAAO,EAAG,GAAI,MACjD,AAAI,EAAG,KAAO,EACO,EAAK,EAAK,GAAK,EAAK,EAAK,MAE5C,IAAQ,EAAM,EAAI,IAClB,EAAG,GAAK,GAGV,GAAI,GAAM,EAAK,GAAI,IACnB,IAAK,EAAI,EAAI,GAAK,EAAI,IAAM,EAAK,IAC3B,GAAK,EAAK,IAAM,EAAK,EAAK,IAAK,IAAM,EAAK,EAAK,EAAK,IAAI,KAAK,IAC7D,KAAO,EAAM,EAAI,EAAK,IAAM,MAFG,EAAI,EAGvC,EAAK,GAAK,EAEZ,EAAK,GAAK,EAIZ,GAAI,GAAK,EAAK,EAAG,IACb,EAAK,EAAG,IACR,EAAK,EAAG,IACR,EAAI,EAAO,GAAK,GACpB,AAAI,GAAU,GAAG,GAAK,EAAK,GAAK,EAAG,GAAK,EAAK,EAAG,KAChD,EAAG,GAAK,EAAK,GAAK,EAAG,GAAK,EAAK,EAAG,IAClC,EAAG,GAAK,EAAK,GAAK,EAAG,GAAK,EAAK,EAAG,IAClC,EAAG,GAAK,EAAK,GAAK,EAAG,GAAK,EAAK,EAAG,IAClC,EAAG,KAAO,EAAG,IACb,EAAG,IAAM,EAAE,EAGX,EAAK,EAAG,IAAI,GAAK,EAAG,GACpB,EAAK,EAAG,IAAI,GAAK,EAAG,GACpB,EAAG,IAAM,EAIX,GAAI,GAAU,GAGd,GAAI,GAAI,EACR,IAAK,EAAI,GAAK,EAAE,EAAG,CACjB,GAAI,GAAI,EAAM,KAAK,MAAM,EAAK,GAAG,IAAK,EAAG,KACrC,EAAI,EAAM,KAAK,MAAM,EAAK,GAAG,IAAK,EAAG,KACrC,EAAI,EAAM,KAAK,MAAM,EAAK,GAAG,IAAK,EAAG,KAErC,EAAI,IACR,GAAI,EAAU,CAEZ,GADA,EAAI,EAAM,KAAK,MAAM,EAAK,GAAG,IAAK,EAAG,KACjC,EAAa,CACf,GAAM,IAAY,MAAO,IAAgB,SAAW,EAAc,IAClE,EAAI,GAAK,GAAY,EAAO,IAE9B,AAAI,GAAc,GAAK,GACrB,GAAI,EAAI,EAAI,EACZ,EAAI,GAIR,GAAM,GAAQ,EAAW,CAAC,EAAG,EAAG,EAAG,GAAK,CAAC,EAAG,EAAG,GAG/C,GADK,AADU,GAAgB,EAAS,IAC3B,EAAQ,KAAK,GACrB,GAAI,EAAK,GAAG,KAAO,EAAG,MAG7B,MAAO,GAGT,YAAyB,EAAS,EAAO,CACvC,OAAS,GAAI,EAAG,EAAI,EAAQ,OAAQ,IAAK,CACvC,GAAM,GAAI,EAAQ,GACd,EACF,EAAE,KAAO,EAAM,IAAM,EAAE,KAAO,EAAM,IAAM,EAAE,KAAO,EAAM,GACvD,EACF,EAAE,QAAU,GAAK,EAAM,QAAU,EAAI,EAAE,KAAO,EAAM,GAAK,GAC3D,GAAI,GAAc,EAAc,MAAO,GAEzC,MAAO,GCpQF,WAAkC,EAAG,EAAG,CAC7C,GAAI,GAAM,EACN,EACJ,IAAK,EAAI,EAAG,EAAI,EAAE,OAAQ,IAAK,CAC7B,GAAM,GAAK,EAAE,GAAK,EAAE,GACpB,GAAO,EAAK,EAEd,MAAO,GCvCT,WAAmB,EAAM,EAAM,CAC7B,MAAO,GAAO,EAAI,KAAK,MAAM,EAAO,GAAQ,EAAO,EAG9C,YACL,EACA,CAAE,WAAW,EAAG,aAAa,GAAI,cAAc,MAAS,GACxD,CACA,GAAM,GAAO,GAAI,aAAY,EAAK,QAClC,OAAS,GAAI,EAAG,EAAI,EAAK,OAAQ,IAAK,CACpC,GAAM,GAAQ,EAAK,GACf,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,EAAK,IACnB,EAAI,EAAQ,IAGhB,GADA,EAAI,EAAU,EAAG,GACb,EAAa,CACf,GAAM,GAAY,MAAO,IAAgB,SAAW,EAAc,IAClE,EAAI,GAAK,EAAY,EAAO,IAE9B,EAAI,EAAU,EAAG,GACjB,EAAI,EAAU,EAAG,GACjB,EAAI,EAAU,EAAG,GAEjB,EAAK,GAAM,GAAK,GAAO,GAAK,GAAO,GAAK,EAAM,GAAK,GAIhD,YAAsB,EAAM,EAAS,EAAS,SAAU,CAC7D,GAAI,CAAC,GAAQ,CAAC,EAAK,OACjB,KAAM,IAAI,OAAM,4CAElB,GAAI,CAAE,aAAgB,cAAe,CAAE,aAAgB,oBACrD,KAAM,IAAI,OAAM,4CAElB,GAAI,EAAQ,OAAS,IACnB,KAAM,IAAI,OAAM,qDAGlB,GAAM,GAAO,GAAI,aAAY,EAAK,QAC5B,EAAS,EAAK,OACd,EAAW,IAAW,SAAW,KAAO,MACxC,EAAQ,GAAI,YAAW,GACvB,EAAQ,GAAI,OAAM,GAClB,EAAW,IAAW,WAI5B,GAAI,IAAW,WACb,OAAS,GAAI,EAAG,EAAI,EAAQ,IAAK,CAC/B,GAAM,GAAQ,EAAK,GACb,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,EAAK,IACnB,EAAI,EAAQ,IACZ,EAAM,EAAqB,EAAG,EAAG,EAAG,GACpC,EAAM,IAAO,GAAQ,EAAM,GAAQ,EAAM,GAAO,GAAsB,EAAG,EAAG,EAAG,EAAG,GACxF,EAAM,GAAK,MAER,CACL,GAAM,GAAgB,IAAW,SAAW,EAAmB,EAC/D,OAAS,GAAI,EAAG,EAAI,EAAQ,IAAK,CAC/B,GAAM,GAAQ,EAAK,GACb,EAAK,GAAS,GAAM,IACpB,EAAK,GAAS,EAAK,IACnB,EAAI,EAAQ,IACZ,EAAM,EAAc,EAAG,EAAG,GAC1B,EAAM,IAAO,GAAQ,EAAM,GAAQ,EAAM,GAAO,GAAqB,EAAG,EAAG,EAAG,GACpF,EAAM,GAAK,GAIf,MAAO,GAGT,YAA+B,EAAG,EAAG,EAAG,EAAG,EAAS,CAClD,GAAI,GAAI,EACJ,EAAU,MACd,OAAS,GAAI,EAAG,EAAI,EAAQ,OAAQ,IAAK,CACvC,GAAM,GAAM,EAAQ,GACd,EAAK,EAAI,GACX,EAAU,EAAI,EAAK,GACvB,GAAI,EAAU,EAAS,SACvB,GAAM,GAAK,EAAI,GAEf,GADA,GAAW,EAAI,EAAK,GAChB,EAAU,EAAS,SACvB,GAAM,GAAK,EAAI,GAEf,GADA,GAAW,EAAI,EAAK,GAChB,EAAU,EAAS,SACvB,GAAM,GAAK,EAAI,GAEf,AADA,GAAW,EAAI,EAAK,GAChB,IAAU,IACd,GAAU,EACV,EAAI,GAEN,MAAO,GAGT,YAA8B,EAAG,EAAG,EAAG,EAAS,CAC9C,GAAI,GAAI,EACJ,EAAU,MACd,OAAS,GAAI,EAAG,EAAI,EAAQ,OAAQ,IAAK,CACvC,GAAM,GAAM,EAAQ,GACd,EAAK,EAAI,GACX,EAAU,EAAI,EAAK,GACvB,GAAI,EAAU,EAAS,SACvB,GAAM,GAAK,EAAI,GAEf,GADA,GAAW,EAAI,EAAK,GAChB,EAAU,EAAS,SACvB,GAAM,GAAK,EAAI,GAEf,AADA,GAAW,EAAI,EAAK,GAChB,IAAU,IACd,GAAU,EACV,EAAI,GAEN,MAAO,GAGF,YAA6B,EAAS,EAAa,EAAY,EAAG,CACvE,GAAI,CAAC,EAAQ,QAAU,CAAC,EAAY,OAAQ,OAE5C,GAAM,GAAa,EAAQ,IAAI,AAAC,GAAM,EAAE,MAAM,EAAG,IAC3C,EAAc,EAAY,EAC1B,EAAM,EAAQ,GAAG,OACvB,OAAS,GAAI,EAAG,EAAI,EAAY,OAAQ,IAAK,CAC3C,GAAI,GAAQ,EAAY,GACxB,AAAI,EAAM,OAAS,EAEjB,EAAQ,CAAC,EAAM,GAAI,EAAM,GAAI,EAAM,GAAI,KAClC,AAAI,EAAM,OAAS,EAExB,EAAQ,EAAM,MAAM,EAAG,GAGvB,EAAQ,EAAM,QAEhB,GAAM,GAAI,EACR,EACA,EAAM,MAAM,EAAG,GACf,GAEI,EAAM,EAAE,GACR,EAAa,EAAE,GACrB,AAAI,EAAa,GAAK,GAAc,GAClC,GAAQ,GAAO,IAKrB,WAAa,EAAG,CACd,MAAO,GAAI,EAGN,WACL,EACA,EACA,EAAa,EACb,CACA,GAAI,GAAU,SACV,EAAe,GACnB,OAAS,GAAI,EAAG,EAAI,EAAO,OAAQ,IAAK,CACtC,GAAM,GAAe,EAAO,GACtB,EAAO,EAAW,EAAO,GAC/B,AAAI,EAAO,GACT,GAAU,EACV,EAAe,GAGnB,MAAO,GAGF,WACL,EACA,EACA,EAAa,EACb,CACA,GAAI,GAAU,SACV,EAAe,GACnB,OAAS,GAAI,EAAG,EAAI,EAAO,OAAQ,IAAK,CACtC,GAAM,GAAe,EAAO,GACtB,EAAO,EAAW,EAAO,GAC/B,AAAI,EAAO,GACT,GAAU,EACV,EAAe,GAGnB,MAAO,CAAC,EAAc,GAGjB,YACL,EACA,EACA,EAAa,EACb,CACA,MAAO,GAAO,EAAkB,EAAQ,EAAO,IC7LjD,YAAoB,EAAM,GAAI,CAC5B,GAAM,CAAE,kBAAkB,KAAM,OAAO,IAAS,EAG1C,EAAS,EAAa,GAGtB,EAAQ,KACR,EAAQ,GAAI,YAAW,KACvB,EAAO,GAAI,YAAW,GACtB,EAAU,GAAI,YAAW,GAE3B,EAAU,GAEd,MAAO,CACL,OAAQ,CACN,EAAO,QACP,EAAU,IAEZ,QAAS,CACP,EAAO,UAAU,EAAU,UAE7B,OAAQ,CACN,MAAO,GAAO,SAEhB,WAAY,CACV,MAAO,GAAO,gBAEZ,SAAS,CACX,MAAO,GAAO,WAEZ,SAAS,CACX,MAAO,IAET,cACA,WAAW,EAAO,EAAO,EAAQ,EAAO,GAAI,CAC1C,GAAM,CACJ,cAAc,GACd,mBAAmB,EACnB,QAAQ,EACR,UAAU,KACV,SAAS,EACT,aAAa,EACb,UAAU,IACR,EAEA,EAAQ,GAsBZ,GArBA,AAAI,EAGG,GAEH,GAAQ,GAIR,IACA,EAAU,IAIZ,EAAQ,QAAQ,EAAK,OAGvB,EAAQ,KAAK,IAAI,EAAG,KAAK,MAAM,IAC/B,EAAS,KAAK,IAAI,EAAG,KAAK,MAAM,IAG5B,EAAO,CACT,GAAI,CAAC,EACH,KAAM,IAAI,OAAM,iDAElB,GACE,EACA,EACA,EACA,EACA,GAEF,GAAiB,EAAQ,GACrB,GAAU,GACZ,GAAkB,EAAQ,GAI9B,GAAM,GAAY,KAAK,MAAM,EAAQ,IACrC,GACE,EACA,EACA,EACA,EACA,GAGF,GAAM,GAAqB,QAAQ,IAAY,CAAC,EAChD,GACE,EACA,EACA,EACA,EAAqB,EAAU,MAE7B,GAAoB,GAAiB,EAAQ,GACjD,GACE,EACA,EACA,EACA,EACA,EACA,EACA,EACA,KAKN,YAAuB,CACrB,GAAc,EAAQ,WAI1B,YACE,EACA,EACA,EACA,EACA,EACA,CACA,EAAO,UAAU,IACjB,EAAO,UAAU,KACjB,EAAO,UAAU,GAEb,EAAmB,GACrB,GAAmB,EACnB,EAAc,IAGhB,GAAI,GAAQ,EACZ,AAAK,EAIH,GAAS,EACT,EAAO,GAJP,GAAS,EACT,EAAO,GAML,GAAW,GACb,GAAO,EAAU,GAGnB,IAAS,EAET,GAAM,GAAY,EAGlB,EAAO,UACL,EACE,EACA,EACA,GAGJ,EAAY,EAAQ,GACpB,EAAO,UAAU,GAAoB,GACrC,EAAO,UAAU,GAGnB,YACE,EACA,EACA,EACA,EACA,EAAa,EACb,CACA,GAAM,GAAuB,EACvB,EAAW,EACX,EAAuB,EAAe,EAAQ,QAAU,EACxD,EACH,GAAwB,EACvB,EAAa,GAAM,EACpB,GAAY,EACb,EACI,EAAuB,EACvB,EAAmB,EACzB,EAAY,EAAQ,GACpB,EAAY,EAAQ,GACpB,EAAO,WAAW,CAAC,EAAQ,EAAsB,IAGnD,YAA2B,EAAQ,EAAQ,CACzC,EAAO,UAAU,IACjB,EAAO,UAAU,KACjB,EAAO,UAAU,IACjB,GAAc,EAAQ,eACtB,EAAO,UAAU,GACjB,EAAO,UAAU,GACjB,EAAY,EAAQ,GACpB,EAAO,UAAU,GAGnB,YAA0B,EAAQ,EAAS,CACzC,GAAM,GAAmB,GAAK,EAAe,EAAQ,QACrD,OAAS,GAAI,EAAG,EAAI,EAAkB,IAAK,CACzC,GAAI,GAAQ,CAAC,EAAG,EAAG,GACnB,AAAI,EAAI,EAAQ,QACd,GAAQ,EAAQ,IAElB,EAAO,UAAU,EAAM,IACvB,EAAO,UAAU,EAAM,IACvB,EAAO,UAAU,EAAM,KAI3B,YAA+B,EAAQ,EAAO,EAAQ,EAAc,CAQlE,GAPA,EAAO,UAAU,IAEjB,EAAY,EAAQ,GACpB,EAAY,EAAQ,GACpB,EAAY,EAAQ,GACpB,EAAY,EAAQ,GAEhB,EAAc,CAChB,GAAM,GAAY,EACZ,EAAS,EACT,EAAU,EAAe,EAAa,QAAU,EAEtD,EAAO,UACL,IACE,EACA,EACA,EACA,OAIJ,GAAO,UAAU,GAIrB,YACE,EACA,EACA,EACA,EACA,EAAa,EACb,EACA,EACA,EACA,CACA,EAAU,EAAO,EAAQ,EAAO,EAAY,EAAQ,EAAO,EAAM,GAKnE,WAAqB,EAAQ,EAAO,CAClC,EAAO,UAAU,EAAQ,KACzB,EAAO,UAAW,GAAS,EAAK,KAGlC,YAAuB,EAAQ,EAAM,CACnC,OAAS,GAAI,EAAG,EAAI,EAAK,OAAQ,IAC/B,EAAO,UAAU,EAAK,WAAW,IAIrC,WAAwB,EAAQ,CAC9B,MAAO,MAAK,IAAI,KAAK,KAAK,KAAK,KAAK,IAAU,GAchD,GAAO,IAAQ", "names": []}