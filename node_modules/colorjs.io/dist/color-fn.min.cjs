"use strict";function e(e,t){let r=e.length;Array.isArray(e[0])||(e=[e]),Array.isArray(t[0])||(t=t.map((e=>[e])));let a=t[0].length,o=t[0].map(((e,r)=>t.map((e=>e[r])))),n=e.map((e=>o.map((t=>{let r=0;if(!Array.isArray(e)){for(let a of t)r+=e*a;return r}for(let a=0;a<e.length;a++)r+=e[a]*(t[a]||0);return r}))));return 1===r&&(n=n[0]),1===a?n.map((e=>e[0])):n}function t(e){return"string"===r(e)}function r(e){return(Object.prototype.toString.call(e).match(/^\[object\s+(.*?)\]$/)[1]||"").toLowerCase()}function a(e,{precision:t,unit:r}){return o(e)?"none":function(e,t){if(0===e)return 0;let r=~~e,a=0;r&&t&&(a=1+~~Math.log10(Math.abs(r)));const o=10**(t-a);return Math.floor(e*o+.5)/o}(e,t)+(r??"")}function o(e){return Number.isNaN(e)||e instanceof Number&&e?.none}function n(e){return o(e)?0:e}const s={deg:1,grad:.9,rad:180/Math.PI,turn:360};function i(e,t,r){return isNaN(e)?t:isNaN(t)?e:e+(t-e)*r}function c(e,t,r){return i(t[0],t[1],function(e,t,r){return(r-e)/(t-e)}(e[0],e[1],r))}function l(e,t){return Math.sign(e)===Math.sign(t)?e:-e}function u(e,t){return l(Math.abs(e)**t,e)}function h(e,t){return 0===t?0:e/t}class p{add(e,t,r){if("string"==typeof arguments[0])(Array.isArray(e)?e:[e]).forEach((function(e){this[e]=this[e]||[],t&&this[e][r?"unshift":"push"](t)}),this);else for(var e in arguments[0])this.add(e,arguments[0][e],arguments[1])}run(e,t){this[e]=this[e]||[],this[e].forEach((function(e){e.call(t&&t.context?t.context:t,t)}))}}const m=new p,d={D50:[.3457/.3585,1,.2958/.3585],D65:[.3127/.329,1,.3583/.329]};function f(e){return Array.isArray(e)?e:d[e]}function g(t,r,a,o={}){if(t=f(t),r=f(r),!t||!r)throw new TypeError(`Missing white point to convert ${t?"":"from"}${t||r?"":"/"}${r?"":"to"}`);if(t===r)return a;let n={W1:t,W2:r,XYZ:a,options:o};if(m.run("chromatic-adaptation-start",n),n.M||(n.W1===d.D65&&n.W2===d.D50?n.M=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]]:n.W1===d.D50&&n.W2===d.D65&&(n.M=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]])),m.run("chromatic-adaptation-end",n),n.M)return e(n.M,n.XYZ);throw new TypeError("Only Bradford CAT with white points D50 and D65 supported for now.")}var b={gamut_mapping:"css",precision:5,deltaE:"76",verbose:"test"!==globalThis?.process?.env?.NODE_ENV?.toLowerCase(),warn:function(e){this.verbose&&globalThis?.console?.warn?.(e)}};const M=new Set(["<number>","<percentage>","<angle>"]);function w(e,t,r,a){let o=Object.entries(e.coords).map((([e,o],n)=>{let s,i=t.coordGrammar[n],l=a[n],u=l?.type;if(s=l.none?i.find((e=>M.has(e))):i.find((e=>e==u)),!s){let t=o.name||e;throw new TypeError(`${u??l.raw} not allowed for ${t} in ${r}()`)}let h=s.range;"<percentage>"===u&&(h||=[0,1]);let p=o.range||o.refRange;return h&&p&&(a[n]=c(h,p,a[n])),s}));return o}function y(e,{meta:t}={}){let r={str:String(e)?.trim()};if(m.run("parse-start",r),r.color)return r.color;if(r.parsed=function(e){if(!e)return;e=e.trim();const t=/^-?[\d.]+$/,r=/%|deg|g?rad|turn$/,a=/\/?\s*(none|[-\w.]+(?:%|deg|g?rad|turn)?)/g;let o=e.match(/^([a-z]+)\((.+?)\)$/i);if(o){let e=[];return o[2].replace(a,((a,o)=>{let n=o.match(r),i=o;if(n){let e=n[0],t=i.slice(0,-e.length);"%"===e?(i=new Number(t/100),i.type="<percentage>"):(i=new Number(t*s[e]),i.type="<angle>",i.unit=e)}else t.test(i)?(i=new Number(i),i.type="<number>"):"none"===i&&(i=new Number(NaN),i.none=!0);a.startsWith("/")&&(i=i instanceof Number?i:new Number(i),i.alpha=!0),"object"==typeof i&&i instanceof Number&&(i.raw=o),e.push(i)})),{name:o[1].toLowerCase(),rawName:o[1],rawArgs:o[2],args:e}}}(r.str),r.parsed){let e=r.parsed.name;if("color"===e){let e=r.parsed.args.shift(),a=e.startsWith("--")?e.substring(2):`--${e}`,o=[e,a],n=r.parsed.rawArgs.indexOf("/")>0?r.parsed.args.pop():1;for(let a of C.all){let s=a.getFormat("color");if(s&&(o.includes(s.id)||s.ids?.filter((e=>o.includes(e))).length)){const o=Object.keys(a.coords).map(((e,t)=>r.parsed.args[t]||0));let i;return s.coordGrammar&&(i=w(a,s,"color",o)),t&&Object.assign(t,{formatId:"color",types:i}),s.id.startsWith("--")&&!e.startsWith("--")&&b.warn(`${a.name} is a non-standard space and not currently supported in the CSS spec. Use prefixed color(${s.id}) instead of color(${e}).`),e.startsWith("--")&&!s.id.startsWith("--")&&b.warn(`${a.name} is a standard space and supported in the CSS spec. Use color(${s.id}) instead of prefixed color(${e}).`),{spaceId:a.id,coords:o,alpha:n}}}let s="",i=e in C.registry?e:a;if(i in C.registry){let e=C.registry[i].formats?.color?.id;e&&(s=`Did you mean color(${e})?`)}throw new TypeError(`Cannot parse color(${e}). `+(s||"Missing a plugin?"))}for(let o of C.all){let n=o.getFormat(e);if(n&&"function"===n.type){let s=1;(n.lastAlpha||(a=r.parsed.args,a[a.length-1]).alpha)&&(s=r.parsed.args.pop());let i,c=r.parsed.args;return n.coordGrammar&&(i=w(o,n,e,c)),t&&Object.assign(t,{formatId:n.name,types:i}),{spaceId:o.id,coords:c,alpha:s}}}}else for(let e of C.all)for(let a in e.formats){let o=e.formats[a];if("custom"!==o.type)continue;if(o.test&&!o.test(r.str))continue;let n=o.parse(r.str);if(n)return n.alpha??=1,t&&(t.formatId=a),n}var a;throw new TypeError(`Could not parse ${e} as a color. Missing a plugin?`)}function x(e){if(Array.isArray(e))return e.map(x);if(!e)throw new TypeError("Empty color reference");t(e)&&(e=y(e));let r=e.space||e.spaceId;return r instanceof C||(e.space=C.get(r)),void 0===e.alpha&&(e.alpha=1),e}class C{constructor(e){this.id=e.id,this.name=e.name,this.base=e.base?C.get(e.base):null,this.aliases=e.aliases,this.base&&(this.fromBase=e.fromBase,this.toBase=e.toBase);let t=e.coords??this.base.coords;for(let e in t)"name"in t[e]||(t[e].name=e);this.coords=t;let r=e.white??this.base.white??"D65";this.white=f(r),this.formats=e.formats??{};for(let e in this.formats){let t=this.formats[e];t.type||="function",t.name||=e}this.formats.color?.id||(this.formats.color={...this.formats.color??{},id:e.cssId||this.id}),e.gamutSpace?this.gamutSpace="self"===e.gamutSpace?this:C.get(e.gamutSpace):this.isPolar?this.gamutSpace=this.base:this.gamutSpace=this,this.gamutSpace.isUnbounded&&(this.inGamut=(e,t)=>!0),this.referred=e.referred,Object.defineProperty(this,"path",{value:v(this).reverse(),writable:!1,enumerable:!0,configurable:!0}),m.run("colorspace-init-end",this)}inGamut(e,{epsilon:t=75e-6}={}){if(!this.equals(this.gamutSpace))return e=this.to(this.gamutSpace,e),this.gamutSpace.inGamut(e,{epsilon:t});let r=Object.values(this.coords);return e.every(((e,a)=>{let o=r[a];if("angle"!==o.type&&o.range){if(Number.isNaN(e))return!0;let[r,a]=o.range;return(void 0===r||e>=r-t)&&(void 0===a||e<=a+t)}return!0}))}get isUnbounded(){return Object.values(this.coords).every((e=>!("range"in e)))}get cssId(){return this.formats?.color?.id||this.id}get isPolar(){for(let e in this.coords)if("angle"===this.coords[e].type)return!0;return!1}getFormat(e){if("object"==typeof e)return e=R(e,this);let t;return t="default"===e?Object.values(this.formats)[0]:this.formats[e],t?(t=R(t,this),t):null}equals(e){return!!e&&(this===e||this.id===e||this.id===e.id)}to(e,t){if(1===arguments.length){const r=x(e);[e,t]=[r.space,r.coords]}if(e=C.get(e),this.equals(e))return t;t=t.map((e=>Number.isNaN(e)?0:e));let r,a,o=this.path,n=e.path;for(let e=0;e<o.length&&o[e].equals(n[e]);e++)r=o[e],a=e;if(!r)throw new Error(`Cannot convert between color spaces ${this} and ${e}: no connection space was found`);for(let e=o.length-1;e>a;e--)t=o[e].toBase(t);for(let e=a+1;e<n.length;e++)t=n[e].fromBase(t);return t}from(e,t){if(1===arguments.length){const r=x(e);[e,t]=[r.space,r.coords]}return(e=C.get(e)).to(this,t)}toString(){return`${this.name} (${this.id})`}getMinCoords(){let e=[];for(let t in this.coords){let r=this.coords[t],a=r.range||r.refRange;e.push(a?.min??0)}return e}static registry={};static get all(){return[...new Set(Object.values(C.registry))]}static register(e,t){if(1===arguments.length&&(e=(t=arguments[0]).id),t=this.get(t),this.registry[e]&&this.registry[e]!==t)throw new Error(`Duplicate color space registration: '${e}'`);if(this.registry[e]=t,1===arguments.length&&t.aliases)for(let e of t.aliases)this.register(e,t);return t}static get(e,...t){if(!e||e instanceof C)return e;if("string"===r(e)){let t=C.registry[e.toLowerCase()];if(!t)throw new TypeError(`No color space found with id = "${e}"`);return t}if(t.length)return C.get(...t);throw new TypeError(`${e} is not a valid color space`)}static resolveCoord(e,t){let a,o,n=r(e);if("string"===n?e.includes(".")?[a,o]=e.split("."):[a,o]=[,e]:Array.isArray(e)?[a,o]=e:(a=e.space,o=e.coordId),a=C.get(a),a||(a=t),!a)throw new TypeError(`Cannot resolve coordinate reference ${e}: No color space specified and relative references are not allowed here`);if(n=r(o),"number"===n||"string"===n&&o>=0){let e=Object.entries(a.coords)[o];if(e)return{space:a,id:e[0],index:o,...e[1]}}a=C.get(a);let s=o.toLowerCase(),i=0;for(let e in a.coords){let t=a.coords[e];if(e.toLowerCase()===s||t.name?.toLowerCase()===s)return{space:a,id:e,index:i,...t};i++}throw new TypeError(`No "${o}" coordinate found in ${a.name}. Its coordinates are: ${Object.keys(a.coords).join(", ")}`)}static DEFAULT_FORMAT={type:"functions",name:"color"}}function v(e){let t=[e];for(let r=e;r=r.base;)t.push(r);return t}function R(e,{coords:t}={}){if(e.coords&&!e.coordGrammar){e.type||="function",e.name||="color",e.coordGrammar=e.coords.map((e=>e.split("|").map((e=>{let t=(e=e.trim()).match(/^(<[a-z]+>)\[(-?[.\d]+),\s*(-?[.\d]+)\]?$/);if(t){let e=new String(t[1]);return e.range=[+t[2],+t[3]],e}return e}))));let r=Object.entries(t).map((([t,r],a)=>{let o=e.coordGrammar[a][0],n=r.range||r.refRange,s=o.range,i="";return"<percentage>"==o?(s=[0,100],i="%"):"<angle>"==o&&(i="deg"),{fromRange:n,toRange:s,suffix:i}}));e.serializeCoords=(e,t)=>e.map(((e,o)=>{let{fromRange:n,toRange:s,suffix:i}=r[o];return n&&s&&(e=c(n,s,e)),e=a(e,{precision:t,unit:i})}))}return e}var B=new C({id:"xyz-d65",name:"XYZ D65",coords:{x:{name:"X"},y:{name:"Y"},z:{name:"Z"}},white:"D65",formats:{color:{ids:["xyz-d65","xyz"]}},aliases:["xyz"]});class N extends C{constructor(t){t.coords||(t.coords={r:{range:[0,1],name:"Red"},g:{range:[0,1],name:"Green"},b:{range:[0,1],name:"Blue"}}),t.base||(t.base=B),t.toXYZ_M&&t.fromXYZ_M&&(t.toBase??=r=>{let a=e(t.toXYZ_M,r);return this.white!==this.base.white&&(a=g(this.white,this.base.white,a)),a},t.fromBase??=r=>(r=g(this.base.white,this.white,r),e(t.fromXYZ_M,r))),t.referred??="display",super(t)}}function k(e,t){return e=x(e),!t||e.space.equals(t)?e.coords.slice():(t=C.get(t)).from(e)}function E(e,t){e=x(e);let{space:r,index:a}=C.resolveCoord(t,e.space);return k(e,r)[a]}function S(e,t,r){return e=x(e),t=C.get(t),e.coords=t.to(e.space,r),e}function L(e,t,a){if(e=x(e),2===arguments.length&&"object"===r(arguments[1])){let t=arguments[1];for(let r in t)L(e,r,t[r])}else{"function"==typeof a&&(a=a(E(e,t)));let{space:r,index:o}=C.resolveCoord(t,e.space),n=k(e,r);n[o]=a,S(e,r,n)}return e}S.returns="color",L.returns="color";var _=new C({id:"xyz-d50",name:"XYZ D50",white:"D50",base:B,fromBase:e=>g(B.white,"D50",e),toBase:e=>g("D50",B.white,e)});const A=24/116,I=24389/27;let z=d.D50;var P=new C({id:"lab",name:"Lab",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:z,base:_,fromBase(e){let t=e.map(((e,t)=>e/z[t])).map((e=>e>.008856451679035631?Math.cbrt(e):(I*e+16)/116));return[116*t[1]-16,500*(t[0]-t[1]),200*(t[1]-t[2])]},toBase(e){let t=[];return t[1]=(e[0]+16)/116,t[0]=e[1]/500+t[1],t[2]=t[1]-e[2]/200,[t[0]>A?Math.pow(t[0],3):(116*t[0]-16)/I,e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/I,t[2]>A?Math.pow(t[2],3):(116*t[2]-16)/I].map(((e,t)=>e*z[t]))},formats:{lab:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function $(e){return(e%360+360)%360}var D=new C({id:"lch",name:"LCH",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,150],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:P,fromBase(e){let t,[r,a,o]=e;return t=Math.abs(a)<.02&&Math.abs(o)<.02?NaN:180*Math.atan2(o,a)/Math.PI,[r,Math.sqrt(a**2+o**2),$(t)]},toBase(e){let[t,r,a]=e;return r<0&&(r=0),isNaN(a)&&(a=0),[t,r*Math.cos(a*Math.PI/180),r*Math.sin(a*Math.PI/180)]},formats:{lch:{coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const q=25**7,H=Math.PI,j=180/H,W=H/180;function T(e){const t=e*e;return t*t*t*e}function G(e,t,{kL:r=1,kC:a=1,kH:o=1}={}){[e,t]=x([e,t]);let[n,s,i]=P.from(e),c=D.from(P,[n,s,i])[1],[l,u,h]=P.from(t),p=D.from(P,[l,u,h])[1];c<0&&(c=0),p<0&&(p=0);let m=T((c+p)/2),d=.5*(1-Math.sqrt(m/(m+q))),f=(1+d)*s,g=(1+d)*u,M=Math.sqrt(f**2+i**2),w=Math.sqrt(g**2+h**2),y=0===f&&0===i?0:Math.atan2(i,f),C=0===g&&0===h?0:Math.atan2(h,g);y<0&&(y+=2*H),C<0&&(C+=2*H),y*=j,C*=j;let v,R=l-n,B=w-M,N=C-y,k=y+C,E=Math.abs(N);M*w==0?v=0:E<=180?v=N:N>180?v=N-360:N<-180?v=N+360:b.warn("the unthinkable has happened");let S,L=2*Math.sqrt(w*M)*Math.sin(v*W/2),_=(n+l)/2,A=(M+w)/2,I=T(A);S=M*w==0?k:E<=180?k/2:k<360?(k+360)/2:(k-360)/2;let z=(_-50)**2,$=1+.015*z/Math.sqrt(20+z),G=1+.045*A,O=1;O-=.17*Math.cos((S-30)*W),O+=.24*Math.cos(2*S*W),O+=.32*Math.cos((3*S+6)*W),O-=.2*Math.cos((4*S-63)*W);let X=1+.015*A*O,Y=30*Math.exp(-1*((S-275)/25)**2),Z=2*Math.sqrt(I/(I+q)),J=(R/(r*$))**2;return J+=(B/(a*G))**2,J+=(L/(o*X))**2,J+=-1*Math.sin(2*Y*W)*Z*(B/(a*G))*(L/(o*X)),Math.sqrt(J)}const O=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],X=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],Y=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],Z=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]];var J=new C({id:"oklab",name:"Oklab",coords:{l:{refRange:[0,1],name:"Lightness"},a:{refRange:[-.4,.4]},b:{refRange:[-.4,.4]}},white:"D65",base:B,fromBase(t){let r=e(O,t).map((e=>Math.cbrt(e)));return e(Y,r)},toBase(t){let r=e(Z,t).map((e=>e**3));return e(X,r)},formats:{oklab:{coords:["<percentage> | <number>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});function F(e,t){[e,t]=x([e,t]);let[r,a,o]=J.from(e),[n,s,i]=J.from(t),c=r-n,l=a-s,u=o-i;return Math.sqrt(c**2+l**2+u**2)}const Q=75e-6;function U(e,t,{epsilon:r=Q}={}){e=x(e),t||(t=e.space),t=C.get(t);let a=e.coords;return t!==e.space&&(a=t.from(e)),t.inGamut(a,{epsilon:r})}function K(e){return{space:e.space,coords:e.coords.slice(),alpha:e.alpha}}function V(e,t,r="lab"){let a=(r=C.get(r)).from(e),o=r.from(t);return Math.sqrt(a.reduce(((e,t,r)=>{let a=o[r];return isNaN(t)||isNaN(a)?e:e+(a-t)**2}),0))}function ee(e,t){return V(e,t,"lab")}const te=Math.PI/180;function re(e,t,{l:r=2,c:a=1}={}){[e,t]=x([e,t]);let[o,n,s]=P.from(e),[,i,c]=D.from(P,[o,n,s]),[l,u,h]=P.from(t),p=D.from(P,[l,u,h])[1];i<0&&(i=0),p<0&&(p=0);let m=o-l,d=i-p,f=(n-u)**2+(s-h)**2-d**2,g=.511;o>=16&&(g=.040975*o/(1+.01765*o));let b,M=.0638*i/(1+.0131*i)+.638;Number.isNaN(c)&&(c=0),b=c>=164&&c<=345?.56+Math.abs(.2*Math.cos((c+168)*te)):.36+Math.abs(.4*Math.cos((c+35)*te));let w=Math.pow(i,4),y=Math.sqrt(w/(w+1900)),C=(m/(r*g))**2;return C+=(d/(a*M))**2,C+=f/(M*(y*b+1-y))**2,Math.sqrt(C)}var ae=new C({id:"xyz-abs-d65",cssId:"--xyz-abs-d65",name:"Absolute XYZ D65",coords:{x:{refRange:[0,9504.7],name:"Xa"},y:{refRange:[0,1e4],name:"Ya"},z:{refRange:[0,10888.3],name:"Za"}},base:B,fromBase:e=>e.map((e=>Math.max(203*e,0))),toBase:e=>e.map((e=>Math.max(e/203,0)))});const oe=1.15,ne=.66,se=2610/16384,ie=.8359375,ce=2413/128,le=18.6875,ue=32/(1.7*2523),he=-.56,pe=16295499532821565e-27,me=[[.41478972,.579999,.014648],[-.20151,1.120649,.0531008],[-.0166008,.2648,.6684799]],de=[[1.9242264357876067,-1.0047923125953657,.037651404030618],[.35031676209499907,.7264811939316552,-.06538442294808501],[-.09098281098284752,-.3127282905230739,1.5227665613052603]],fe=[[.5,.5,0],[3.524,-4.066708,.542708],[.199076,1.096799,-1.295875]],ge=[[1,.1386050432715393,.05804731615611886],[.9999999999999999,-.1386050432715393,-.05804731615611886],[.9999999999999998,-.09601924202631895,-.8118918960560388]];var be=new C({id:"jzazbz",name:"Jzazbz",coords:{jz:{refRange:[0,1],name:"Jz"},az:{refRange:[-.5,.5]},bz:{refRange:[-.5,.5]}},base:ae,fromBase(t){let[r,a,o]=t,n=e(me,[oe*r-(oe-1)*o,ne*a-(ne-1)*r,o]).map((function(e){return((ie+ce*(e/1e4)**se)/(1+le*(e/1e4)**se))**134.03437499999998})),[s,i,c]=e(fe,n);return[(1+he)*s/(1+he*s)-pe,i,c]},toBase(t){let[r,a,o]=t,n=e(ge,[(r+pe)/(1+he-he*(r+pe)),a,o]).map((function(e){return 1e4*((ie-e**ue)/(le*e**ue-ce))**6.277394636015326})),[s,i,c]=e(de,n),l=(s+(oe-1)*c)/oe;return[l,(i+(ne-1)*l)/ne,c]},formats:{color:{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),Me=new C({id:"jzczhz",name:"JzCzHz",coords:{jz:{refRange:[0,1],name:"Jz"},cz:{refRange:[0,1],name:"Chroma"},hz:{refRange:[0,360],type:"angle",name:"Hue"}},base:be,fromBase(e){let t,[r,a,o]=e;const n=2e-4;return t=Math.abs(a)<n&&Math.abs(o)<n?NaN:180*Math.atan2(o,a)/Math.PI,[r,Math.sqrt(a**2+o**2),$(t)]},toBase:e=>[e[0],e[1]*Math.cos(e[2]*Math.PI/180),e[1]*Math.sin(e[2]*Math.PI/180)]});function we(e,t){[e,t]=x([e,t]);let[r,a,o]=Me.from(e),[n,s,i]=Me.from(t),c=r-n,l=a-s;Number.isNaN(o)&&Number.isNaN(i)?(o=0,i=0):Number.isNaN(o)?o=i:Number.isNaN(i)&&(i=o);let u=o-i,h=2*Math.sqrt(a*s)*Math.sin(u/2*(Math.PI/180));return Math.sqrt(c**2+l**2+h**2)}const ye=.8359375,xe=2413/128,Ce=18.6875,ve=2610/16384,Re=2523/32,Be=16384/2610,Ne=32/2523,ke=[[.3592832590121217,.6976051147779502,-.035891593232029],[-.1920808463704993,1.100476797037432,.0753748658519118],[.0070797844607479,.0748396662186362,.8433265453898765]],Ee=[[.5,.5,0],[6610/4096,-13613/4096,7003/4096],[17933/4096,-17390/4096,-543/4096]],Se=[[.9999999999999998,.0086090370379328,.111029625003026],[.9999999999999998,-.0086090370379328,-.1110296250030259],[.9999999999999998,.5600313357106791,-.3206271749873188]],Le=[[2.0701522183894223,-1.3263473389671563,.2066510476294053],[.3647385209748072,.6805660249472273,-.0453045459220347],[-.0497472075358123,-.0492609666966131,1.1880659249923042]];var _e=new C({id:"ictcp",name:"ICTCP",coords:{i:{refRange:[0,1],name:"I"},ct:{refRange:[-.5,.5],name:"CT"},cp:{refRange:[-.5,.5],name:"CP"}},base:ae,fromBase:t=>function(t){let r=t.map((function(e){return((ye+xe*(e/1e4)**ve)/(1+Ce*(e/1e4)**ve))**Re}));return e(Ee,r)}(e(ke,t)),toBase(t){let r=function(t){let r=e(Se,t),a=r.map((function(e){return 1e4*(Math.max(e**Ne-ye,0)/(xe-Ce*e**Ne))**Be}));return a}(t);return e(Le,r)}});function Ae(e,t){[e,t]=x([e,t]);let[r,a,o]=_e.from(e),[n,s,i]=_e.from(t);return 720*Math.sqrt((r-n)**2+.25*(a-s)**2+(o-i)**2)}const Ie=d.D65,ze=.42,Pe=1/ze,$e=2*Math.PI,De=[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],qe=[[1.8620678550872327,-1.0112546305316843,.14918677544445175],[.38752654323613717,.6214474419314753,-.008973985167612518],[-.015841498849333856,-.03412293802851557,1.0499644368778496]],He=[[460,451,288],[460,-891,-261],[460,-220,-6300]],je={dark:[.8,.525,.8],dim:[.9,.59,.9],average:[1,.69,1]},We={h:[20.14,90,164.25,237.53,380.14],e:[.8,.7,1,1.2,.8],H:[0,100,200,300,400]},Te=180/Math.PI,Ge=Math.PI/180;function Oe(e,t){const r=e.map((e=>{const r=u(t*Math.abs(e)*.01,ze);return 400*l(r,e)/(r+27.13)}));return r}function Xe(t,r,a,o,n){const s={};s.discounting=n,s.refWhite=t,s.surround=o;const c=t.map((e=>100*e));s.la=r,s.yb=a;const l=c[1],u=e(De,c),h=(o=je[s.surround])[0];s.c=o[1],s.nc=o[2];const p=(1/(5*s.la+1))**4;s.fl=p*s.la+.1*(1-p)*(1-p)*Math.cbrt(5*s.la),s.flRoot=s.fl**.25,s.n=s.yb/l,s.z=1.48+Math.sqrt(s.n),s.nbb=.725*s.n**-.2,s.ncb=s.nbb;const m=n?1:Math.max(Math.min(h*(1-1/3.6*Math.exp((-s.la-42)/92)),1),0);s.dRgb=u.map((e=>i(1,l/e,m))),s.dRgbInv=s.dRgb.map((e=>1/e));const d=u.map(((e,t)=>e*s.dRgb[t])),f=Oe(d,s.fl);return s.aW=s.nbb*(2*f[0]+f[1]+.05*f[2]),s}const Ye=Xe(Ie,64/Math.PI*.2,20,"average",!1);function Ze(t,r){if(!(void 0!==t.J^void 0!==t.Q))throw new Error("Conversion requires one and only one: 'J' or 'Q'");if(!(void 0!==t.C^void 0!==t.M^void 0!==t.s))throw new Error("Conversion requires one and only one: 'C', 'M' or 's'");if(!(void 0!==t.h^void 0!==t.H))throw new Error("Conversion requires one and only one: 'h' or 'H'");if(0===t.J||0===t.Q)return[0,0,0];let a=0;a=void 0!==t.h?$(t.h)*Ge:function(e){let t=(e%400+400)%400;const r=Math.floor(.01*t);t%=100;const[a,o]=We.h.slice(r,r+2),[n,s]=We.e.slice(r,r+2);return $((t*(s*a-n*o)-100*a*s)/(t*(s-n)-100*s))}(t.H)*Ge;const o=Math.cos(a),n=Math.sin(a);let s=0;void 0!==t.J?s=.1*u(t.J,.5):void 0!==t.Q&&(s=.25*r.c*t.Q/((r.aW+4)*r.flRoot));let i=0;void 0!==t.C?i=t.C/s:void 0!==t.M?i=t.M/r.flRoot/s:void 0!==t.s&&(i=4e-4*t.s**2*(r.aW+4)/r.c);const c=u(i*Math.pow(1.64-Math.pow(.29,r.n),-.73),10/9),p=.25*(Math.cos(a+2)+3.8),m=r.aW*u(s,2/r.c/r.z),d=5e4/13*r.nc*r.ncb*p,f=m/r.nbb,g=23*(f+.305)*h(c,23*d+c*(11*o+108*n)),b=function(e,t){const r=100/t*27.13**Pe;return e.map((e=>{const t=Math.abs(e);return l(r*u(t/(400-t),Pe),e)}))}(e(He,[f,g*o,g*n]).map((e=>1*e/1403)),r.fl);return e(qe,b.map(((e,t)=>e*r.dRgbInv[t]))).map((e=>e/100))}function Je(t,r){const a=t.map((e=>100*e)),o=Oe(e(De,a).map(((e,t)=>e*r.dRgb[t])),r.fl),n=o[0]+(-12*o[1]+o[2])/11,s=(o[0]+o[1]-2*o[2])/9,i=(Math.atan2(s,n)%$e+$e)%$e,c=.25*(Math.cos(i+2)+3.8),l=u(5e4/13*r.nc*r.ncb*h(c*Math.sqrt(n**2+s**2),o[0]+o[1]+1.05*o[2]+.305),.9)*Math.pow(1.64-Math.pow(.29,r.n),.73),p=u(r.nbb*(2*o[0]+o[1]+.05*o[2])/r.aW,.5*r.c*r.z),m=100*u(p,2),d=4/r.c*p*(r.aW+4)*r.flRoot,f=l*p,g=f*r.flRoot,b=$(i*Te),M=function(e){let t=$(e);t<=We.h[0]&&(t+=360);const r=function(e,t,r=0,a=e.length){for(;r<a;){const o=r+a>>1;e[o]<t?r=o+1:a=o}return r}(We.h,t)-1,[a,o]=We.h.slice(r,r+2),[n,s]=We.e.slice(r,r+2),i=(t-a)/n;return We.H[r]+100*i/(i+(o-t)/s)}(b);return{J:m,C:f,h:b,s:50*u(r.c*l/(r.aW+4),.5),Q:d,M:g,H:M}}var Fe=new C({id:"cam16-jmh",cssId:"--cam16-jmh",name:"CAM16-JMh",coords:{j:{refRange:[0,100],name:"J"},m:{refRange:[0,105],name:"Colorfulness"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:B,fromBase(e){const t=Je(e,Ye);return[t.J,t.M,t.h]},toBase:e=>Ze({J:e[0],M:e[1],h:e[2]},Ye)});const Qe=d.D65,Ue=216/24389,Ke=24389/27;function Ve(e){return e>8?Math.pow((e+16)/116,3):e/Ke}function et(e,t){const r=116*((a=e[1])>Ue?Math.cbrt(a):(Ke*a+16)/116)-16;var a;if(0===r)return[0,0,0];const o=Je(e,tt);return[$(o.h),o.C,r]}const tt=Xe(Qe,200/Math.PI*Ve(50),100*Ve(50),"average",!1);var rt=new C({id:"hct",name:"HCT",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},c:{refRange:[0,145],name:"Colorfulness"},t:{refRange:[0,100],name:"Tone"}},base:B,fromBase:e=>et(e),toBase:e=>function(e,t){let[r,a,o]=e,n=[],s=0;if(0===o)return[0,0,0];let i=Ve(o);s=o>0?.00379058511492914*o**2+.608983189401032*o+.9155088574762233:9514440756550361e-21*o**2+.08693057439788597*o-21.928975842194614;let c=0,l=1/0;for(;c<=15;){n=Ze({J:s,C:a,h:r},t);const e=Math.abs(n[1]-i);if(e<l){if(e<=2e-12)return n;l=e}s-=(n[1]-i)*s/(2*n[1]),c+=1}return Ze({J:s,C:a,h:r},t)}(e,tt),formats:{color:{id:"--hct",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const at=Math.PI/180,ot=[1,.007,.0228];function nt(e){e[1]<0&&(e=rt.fromBase(rt.toBase(e)));const t=Math.log(Math.max(1+ot[2]*e[1]*tt.flRoot,1))/ot[2],r=e[0]*at,a=t*Math.cos(r),o=t*Math.sin(r);return[e[2],a,o]}function st(e,t){[e,t]=x([e,t]);let[r,a,o]=nt(rt.from(e)),[n,s,i]=nt(rt.from(t));return Math.sqrt((r-n)**2+(a-s)**2+(o-i)**2)}var it={deltaE76:ee,deltaECMC:re,deltaE2000:G,deltaEJz:we,deltaEITP:Ae,deltaEOK:F,deltaEHCT:st};const ct={hct:{method:"hct.c",jnd:2,deltaEMethod:"hct",blackWhiteClamp:{}},"hct-tonal":{method:"hct.c",jnd:0,deltaEMethod:"hct",blackWhiteClamp:{channel:"hct.t",min:0,max:100}}};function lt(e,{method:r=b.gamut_mapping,space:a,deltaEMethod:n="",jnd:s=2,blackWhiteClamp:i={}}={}){if(e=x(e),t(arguments[1])?a=arguments[1]:a||(a=e.space),U(e,a=C.get(a),{epsilon:0}))return e;let c;if("css"===r)c=ht(e,{space:a});else{if("clip"===r||U(e,a))c=pt(e,a);else{Object.prototype.hasOwnProperty.call(ct,r)&&({method:r,jnd:s,deltaEMethod:n,blackWhiteClamp:i}=ct[r]);let t=G;if(""!==n)for(let e in it)if("deltae"+n.toLowerCase()===e.toLowerCase()){t=it[e];break}let l=lt(pt(e,a),{method:"clip",space:a});if(t(e,l)>s){if(3===Object.keys(i).length){let t=C.resolveCoord(i.channel),r=E(pt(e,t.space),t.id);if(o(r)&&(r=0),r>=i.max)return pt({space:"xyz-d65",coords:d.D65},e.space);if(r<=i.min)return pt({space:"xyz-d65",coords:[0,0,0]},e.space)}let n=C.resolveCoord(r),l=n.space,u=n.id,h=pt(e,l);h.coords.forEach(((e,t)=>{o(e)&&(h.coords[t]=0)}));let p=(n.range||n.refRange)[0],m=function(e){const t=e?Math.floor(Math.log10(Math.abs(e))):0;return Math.max(parseFloat("1e"+(t-2)),1e-6)}(s),f=p,g=E(h,u);for(;g-f>m;){let e=K(h);e=lt(e,{space:a,method:"clip"}),t(h,e)-s<m?f=E(h,u):g=E(h,u),L(h,u,(f+g)/2)}c=pt(h,a)}else c=l}if("clip"===r||!U(c,a,{epsilon:0})){let e=Object.values(a.coords).map((e=>e.range||[]));c.coords=c.coords.map(((t,r)=>{let[a,o]=e[r];return void 0!==a&&(t=Math.max(a,t)),void 0!==o&&(t=Math.min(t,o)),t}))}}return a!==e.space&&(c=pt(c,e.space)),e.coords=c.coords,e}lt.returns="color";const ut={WHITE:{space:J,coords:[1,0,0]},BLACK:{space:J,coords:[0,0,0]}};function ht(e,{space:t}={}){const r=.02,a=1e-4;e=x(e),t||(t=e.space),t=C.get(t);const o=C.get("oklch");if(t.isUnbounded)return pt(e,t);const n=pt(e,o);let s=n.coords[0];if(s>=1){const r=pt(ut.WHITE,t);return r.alpha=e.alpha,pt(r,t)}if(s<=0){const r=pt(ut.BLACK,t);return r.alpha=e.alpha,pt(r,t)}if(U(n,t,{epsilon:0}))return pt(n,t);function i(e){const r=pt(e,t),a=Object.values(t.coords);return r.coords=r.coords.map(((e,t)=>{if("range"in a[t]){const[r,o]=a[t].range;return function(e,t,r){return Math.max(Math.min(r,t),e)}(r,e,o)}return e})),r}let c=0,l=n.coords[1],u=!0,h=K(n),p=i(h),m=F(p,h);if(m<r)return p;for(;l-c>a;){const e=(c+l)/2;if(h.coords[1]=e,u&&U(h,t,{epsilon:0}))c=e;else if(p=i(h),m=F(p,h),m<r){if(r-m<a)break;u=!1,c=e}else l=e}return p}function pt(e,t,{inGamut:r}={}){e=x(e);let a=(t=C.get(t)).from(e),o={space:t,coords:a,alpha:e.alpha};return r&&(o=lt(o,!0===r?void 0:r)),o}function mt(e,{precision:t=b.precision,format:r="default",inGamut:o=!0,...n}={}){let s,i=r;r=(e=x(e)).space.getFormat(r)??e.space.getFormat("default")??C.DEFAULT_FORMAT;let c=e.coords.slice();if(o||=r.toGamut,o&&!U(e)&&(c=lt(K(e),!0===o?void 0:o).coords),"custom"===r.type){if(n.precision=t,!r.serialize)throw new TypeError(`format ${i} can only be used to parse colors, not for serialization`);s=r.serialize(c,e.alpha,n)}else{let o=r.name||"color";r.serializeCoords?c=r.serializeCoords(c,t):null!==t&&(c=c.map((e=>a(e,{precision:t}))));let n=[...c];if("color"===o){let t=r.id||r.ids?.[0]||e.space.id;n.unshift(t)}let i=e.alpha;null!==t&&(i=a(i,{precision:t}));let l=e.alpha>=1||r.noAlpha?"":`${r.commas?",":" /"} ${i}`;s=`${o}(${n.join(r.commas?", ":" ")}${l})`}return s}pt.returns="color";var dt=new N({id:"rec2020-linear",cssId:"--rec2020-linear",name:"Linear REC.2020",white:"D65",toXYZ_M:[[.6369580483012914,.14461690358620832,.1688809751641721],[.2627002120112671,.6779980715188708,.05930171646986196],[0,.028072693049087428,1.060985057710791]],fromXYZ_M:[[1.716651187971268,-.355670783776392,-.25336628137366],[-.666684351832489,1.616481236634939,.0157685458139111],[.017639857445311,-.042770613257809,.942103121235474]]});const ft=1.09929682680944,gt=.018053968510807;var bt=new N({id:"rec2020",name:"REC.2020",base:dt,toBase:e=>e.map((function(e){return e<4.5*gt?e/4.5:Math.pow((e+ft-1)/ft,1/.45)})),fromBase:e=>e.map((function(e){return e>=gt?ft*Math.pow(e,.45)-(ft-1):4.5*e}))});var Mt=new N({id:"p3-linear",cssId:"--display-p3-linear",name:"Linear P3",white:"D65",toXYZ_M:[[.4865709486482162,.26566769316909306,.1982172852343625],[.2289745640697488,.6917385218365064,.079286914093745],[0,.04511338185890264,1.043944368900976]],fromXYZ_M:[[2.493496911941425,-.9313836179191239,-.40271078445071684],[-.8294889695615747,1.7626640603183463,.023624685841943577],[.03584583024378447,-.07617238926804182,.9568845240076872]]});const wt=[[3.2409699419045226,-1.537383177570094,-.4986107602930034],[-.9692436362808796,1.8759675015077202,.04155505740717559],[.05563007969699366,-.20397695888897652,1.0569715142428786]];var yt=new N({id:"srgb-linear",name:"Linear sRGB",white:"D65",toXYZ_M:[[.41239079926595934,.357584339383878,.1804807884018343],[.21263900587151027,.715168678767756,.07219231536073371],[.01933081871559182,.11919477979462598,.9505321522496607]],fromXYZ_M:wt}),xt={aliceblue:[240/255,248/255,1],antiquewhite:[250/255,235/255,215/255],aqua:[0,1,1],aquamarine:[127/255,1,212/255],azure:[240/255,1,1],beige:[245/255,245/255,220/255],bisque:[1,228/255,196/255],black:[0,0,0],blanchedalmond:[1,235/255,205/255],blue:[0,0,1],blueviolet:[138/255,43/255,226/255],brown:[165/255,42/255,42/255],burlywood:[222/255,184/255,135/255],cadetblue:[95/255,158/255,160/255],chartreuse:[127/255,1,0],chocolate:[210/255,105/255,30/255],coral:[1,127/255,80/255],cornflowerblue:[100/255,149/255,237/255],cornsilk:[1,248/255,220/255],crimson:[220/255,20/255,60/255],cyan:[0,1,1],darkblue:[0,0,139/255],darkcyan:[0,139/255,139/255],darkgoldenrod:[184/255,134/255,11/255],darkgray:[169/255,169/255,169/255],darkgreen:[0,100/255,0],darkgrey:[169/255,169/255,169/255],darkkhaki:[189/255,183/255,107/255],darkmagenta:[139/255,0,139/255],darkolivegreen:[85/255,107/255,47/255],darkorange:[1,140/255,0],darkorchid:[.6,50/255,.8],darkred:[139/255,0,0],darksalmon:[233/255,150/255,122/255],darkseagreen:[143/255,188/255,143/255],darkslateblue:[72/255,61/255,139/255],darkslategray:[47/255,79/255,79/255],darkslategrey:[47/255,79/255,79/255],darkturquoise:[0,206/255,209/255],darkviolet:[148/255,0,211/255],deeppink:[1,20/255,147/255],deepskyblue:[0,191/255,1],dimgray:[105/255,105/255,105/255],dimgrey:[105/255,105/255,105/255],dodgerblue:[30/255,144/255,1],firebrick:[178/255,34/255,34/255],floralwhite:[1,250/255,240/255],forestgreen:[34/255,139/255,34/255],fuchsia:[1,0,1],gainsboro:[220/255,220/255,220/255],ghostwhite:[248/255,248/255,1],gold:[1,215/255,0],goldenrod:[218/255,165/255,32/255],gray:[128/255,128/255,128/255],green:[0,128/255,0],greenyellow:[173/255,1,47/255],grey:[128/255,128/255,128/255],honeydew:[240/255,1,240/255],hotpink:[1,105/255,180/255],indianred:[205/255,92/255,92/255],indigo:[75/255,0,130/255],ivory:[1,1,240/255],khaki:[240/255,230/255,140/255],lavender:[230/255,230/255,250/255],lavenderblush:[1,240/255,245/255],lawngreen:[124/255,252/255,0],lemonchiffon:[1,250/255,205/255],lightblue:[173/255,216/255,230/255],lightcoral:[240/255,128/255,128/255],lightcyan:[224/255,1,1],lightgoldenrodyellow:[250/255,250/255,210/255],lightgray:[211/255,211/255,211/255],lightgreen:[144/255,238/255,144/255],lightgrey:[211/255,211/255,211/255],lightpink:[1,182/255,193/255],lightsalmon:[1,160/255,122/255],lightseagreen:[32/255,178/255,170/255],lightskyblue:[135/255,206/255,250/255],lightslategray:[119/255,136/255,.6],lightslategrey:[119/255,136/255,.6],lightsteelblue:[176/255,196/255,222/255],lightyellow:[1,1,224/255],lime:[0,1,0],limegreen:[50/255,205/255,50/255],linen:[250/255,240/255,230/255],magenta:[1,0,1],maroon:[128/255,0,0],mediumaquamarine:[.4,205/255,170/255],mediumblue:[0,0,205/255],mediumorchid:[186/255,85/255,211/255],mediumpurple:[147/255,112/255,219/255],mediumseagreen:[60/255,179/255,113/255],mediumslateblue:[123/255,104/255,238/255],mediumspringgreen:[0,250/255,154/255],mediumturquoise:[72/255,209/255,.8],mediumvioletred:[199/255,21/255,133/255],midnightblue:[25/255,25/255,112/255],mintcream:[245/255,1,250/255],mistyrose:[1,228/255,225/255],moccasin:[1,228/255,181/255],navajowhite:[1,222/255,173/255],navy:[0,0,128/255],oldlace:[253/255,245/255,230/255],olive:[128/255,128/255,0],olivedrab:[107/255,142/255,35/255],orange:[1,165/255,0],orangered:[1,69/255,0],orchid:[218/255,112/255,214/255],palegoldenrod:[238/255,232/255,170/255],palegreen:[152/255,251/255,152/255],paleturquoise:[175/255,238/255,238/255],palevioletred:[219/255,112/255,147/255],papayawhip:[1,239/255,213/255],peachpuff:[1,218/255,185/255],peru:[205/255,133/255,63/255],pink:[1,192/255,203/255],plum:[221/255,160/255,221/255],powderblue:[176/255,224/255,230/255],purple:[128/255,0,128/255],rebeccapurple:[.4,.2,.6],red:[1,0,0],rosybrown:[188/255,143/255,143/255],royalblue:[65/255,105/255,225/255],saddlebrown:[139/255,69/255,19/255],salmon:[250/255,128/255,114/255],sandybrown:[244/255,164/255,96/255],seagreen:[46/255,139/255,87/255],seashell:[1,245/255,238/255],sienna:[160/255,82/255,45/255],silver:[192/255,192/255,192/255],skyblue:[135/255,206/255,235/255],slateblue:[106/255,90/255,205/255],slategray:[112/255,128/255,144/255],slategrey:[112/255,128/255,144/255],snow:[1,250/255,250/255],springgreen:[0,1,127/255],steelblue:[70/255,130/255,180/255],tan:[210/255,180/255,140/255],teal:[0,128/255,128/255],thistle:[216/255,191/255,216/255],tomato:[1,99/255,71/255],turquoise:[64/255,224/255,208/255],violet:[238/255,130/255,238/255],wheat:[245/255,222/255,179/255],white:[1,1,1],whitesmoke:[245/255,245/255,245/255],yellow:[1,1,0],yellowgreen:[154/255,205/255,50/255]};let Ct=Array(3).fill("<percentage> | <number>[0, 255]"),vt=Array(3).fill("<number>[0, 255]");var Rt=new N({id:"srgb",name:"sRGB",base:yt,fromBase:e=>e.map((e=>{let t=e<0?-1:1,r=e*t;return r>.0031308?t*(1.055*r**(1/2.4)-.055):12.92*e})),toBase:e=>e.map((e=>{let t=e<0?-1:1,r=e*t;return r<=.04045?e/12.92:t*((r+.055)/1.055)**2.4})),formats:{rgb:{coords:Ct},rgb_number:{name:"rgb",commas:!0,coords:vt,noAlpha:!0},color:{},rgba:{coords:Ct,commas:!0,lastAlpha:!0},rgba_number:{name:"rgba",commas:!0,coords:vt},hex:{type:"custom",toGamut:!0,test:e=>/^#([a-f0-9]{3,4}){1,2}$/i.test(e),parse(e){e.length<=5&&(e=e.replace(/[a-f0-9]/gi,"$&$&"));let t=[];return e.replace(/[a-f0-9]{2}/gi,(e=>{t.push(parseInt(e,16)/255)})),{spaceId:"srgb",coords:t.slice(0,3),alpha:t.slice(3)[0]}},serialize:(e,t,{collapse:r=!0}={})=>{t<1&&e.push(t),e=e.map((e=>Math.round(255*e)));let a=r&&e.every((e=>e%17==0)),o=e.map((e=>a?(e/17).toString(16):e.toString(16).padStart(2,"0"))).join("");return"#"+o}},keyword:{type:"custom",test:e=>/^[a-z]+$/i.test(e),parse(e){let t={spaceId:"srgb",coords:null,alpha:1};if("transparent"===(e=e.toLowerCase())?(t.coords=xt.black,t.alpha=0):t.coords=xt[e],t.coords)return t}}}}),Bt=new N({id:"p3",cssId:"display-p3",name:"P3",base:Mt,fromBase:Rt.fromBase,toBase:Rt.toBase});let Nt;if(b.display_space=Rt,"undefined"!=typeof CSS&&CSS.supports)for(let e of[P,bt,Bt]){let t=e.getMinCoords(),r=mt({space:e,coords:t,alpha:1});if(CSS.supports("color",r)){b.display_space=e;break}}function kt(e){return E(e,[B,"y"])}function Et(e,t){e=x(e),t=x(t);let r=Math.max(kt(e),0),a=Math.max(kt(t),0);return a>r&&([r,a]=[a,r]),(r+.05)/(a+.05)}const St=.022,Lt=1.414;function _t(e){return e>=St?e:e+(St-e)**Lt}function At(e){let t=e<0?-1:1,r=Math.abs(e);return t*Math.pow(r,2.4)}function It(e,t){let r,a,o,n,s,i;t=x(t),e=x(e),t=pt(t,"srgb"),[n,s,i]=t.coords;let c=.2126729*At(n)+.7151522*At(s)+.072175*At(i);e=pt(e,"srgb"),[n,s,i]=e.coords;let l=.2126729*At(n)+.7151522*At(s)+.072175*At(i),u=_t(c),h=_t(l),p=h>u;return Math.abs(h-u)<5e-4?a=0:p?(r=h**.56-u**.57,a=1.14*r):(r=h**.65-u**.62,a=1.14*r),o=Math.abs(a)<.1?0:a>0?a-.027:a+.027,100*o}function zt(e,t){e=x(e),t=x(t);let r=Math.max(kt(e),0),a=Math.max(kt(t),0);a>r&&([r,a]=[a,r]);let o=r+a;return 0===o?0:(r-a)/o}function Pt(e,t){e=x(e),t=x(t);let r=Math.max(kt(e),0),a=Math.max(kt(t),0);return a>r&&([r,a]=[a,r]),0===a?5e4:(r-a)/a}function $t(e,t){e=x(e),t=x(t);let r=E(e,[P,"l"]),a=E(t,[P,"l"]);return Math.abs(r-a)}const Dt=24/116,qt=24389/27;let Ht=d.D65;var jt=new C({id:"lab-d65",name:"Lab D65",coords:{l:{refRange:[0,100],name:"Lightness"},a:{refRange:[-125,125]},b:{refRange:[-125,125]}},white:Ht,base:B,fromBase(e){let t=e.map(((e,t)=>e/Ht[t])).map((e=>e>.008856451679035631?Math.cbrt(e):(qt*e+16)/116));return[116*t[1]-16,500*(t[0]-t[1]),200*(t[1]-t[2])]},toBase(e){let t=[];return t[1]=(e[0]+16)/116,t[0]=e[1]/500+t[1],t[2]=t[1]-e[2]/200,[t[0]>Dt?Math.pow(t[0],3):(116*t[0]-16)/qt,e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/qt,t[2]>Dt?Math.pow(t[2],3):(116*t[2]-16)/qt].map(((e,t)=>e*Ht[t]))},formats:{"lab-d65":{coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}});const Wt=.5*Math.pow(5,.5)+.5;function Tt(e,t){e=x(e),t=x(t);let r=E(e,[jt,"l"]),a=E(t,[jt,"l"]),o=Math.abs(Math.pow(r,Wt)-Math.pow(a,Wt)),n=Math.pow(o,1/Wt)*Math.SQRT2-40;return n<7.5?0:n}var Gt=Object.freeze({__proto__:null,contrastAPCA:It,contrastDeltaPhi:Tt,contrastLstar:$t,contrastMichelson:zt,contrastWCAG21:Et,contrastWeber:Pt});function Ot(e){let[t,r,a]=k(e,B),o=t+15*r+3*a;return[4*t/o,9*r/o]}function Xt(e,r,a={}){t(a)&&(a={method:a});let{method:o=b.deltaE,...n}=a;for(let t in it)if("deltae"+o.toLowerCase()===t.toLowerCase())return it[t](e,r,n);throw new TypeError(`Unknown deltaE method: ${o}`)}function Yt(e,t,r={}){if(Zt(e)){let[r,a]=[e,t];return Yt(...r.rangeArgs.colors,{...r.rangeArgs.options,...a})}let{space:a,outputSpace:o,progression:n,premultiplied:s}=r;e=x(e),t=x(t),e=K(e),t=K(t);let c={colors:[e,t],options:r};if(a=a?C.get(a):C.registry[b.interpolationSpace]||e.space,o=o?C.get(o):a,e=pt(e,a),t=pt(t,a),e=lt(e),t=lt(t),a.coords.h&&"angle"===a.coords.h.type){let o=r.hue=r.hue||"shorter",n=[a,"h"],[s,i]=[E(e,n),E(t,n)];isNaN(s)&&!isNaN(i)?s=i:isNaN(i)&&!isNaN(s)&&(i=s),[s,i]=function(e,t){if("raw"===e)return t;let[r,a]=t.map($),o=a-r;return"increasing"===e?o<0&&(a+=360):"decreasing"===e?o>0&&(r+=360):"longer"===e?-180<o&&o<180&&(o>0?r+=360:a+=360):"shorter"===e&&(o>180?r+=360:o<-180&&(a+=360)),[r,a]}(o,[s,i]),L(e,n,s),L(t,n,i)}return s&&(e.coords=e.coords.map((t=>t*e.alpha)),t.coords=t.coords.map((e=>e*t.alpha))),Object.assign((r=>{r=n?n(r):r;let c=e.coords.map(((e,a)=>i(e,t.coords[a],r))),l=i(e.alpha,t.alpha,r),u={space:a,coords:c,alpha:l};return s&&(u.coords=u.coords.map((e=>e/l))),o!==a&&(u=pt(u,o)),u}),{rangeArgs:c})}function Zt(e){return"function"===r(e)&&!!e.rangeArgs}b.interpolationSpace="lab";var Jt=new C({id:"hsl",name:"HSL",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:Rt,fromBase:e=>{let t=Math.max(...e),r=Math.min(...e),[a,o,n]=e,[s,i,c]=[NaN,0,(r+t)/2],l=t-r;if(0!==l){switch(i=0===c||1===c?0:(t-c)/Math.min(c,1-c),t){case a:s=(o-n)/l+(o<n?6:0);break;case o:s=(n-a)/l+2;break;case n:s=(a-o)/l+4}s*=60}return i<0&&(s+=180,i=Math.abs(i)),s>=360&&(s-=360),[s,100*i,100*c]},toBase:e=>{let[t,r,a]=e;function o(e){let o=(e+t/30)%12,n=r*Math.min(a,1-a);return a-n*Math.max(-1,Math.min(o-3,9-o,1))}return t%=360,t<0&&(t+=360),r/=100,a/=100,[o(0),o(8),o(4)]},formats:{hsl:{coords:["<number> | <angle>","<percentage>","<percentage>"]},hsla:{coords:["<number> | <angle>","<percentage>","<percentage>"],commas:!0,lastAlpha:!0}}}),Ft=new C({id:"hsv",name:"HSV",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},v:{range:[0,100],name:"Value"}},base:Jt,fromBase(e){let[t,r,a]=e;r/=100,a/=100;let o=a+r*Math.min(a,1-a);return[t,0===o?0:200*(1-a/o),100*o]},toBase(e){let[t,r,a]=e;r/=100,a/=100;let o=a*(1-r/2);return[t,0===o||1===o?0:(a-o)/Math.min(o,1-o)*100,100*o]},formats:{color:{id:"--hsv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}}),Qt=new C({id:"hwb",name:"HWB",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},w:{range:[0,100],name:"Whiteness"},b:{range:[0,100],name:"Blackness"}},base:Ft,fromBase(e){let[t,r,a]=e;return[t,a*(100-r)/100,100-a]},toBase(e){let[t,r,a]=e;r/=100,a/=100;let o=r+a;if(o>=1){return[t,0,100*(r/o)]}let n=1-a;return[t,100*(0===n?0:1-r/n),100*n]},formats:{hwb:{coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});var Ut=new N({id:"a98rgb-linear",cssId:"--a98-rgb-linear",name:"Linear Adobe® 98 RGB compatible",white:"D65",toXYZ_M:[[.5766690429101305,.1855582379065463,.1882286462349947],[.29734497525053605,.6273635662554661,.07529145849399788],[.02703136138641234,.07068885253582723,.9913375368376388]],fromXYZ_M:[[2.0415879038107465,-.5650069742788596,-.34473135077832956],[-.9692436362808795,1.8759675015077202,.04155505740717557],[.013444280632031142,-.11836239223101838,1.0151749943912054]]}),Kt=new N({id:"a98rgb",cssId:"a98-rgb",name:"Adobe® 98 RGB compatible",base:Ut,toBase:e=>e.map((e=>Math.pow(Math.abs(e),563/256)*Math.sign(e))),fromBase:e=>e.map((e=>Math.pow(Math.abs(e),256/563)*Math.sign(e)))});var Vt=new N({id:"prophoto-linear",cssId:"--prophoto-rgb-linear",name:"Linear ProPhoto",white:"D50",base:_,toXYZ_M:[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],fromXYZ_M:[[1.3457868816471583,-.25557208737979464,-.05110186497554526],[-.5446307051249019,1.5082477428451468,.02052744743642139],[0,0,1.2119675456389452]]});var er=new N({id:"prophoto",cssId:"prophoto-rgb",name:"ProPhoto",base:Vt,toBase:e=>e.map((e=>e<.03125?e/16:e**1.8)),fromBase:e=>e.map((e=>e>=.001953125?e**(1/1.8):16*e))}),tr=new C({id:"oklch",name:"Oklch",coords:{l:{refRange:[0,1],name:"Lightness"},c:{refRange:[0,.4],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},white:"D65",base:J,fromBase(e){let t,[r,a,o]=e;const n=2e-4;return t=Math.abs(a)<n&&Math.abs(o)<n?NaN:180*Math.atan2(o,a)/Math.PI,[r,Math.sqrt(a**2+o**2),$(t)]},toBase(e){let t,r,[a,o,n]=e;return isNaN(n)?(t=0,r=0):(t=o*Math.cos(n*Math.PI/180),r=o*Math.sin(n*Math.PI/180)),[a,t,r]},formats:{oklch:{coords:["<percentage> | <number>","<number> | <percentage>[0,1]","<number> | <angle>"]}}});let rr=d.D65;const ar=24389/27,[or,nr]=Ot({space:B,coords:rr});var sr=new C({id:"luv",name:"Luv",coords:{l:{refRange:[0,100],name:"Lightness"},u:{refRange:[-215,215]},v:{refRange:[-215,215]}},white:rr,base:B,fromBase(e){let t=[n(e[0]),n(e[1]),n(e[2])],r=t[1],[a,o]=Ot({space:B,coords:t});if(!Number.isFinite(a)||!Number.isFinite(o))return[0,0,0];let s=r<=.008856451679035631?ar*r:116*Math.cbrt(r)-16;return[s,13*s*(a-or),13*s*(o-nr)]},toBase(e){let[t,r,a]=e;if(0===t||o(t))return[0,0,0];r=n(r),a=n(a);let s=r/(13*t)+or,i=a/(13*t)+nr,c=t<=8?t/ar:Math.pow((t+16)/116,3);return[c*(9*s/(4*i)),c,c*((12-3*s-20*i)/(4*i))]},formats:{color:{id:"--luv",coords:["<number> | <percentage>","<number> | <percentage>[-1,1]","<number> | <percentage>[-1,1]"]}}}),ir=new C({id:"lchuv",name:"LChuv",coords:{l:{refRange:[0,100],name:"Lightness"},c:{refRange:[0,220],name:"Chroma"},h:{refRange:[0,360],type:"angle",name:"Hue"}},base:sr,fromBase(e){let t,[r,a,o]=e;return t=Math.abs(a)<.02&&Math.abs(o)<.02?NaN:180*Math.atan2(o,a)/Math.PI,[r,Math.sqrt(a**2+o**2),$(t)]},toBase(e){let[t,r,a]=e;return r<0&&(r=0),isNaN(a)&&(a=0),[t,r*Math.cos(a*Math.PI/180),r*Math.sin(a*Math.PI/180)]},formats:{color:{id:"--lchuv",coords:["<number> | <percentage>","<number> | <percentage>","<number> | <angle>"]}}});const cr=wt[0][0],lr=wt[0][1],ur=wt[0][2],hr=wt[1][0],pr=wt[1][1],mr=wt[1][2],dr=wt[2][0],fr=wt[2][1],gr=wt[2][2];function br(e,t,r){const a=t/(Math.sin(r)-e*Math.cos(r));return a<0?1/0:a}function Mr(e){const t=Math.pow(e+16,3)/1560896,r=t>.008856451679035631?t:e/903.2962962962963,a=r*(284517*cr-94839*ur),o=r*(838422*ur+769860*lr+731718*cr),n=r*(632260*ur-126452*lr),s=r*(284517*hr-94839*mr),i=r*(838422*mr+769860*pr+731718*hr),c=r*(632260*mr-126452*pr),l=r*(284517*dr-94839*gr),u=r*(838422*gr+769860*fr+731718*dr),h=r*(632260*gr-126452*fr);return{r0s:a/n,r0i:o*e/n,r1s:a/(n+126452),r1i:(o-769860)*e/(n+126452),g0s:s/c,g0i:i*e/c,g1s:s/(c+126452),g1i:(i-769860)*e/(c+126452),b0s:l/h,b0i:u*e/h,b1s:l/(h+126452),b1i:(u-769860)*e/(h+126452)}}function wr(e,t){const r=t/360*Math.PI*2,a=br(e.r0s,e.r0i,r),o=br(e.r1s,e.r1i,r),n=br(e.g0s,e.g0i,r),s=br(e.g1s,e.g1i,r),i=br(e.b0s,e.b0i,r),c=br(e.b1s,e.b1i,r);return Math.min(a,o,n,s,i,c)}var yr=new C({id:"hsluv",name:"HSLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:ir,gamutSpace:Rt,fromBase(e){let t,[r,a,o]=[n(e[0]),n(e[1]),n(e[2])];if(r>99.9999999)t=0,r=100;else if(r<1e-8)t=0,r=0;else{t=a/wr(Mr(r),o)*100}return[o,t,r]},toBase(e){let t,[r,a,o]=[n(e[0]),n(e[1]),n(e[2])];if(o>99.9999999)o=100,t=0;else if(o<1e-8)o=0,t=0;else{t=wr(Mr(o),r)/100*a}return[o,t,r]},formats:{color:{id:"--hsluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});function xr(e,t){return Math.abs(t)/Math.sqrt(Math.pow(e,2)+1)}function Cr(e){let t=xr(e.r0s,e.r0i),r=xr(e.r1s,e.r1i),a=xr(e.g0s,e.g0i),o=xr(e.g1s,e.g1i),n=xr(e.b0s,e.b0i),s=xr(e.b1s,e.b1i);return Math.min(t,r,a,o,n,s)}wt[0][0],wt[0][1],wt[0][2],wt[1][0],wt[1][1],wt[1][2],wt[2][0],wt[2][1],wt[2][2];var vr=new C({id:"hpluv",name:"HPLuv",coords:{h:{refRange:[0,360],type:"angle",name:"Hue"},s:{range:[0,100],name:"Saturation"},l:{range:[0,100],name:"Lightness"}},base:ir,gamutSpace:"self",fromBase(e){let t,[r,a,o]=[n(e[0]),n(e[1]),n(e[2])];if(r>99.9999999)t=0,r=100;else if(r<1e-8)t=0,r=0;else{t=a/Cr(Mr(r))*100}return[o,t,r]},toBase(e){let t,[r,a,o]=[n(e[0]),n(e[1]),n(e[2])];if(o>99.9999999)o=100,t=0;else if(o<1e-8)o=0,t=0;else{t=Cr(Mr(o))/100*a}return[o,t,r]},formats:{color:{id:"--hpluv",coords:["<number> | <angle>","<percentage> | <number>","<percentage> | <number>"]}}});const Rr=2610/16384,Br=32/2523,Nr=.8359375,kr=2413/128,Er=18.6875;var Sr=new N({id:"rec2100pq",cssId:"rec2100-pq",name:"REC.2100-PQ",base:dt,toBase:e=>e.map((function(e){return 1e4*(Math.max(e**Br-Nr,0)/(kr-Er*e**Br))**6.277394636015326/203})),fromBase:e=>e.map((function(e){let t=Math.max(203*e/1e4,0);return((Nr+kr*t**Rr)/(1+Er*t**Rr))**78.84375}))});const Lr=.17883277,_r=.28466892,Ar=.55991073,Ir=3.7743;var zr=new N({id:"rec2100hlg",cssId:"rec2100-hlg",name:"REC.2100-HLG",referred:"scene",base:dt,toBase:e=>e.map((function(e){return e<=.5?e**2/3*Ir:(Math.exp((e-Ar)/Lr)+_r)/12*Ir})),fromBase:e=>e.map((function(e){return(e/=Ir)<=1/12?Math.sqrt(3*e):Lr*Math.log(12*e-_r)+Ar}))});const Pr={};function $r({id:e,toCone_M:t,fromCone_M:r}){Pr[e]=arguments[0]}function Dr(t,r,a="Bradford"){let o=Pr[a],[n,s,i]=e(o.toCone_M,t),[c,l,u]=e(o.toCone_M,r),h=e([[c/n,0,0],[0,l/s,0],[0,0,u/i]],o.toCone_M);return e(o.fromCone_M,h)}m.add("chromatic-adaptation-start",(e=>{e.options.method&&(e.M=Dr(e.W1,e.W2,e.options.method))})),m.add("chromatic-adaptation-end",(e=>{e.M||(e.M=Dr(e.W1,e.W2,e.options.method))})),$r({id:"von Kries",toCone_M:[[.40024,.7076,-.08081],[-.2263,1.16532,.0457],[0,0,.91822]],fromCone_M:[[1.8599363874558397,-1.1293816185800916,.21989740959619328],[.3611914362417676,.6388124632850422,-6370596838649899e-21],[0,0,1.0890636230968613]]}),$r({id:"Bradford",toCone_M:[[.8951,.2664,-.1614],[-.7502,1.7135,.0367],[.0389,-.0685,1.0296]],fromCone_M:[[.9869929054667121,-.14705425642099013,.15996265166373122],[.4323052697233945,.5183602715367774,.049291228212855594],[-.00852866457517732,.04004282165408486,.96848669578755]]}),$r({id:"CAT02",toCone_M:[[.7328,.4296,-.1624],[-.7036,1.6975,.0061],[.003,.0136,.9834]],fromCone_M:[[1.0961238208355142,-.27886900021828726,.18274517938277307],[.4543690419753592,.4735331543074117,.07209780371722911],[-.009627608738429355,-.00569803121611342,1.0153256399545427]]}),$r({id:"CAT16",toCone_M:[[.401288,.650173,-.051461],[-.250268,1.204414,.045854],[-.002079,.048952,.953127]],fromCone_M:[[1.862067855087233,-1.0112546305316845,.14918677544445172],[.3875265432361372,.6214474419314753,-.008973985167612521],[-.01584149884933386,-.03412293802851557,1.0499644368778496]]}),Object.assign(d,{A:[1.0985,1,.35585],C:[.98074,1,1.18232],D55:[.95682,1,.92149],D75:[.94972,1,1.22638],E:[1,1,1],F2:[.99186,1,.67393],F7:[.95041,1,1.08747],F11:[1.00962,1,.6435]}),d.ACES=[.32168/.33767,1,.34065/.33767];var qr=new N({id:"acescg",cssId:"--acescg",name:"ACEScg",coords:{r:{range:[0,65504],name:"Red"},g:{range:[0,65504],name:"Green"},b:{range:[0,65504],name:"Blue"}},referred:"scene",white:d.ACES,toXYZ_M:[[.6624541811085053,.13400420645643313,.1561876870049078],[.27222871678091454,.6740817658111484,.05368951740793705],[-.005574649490394108,.004060733528982826,1.0103391003129971]],fromXYZ_M:[[1.6410233796943257,-.32480329418479,-.23642469523761225],[-.6636628587229829,1.6153315916573379,.016756347685530137],[.011721894328375376,-.008284441996237409,.9883948585390215]]});const Hr=2**-16,jr=-.35828683,Wr=(Math.log2(65504)+9.72)/17.52;var Tr=new N({id:"acescc",cssId:"--acescc",name:"ACEScc",coords:{r:{range:[jr,Wr],name:"Red"},g:{range:[jr,Wr],name:"Green"},b:{range:[jr,Wr],name:"Blue"}},referred:"scene",base:qr,toBase:e=>e.map((function(e){return e<=-.3013698630136986?2*(2**(17.52*e-9.72)-Hr):e<Wr?2**(17.52*e-9.72):65504})),fromBase:e=>e.map((function(e){return e<=0?(Math.log2(Hr)+9.72)/17.52:e<Hr?(Math.log2(Hr+.5*e)+9.72)/17.52:(Math.log2(e)+9.72)/17.52}))});exports.A98RGB=Kt,exports.A98RGB_Linear=Ut,exports.ACEScc=Tr,exports.ACEScg=qr,exports.CAM16_JMh=Fe,exports.ColorSpace=C,exports.HCT=rt,exports.HPLuv=vr,exports.HSL=Jt,exports.HSLuv=yr,exports.HSV=Ft,exports.HWB=Qt,exports.Hooks=p,exports.ICTCP=_e,exports.JzCzHz=Me,exports.Jzazbz=be,exports.LCH=D,exports.LCHuv=ir,exports.Lab=P,exports.Lab_D65=jt,exports.Luv=sr,exports.OKLCH=tr,exports.OKLab=J,exports.P3=Bt,exports.P3_Linear=Mt,exports.ProPhoto=er,exports.ProPhoto_Linear=Vt,exports.REC_2020=bt,exports.REC_2020_Linear=dt,exports.REC_2100_HLG=zr,exports.REC_2100_PQ=Sr,exports.RGBColorSpace=N,exports.XYZ_ABS_D65=ae,exports.XYZ_D50=_,exports.XYZ_D65=B,exports.clone=K,exports.contrast=function(e,r,a={}){t(a)&&(a={algorithm:a});let{algorithm:o,...n}=a;if(!o){let e=Object.keys(Gt).map((e=>e.replace(/^contrast/,""))).join(", ");throw new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${e}`)}e=x(e),r=x(r);for(let t in Gt)if("contrast"+o.toLowerCase()===t.toLowerCase())return Gt[t](e,r,n);throw new TypeError(`Unknown contrast algorithm: ${o}`)},exports.contrastAPCA=It,exports.contrastDeltaPhi=Tt,exports.contrastLstar=$t,exports.contrastMichelson=zt,exports.contrastWCAG21=Et,exports.contrastWeber=Pt,exports.darken=function(e,t=.25){return L(e,[C.get("oklch","lch"),"l"],(e=>e*(1-t)))},exports.defaults=b,exports.deltaE=Xt,exports.deltaE2000=G,exports.deltaE76=ee,exports.deltaECMC=re,exports.deltaEHCT=st,exports.deltaEITP=Ae,exports.deltaEJz=we,exports.deltaEMethods=it,exports.deltaEOK=F,exports.display=function(e,{space:t=b.display_space,...r}={}){let a=mt(e,r);if("undefined"==typeof CSS||CSS.supports("color",a)||!b.display_space)a=new String(a),a.color=e;else{let s=e;if((e.coords.some(o)||o(e.alpha))&&!(Nt??=CSS.supports("color","hsl(none 50% 50%)"))&&(s=K(e),s.coords=s.coords.map(n),s.alpha=n(s.alpha),a=mt(s,r),CSS.supports("color",a)))return a=new String(a),a.color=s,a;s=pt(s,t),a=new String(mt(s,r)),a.color=s}return a},exports.distance=V,exports.equals=function(e,t){return e=x(e),t=x(t),e.space===t.space&&e.alpha===t.alpha&&e.coords.every(((e,r)=>e===t.coords[r]))},exports.get=E,exports.getAll=k,exports.getColor=x,exports.getLuminance=kt,exports.hooks=m,exports.inGamut=U,exports.isRange=Zt,exports.lighten=function(e,t=.25){return L(e,[C.get("oklch","lch"),"l"],(e=>e*(1+t)))},exports.mix=function(e,t,a=.5,o={}){return[e,t]=[x(e),x(t)],"object"===r(a)&&([a,o]=[.5,a]),Yt(e,t,o)(a)},exports.parse=y,exports.range=Yt,exports.sRGB=Rt,exports.sRGB_Linear=yt,exports.serialize=mt,exports.set=L,exports.setAll=S,exports.setLuminance=function(e,t){L(e,[B,"y"],t)},exports.steps=function(e,t,r={}){let a;Zt(e)&&([a,r]=[e,t],[e,t]=a.rangeArgs.colors);let{maxDeltaE:o,deltaEMethod:n,steps:s=2,maxSteps:i=1e3,...c}=r;a||([e,t]=[x(e),x(t)],a=Yt(e,t,c));let l=Xt(e,t),u=o>0?Math.max(s,Math.ceil(l/o)+1):s,h=[];if(void 0!==i&&(u=Math.min(u,i)),1===u)h=[{p:.5,color:a(.5)}];else{let e=1/(u-1);h=Array.from({length:u},((t,r)=>{let o=r*e;return{p:o,color:a(o)}}))}if(o>0){let e=h.reduce(((e,t,r)=>{if(0===r)return 0;let a=Xt(t.color,h[r-1].color,n);return Math.max(e,a)}),0);for(;e>o;){e=0;for(let t=1;t<h.length&&h.length<i;t++){let r=h[t-1],o=h[t],n=(o.p+r.p)/2,s=a(n);e=Math.max(e,Xt(s,r.color),Xt(s,o.color)),h.splice(t,0,{p:n,color:a(n)}),t++}}}return h=h.map((e=>e.color)),h},exports.to=pt,exports.toGamut=lt,exports.toGamutCSS=ht,exports.uv=Ot,exports.xy=function(e){let[t,r,a]=k(e,B),o=t+r+a;return[t/o,r/o]};
//# sourceMappingURL=color-fn.min.cjs.map
