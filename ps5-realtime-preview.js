const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const {
  PS5_CONFIG,
  makePS5Traits
} = require('./ps5-advanced-nft-generator');
const { createPS5Art } = require('./ps5-production-system');

// PS5 Real-time Preview Configuration
const PREVIEW_CONFIG = {
  WIDTH: 1024,  // Preview resolution for speed
  HEIGHT: 1024,
  PREVIEW_DIR: path.join(__dirname, 'ps5-previews'),
  ANIMATION_FRAMES: 60,
  INTERACTIVE_MODE: true,
  REAL_TIME_GENERATION: true
};

// PS5 Interactive Preview System
class PS5PreviewSystem {
  constructor() {
    this.currentSeed = Math.floor(Math.random() * 1e9);
    this.currentTraits = null;
    this.animationFrame = 0;
    this.isAnimating = false;
    this.previewCache = new Map();
    this.ensurePreviewDir();
  }

  ensurePreviewDir() {
    if (!fs.existsSync(PREVIEW_CONFIG.PREVIEW_DIR)) {
      fs.mkdirSync(PREVIEW_CONFIG.PREVIEW_DIR, { recursive: true });
    }
  }

  // Gerçek zamanlı önizleme oluştur
  generateRealtimePreview(customTraits = null) {
    console.log('🎮 PS5 Gerçek Zamanlı Önizleme Oluşturuluyor...');
    
    const seed = customTraits ? customTraits.seed : this.currentSeed;
    const traits = customTraits || makePS5Traits(seed);
    this.currentTraits = traits;
    
    // Önizleme için optimize edilmiş traits
    const previewTraits = this.optimizeForPreview(traits);
    
    try {
      // Hızlı önizleme canvas'ı oluştur
      const canvas = this.createPreviewCanvas(seed, previewTraits);
      
      // Önizleme dosyasını kaydet
      const previewPath = path.join(PREVIEW_CONFIG.PREVIEW_DIR, `preview_${seed}.png`);
      const buffer = canvas.toBuffer('image/png');
      fs.writeFileSync(previewPath, buffer);
      
      console.log(`✅ Önizleme oluşturuldu: ${previewPath}`);
      console.log(`🎨 Spiral: ${traits.spiralType}`);
      console.log(`✨ Efekt: ${traits.effect}`);
      console.log(`🎨 Palet: ${traits.palette}`);
      
      return {
        seed: seed,
        traits: traits,
        previewPath: previewPath,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ Önizleme oluşturulurken hata:', error.message);
      return null;
    }
  }

  // Önizleme için optimize edilmiş traits
  optimizeForPreview(traits) {
    return {
      ...traits,
      rings: Math.min(traits.rings, 300), // Hız için ring sayısını azalt
      steps: Math.min(traits.steps, 50),   // Step sayısını azalt
      neuralComplexity: Math.min(traits.neuralComplexity, 4), // Karmaşıklığı azalt
      antiAliasing: 2, // AA'yı azalt
      hdrIntensity: Math.min(traits.hdrIntensity, 1.0) // HDR'ı sınırla
    };
  }

  // Hızlı önizleme canvas'ı oluştur
  createPreviewCanvas(seed, traits) {
    const canvas = createCanvas(PREVIEW_CONFIG.WIDTH, PREVIEW_CONFIG.HEIGHT);
    const ctx = canvas.getContext('2d');
    
    // Hızlı rendering için basitleştirilmiş çizim
    return this.drawSimplifiedArt(canvas, ctx, seed, traits);
  }

  // Basitleştirilmiş sanat çizimi
  drawSimplifiedArt(canvas, ctx, seed, traits) {
    const { PS5SeededRandom, PS5_HDR_PALETTES } = require('./ps5-advanced-nft-generator');
    const rng = new PS5SeededRandom(seed);
    const pal = PS5_HDR_PALETTES.find(p => p.name === traits.palette);
    
    // Arka plan
    ctx.fillStyle = pal.bg;
    ctx.fillRect(0, 0, PREVIEW_CONFIG.WIDTH, PREVIEW_CONFIG.HEIGHT);
    
    // Merkez
    const centerX = PREVIEW_CONFIG.WIDTH * 0.5;
    const centerY = PREVIEW_CONFIG.HEIGHT * 0.5;
    
    ctx.save();
    ctx.translate(centerX, centerY);
    
    // Basitleştirilmiş spiral çizimi
    this.drawSimplifiedSpiral(ctx, traits, pal, rng);
    
    // Basitleştirilmiş efekt
    this.drawSimplifiedEffect(ctx, traits, pal, rng);
    
    ctx.restore();
    
    return canvas;
  }

  // Basitleştirilmiş spiral çizimi
  drawSimplifiedSpiral(ctx, traits, pal, rng) {
    const { PS5SpiralCalculators } = require('./ps5-advanced-nft-generator');
    
    // Spiral fonksiyonu seç
    let spiralFunc;
    switch(traits.spiralType) {
      case 'Quantum Fibonacci':
        spiralFunc = PS5SpiralCalculators.quantumFibonacci;
        break;
      case 'Neural Network Spiral':
        spiralFunc = PS5SpiralCalculators.neuralNetworkSpiral;
        break;
      case 'Fractal Mandelbrot Spiral':
        spiralFunc = PS5SpiralCalculators.fractalMandelbrotSpiral;
        break;
      default:
        spiralFunc = PS5SpiralCalculators.quantumFibonacci;
    }
    
    // Hızlı spiral çizimi
    ctx.beginPath();
    let firstPoint = true;
    
    for (let i = 0; i < traits.rings; i += 2) { // Her 2. noktayı atla
      const t = i * traits.steps;
      const coords = spiralFunc(t, traits, rng);
      
      const x = coords.r * Math.cos(coords.theta) * 0.8; // Önizleme için küçült
      const y = coords.r * Math.sin(coords.theta) * 0.8;
      
      if (firstPoint) {
        ctx.moveTo(x, y);
        firstPoint = false;
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    // Basit renklendirme
    const hue = pal.hues[0];
    ctx.strokeStyle = `hsla(${hue}, 70%, 60%, ${traits.alpha})`;
    ctx.lineWidth = traits.strokeW;
    ctx.stroke();
  }

  // Basitleştirilmiş efekt çizimi
  drawSimplifiedEffect(ctx, traits, pal, rng) {
    switch(traits.effect) {
      case 'Volumetric Lighting':
        this.drawSimpleLighting(ctx, pal, rng);
        break;
      case 'Particle Physics Simulation':
        this.drawSimpleParticles(ctx, pal, rng);
        break;
      case 'Holographic Interference':
        this.drawSimpleInterference(ctx, pal, rng);
        break;
      default:
        // Basit glow efekti
        this.drawSimpleGlow(ctx, pal);
    }
  }

  drawSimpleLighting(ctx, pal, rng) {
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 200);
    const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
    gradient.addColorStop(0, `hsla(${hue}, 70%, 70%, 0.2)`);
    gradient.addColorStop(1, `hsla(${hue}, 70%, 30%, 0)`);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(-200, -200, 400, 400);
  }

  drawSimpleParticles(ctx, pal, rng) {
    for (let i = 0; i < 20; i++) {
      const x = rng.randomRange(-200, 200);
      const y = rng.randomRange(-200, 200);
      const size = rng.randomRange(2, 6);
      const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
      
      ctx.fillStyle = `hsla(${hue}, 80%, 60%, 0.6)`;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    }
  }

  drawSimpleInterference(ctx, pal, rng) {
    for (let i = 0; i < 10; i++) {
      const angle = (i / 10) * Math.PI * 2;
      const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
      
      ctx.strokeStyle = `hsla(${hue}, 60%, 50%, 0.3)`;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(0, 0);
      ctx.lineTo(Math.cos(angle) * 150, Math.sin(angle) * 150);
      ctx.stroke();
    }
  }

  drawSimpleGlow(ctx, pal) {
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 100);
    const hue = pal.hues[0];
    gradient.addColorStop(0, `hsla(${hue}, 80%, 80%, 0.1)`);
    gradient.addColorStop(1, `hsla(${hue}, 80%, 40%, 0)`);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(-100, -100, 200, 200);
  }

  // Animasyonlu önizleme oluştur
  generateAnimatedPreview(duration = 3000) {
    console.log('🎬 PS5 Animasyonlu Önizleme Oluşturuluyor...');
    
    const frames = [];
    const frameCount = PREVIEW_CONFIG.ANIMATION_FRAMES;
    const seed = this.currentSeed;
    
    for (let frame = 0; frame < frameCount; frame++) {
      const progress = frame / frameCount;
      const animatedTraits = this.createAnimatedTraits(seed, progress);
      
      const canvas = this.createPreviewCanvas(seed, animatedTraits);
      frames.push(canvas.toBuffer('image/png'));
      
      console.log(`📽️  Frame ${frame + 1}/${frameCount} oluşturuldu`);
    }
    
    // GIF oluştur (basit implementasyon)
    const gifPath = path.join(PREVIEW_CONFIG.PREVIEW_DIR, `animated_preview_${seed}.gif`);
    this.createSimpleGIF(frames, gifPath);
    
    console.log(`✅ Animasyonlu önizleme oluşturuldu: ${gifPath}`);
    return gifPath;
  }

  // Animasyon için traits oluştur
  createAnimatedTraits(seed, progress) {
    const baseTraits = makePS5Traits(seed);
    
    return {
      ...baseTraits,
      phaseShift: progress * Math.PI * 2,
      amplitudeModulation: 1.0 + Math.sin(progress * Math.PI * 4) * 0.2,
      hdrIntensity: 0.8 + Math.sin(progress * Math.PI * 2) * 0.3,
      rings: Math.floor(baseTraits.rings * (0.8 + progress * 0.4))
    };
  }

  // Basit GIF oluşturucu
  createSimpleGIF(frames, outputPath) {
    // Bu basit bir implementasyon - gerçek GIF için gifenc kullanılabilir
    console.log(`📁 ${frames.length} frame GIF olarak kaydediliyor...`);
    // Şimdilik ilk frame'i kaydet
    fs.writeFileSync(outputPath.replace('.gif', '.png'), frames[0]);
  }

  // Yeni seed ile önizleme
  regeneratePreview() {
    this.currentSeed = Math.floor(Math.random() * 1e9);
    return this.generateRealtimePreview();
  }

  // Mevcut traits'i modifiye et
  modifyCurrentTraits(modifications) {
    if (!this.currentTraits) {
      console.log('⚠️  Önce bir önizleme oluşturun');
      return null;
    }
    
    const modifiedTraits = { ...this.currentTraits, ...modifications };
    return this.generateRealtimePreview(modifiedTraits);
  }
}

// CLI kullanımı
function runPreviewCLI() {
  const preview = new PS5PreviewSystem();
  
  console.log('🎮 PS5 Gerçek Zamanlı Önizleme Sistemi');
  console.log('=====================================');
  
  // Komut satırı argümanlarını kontrol et
  const args = process.argv.slice(2);
  
  if (args.includes('--animate')) {
    preview.generateAnimatedPreview();
  } else if (args.includes('--regenerate')) {
    const count = parseInt(args[args.indexOf('--regenerate') + 1]) || 5;
    console.log(`🔄 ${count} adet önizleme oluşturuluyor...`);
    
    for (let i = 0; i < count; i++) {
      preview.regeneratePreview();
    }
  } else {
    // Tek önizleme oluştur
    preview.generateRealtimePreview();
  }
}

// CLI kullanımı
if (require.main === module) {
  runPreviewCLI();
}

module.exports = { PS5PreviewSystem, PREVIEW_CONFIG };
