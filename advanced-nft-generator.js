const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const {
  ADVANCED_SPIRAL_TYPES,
  ADVANCED_EFFECTS,
  ENHANCED_PALETTES,
  AdvancedSeededRandom,
  SpiralCalculators,
  AdvancedEffects,
  weightedPick,
  drawAdvancedSpiral,
  applyAdvancedEffect
} = require('./advanced-spiral-generator');

// Konfigürasyon
const CONFIG = {
  OUTPUT_DIR: path.join(__dirname, 'advanced-output'),
  NFT_COUNT: 20,
  WIDTH: 3000,
  HEIGHT: 4000
};

function ensureDir(dir) {
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

// Gelişmiş traits oluşturucu
function makeAdvancedTraits(seed) {
  const rng = new AdvancedSeededRandom(seed);
  
  const pal = weightedPick(ENHANCED_PALETTES, () => rng.random());
  const spiralType = weightedPick(ADVANCED_SPIRAL_TYPES, () => rng.random());
  const effect = weightedPick(ADVANCED_EFFECTS, () => rng.random());
  
  return {
    seed: seed,
    palette: pal.name,
    spiralType: spiralType.name,
    effect: effect.name,
    rings: rng.random() < 0.3 ? rng.randomInt(480,520) : rng.randomInt(360,380),
    steps: rng.randomInt(70,110),
    wobble: rng.random() < 0.2 ? rng.randomRange(0.14,0.16) : rng.randomRange(0.08,0.12),
    flowFreqR: rng.randomRange(0.015,0.028),
    flowFreqI: rng.randomRange(0.015,0.028),
    swirlOffset: rng.randomRange(0.42,0.62),
    strokeW: rng.random() < 0.3 ? rng.randomRange(2.0,2.6) : rng.randomRange(1.4,1.8),
    alpha: rng.randomRange(0.28,0.52),
    // Gelişmiş özellikler
    gradientIntensity: rng.randomRange(0.1, 0.8),
    particleCount: effect.name.includes('Particle') ? rng.randomInt(50, 300) : 0,
    geometricDensity: effect.name === 'Geometric' ? rng.randomRange(0.1, 0.4) : 0,
    textureScale: effect.name === 'Texture' ? rng.randomRange(0.005, 0.02) : 0,
    spiralTightness: getSpiralTightness(spiralType.name, rng),
    centerOffset: {x: rng.randomRange(-0.1, 0.1), y: rng.randomRange(-0.1, 0.1)},
    // Yeni gelişmiş özellikler
    complexity: rng.randomRange(0.3, 1.0),
    harmonics: rng.randomInt(1, 5),
    phaseShift: rng.randomRange(0, Math.PI * 2),
    amplitudeModulation: rng.randomRange(0.5, 1.5),
    frequencyModulation: rng.randomRange(0.8, 1.2)
  };
}

function getSpiralTightness(spiralType, rng) {
  switch(spiralType) {
    case 'Fibonacci':
      return rng.randomRange(0.1, 0.3);
    case 'Logarithmic':
      return rng.randomRange(0.05, 0.15);
    case 'Hyperbolic':
      return rng.randomRange(0.8, 1.5);
    case 'Fermat':
      return rng.randomRange(0.2, 0.6);
    case 'Dual Spiral':
      return rng.randomRange(0.3, 0.8);
    case 'Triple Spiral':
      return rng.randomRange(0.2, 0.5);
    case 'Nested Spirals':
      return rng.randomRange(0.4, 1.0);
    case 'Interference Pattern':
      return rng.randomRange(0.6, 1.2);
    default:
      return rng.randomRange(0.8, 1.2);
  }
}

// Gelişmiş çizim fonksiyonu
function drawAdvancedArt(seed, traits) {
  const canvas = createCanvas(CONFIG.WIDTH, CONFIG.HEIGHT);
  const ctx = canvas.getContext('2d');
  const rng = new AdvancedSeededRandom(seed);
  const pal = ENHANCED_PALETTES.find(p => p.name === traits.palette);
  
  // Arka plan
  ctx.fillStyle = pal.bg;
  ctx.fillRect(0, 0, CONFIG.WIDTH, CONFIG.HEIGHT);
  
  // Merkez noktası
  const centerX = CONFIG.WIDTH * (0.5 + traits.centerOffset.x);
  const centerY = CONFIG.HEIGHT * (traits.swirlOffset + traits.centerOffset.y);
  
  ctx.save();
  ctx.translate(centerX, centerY);
  
  // Arka plan efektleri (önce)
  if (traits.effect === 'Gradient Flow') {
    applyAdvancedEffect(ctx, traits, pal, rng, centerX, centerY);
  }
  
  // Ana spiral çizimi
  drawAdvancedSpiral(ctx, traits, pal, rng);
  
  // Ön plan efektleri (sonra)
  if (['Wave Distortion', 'Fractal Overlay', 'Light Rays', 'Particle Storm'].includes(traits.effect)) {
    applyAdvancedEffect(ctx, traits, pal, rng, centerX, centerY);
  }
  
  // Klasik efektler (geriye uyumluluk)
  if (traits.effect === 'Particles') {
    drawParticles(ctx, traits, pal, rng);
  } else if (traits.effect === 'Geometric') {
    drawGeometricElements(ctx, traits, pal, rng);
  } else if (traits.effect === 'Texture') {
    drawTextureBackground(ctx, traits, rng);
  }
  
  ctx.restore();
  
  return canvas;
}

// Klasik efekt fonksiyonları (geriye uyumluluk)
function drawParticles(ctx, traits, pal, rng) {
  for (let i = 0; i < traits.particleCount; i++) {
    let angle = rng.random() * Math.PI * 2;
    let radius = rng.randomRange(50, traits.rings * 2);
    let x = radius * Math.cos(angle);
    let y = radius * Math.sin(angle);
    
    let size = rng.randomRange(1, 4);
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
    let alpha = rng.randomRange(0.1, 0.4);
    
    ctx.fillStyle = `hsla(${hue}, 70%, 60%, ${alpha})`;
    ctx.beginPath();
    ctx.arc(x, y, size/2, 0, Math.PI * 2);
    ctx.fill();
  }
}

function drawGeometricElements(ctx, traits, pal, rng) {
  ctx.lineWidth = 1;
  let elementCount = traits.geometricDensity * 100;
  
  for (let i = 0; i < elementCount; i++) {
    let angle = (i / elementCount) * Math.PI * 2;
    let radius = rng.randomRange(100, traits.rings * 1.5);
    let x = radius * Math.cos(angle);
    let y = radius * Math.sin(angle);
    
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
    ctx.strokeStyle = `hsla(${hue}, 50%, 50%, 0.3)`;
    
    let shapes = ['triangle', 'square', 'hexagon'];
    let shapeType = shapes[Math.floor(rng.random() * shapes.length)];
    drawGeometricShape(ctx, x, y, shapeType, rng.randomRange(5, 15), rng);
  }
}

function drawGeometricShape(ctx, x, y, type, size, rng) {
  ctx.save();
  ctx.translate(x, y);
  ctx.rotate(rng.random() * Math.PI * 2);
  
  ctx.beginPath();
  switch(type) {
    case 'triangle':
      ctx.moveTo(-size/2, size/2);
      ctx.lineTo(size/2, size/2);
      ctx.lineTo(0, -size/2);
      ctx.closePath();
      break;
    case 'square':
      ctx.rect(-size/2, -size/2, size, size);
      break;
    case 'hexagon':
      for (let i = 0; i < 6; i++) {
        let angle = (i / 6) * Math.PI * 2;
        let px = size/2 * Math.cos(angle);
        let py = size/2 * Math.sin(angle);
        if (i === 0) ctx.moveTo(px, py);
        else ctx.lineTo(px, py);
      }
      ctx.closePath();
      break;
  }
  ctx.stroke();
  ctx.restore();
}

function drawTextureBackground(ctx, traits, rng) {
  const imageData = ctx.getImageData(-500, -500, 1000, 1000);
  const data = imageData.data;
  
  for (let x = 0; x < 1000; x += 4) {
    for (let y = 0; y < 1000; y += 4) {
      let noiseVal = rng.perlinNoise(x * traits.textureScale, y * traits.textureScale);
      let brightness = noiseVal * 30;
      
      let index = (x + y * 1000) * 4;
      if (index < data.length - 3) {
        data[index] += brightness;
        data[index + 1] += brightness;
        data[index + 2] += brightness;
      }
    }
  }
  
  ctx.putImageData(imageData, -500, -500);
}

// Gelişmiş rarity hesaplayıcı
function calculateAdvancedRarity(traits) {
  let score = 0;
  
  // Spiral türü nadirligi (gelişmiş)
  const spiralRarity = {
    'Classic': 10,
    'Fibonacci': 20,
    'Archimedean': 20,
    'Logarithmic': 30,
    'Hyperbolic': 35,
    'Fermat': 35,
    'Dual Spiral': 40,
    'Triple Spiral': 45,
    'Nested Spirals': 50,
    'Interference Pattern': 55
  };
  score += spiralRarity[traits.spiralType] || 10;
  
  // Efekt nadirligi (gelişmiş)
  const effectRarity = {
    'None': 5,
    'Particles': 15,
    'Geometric': 20,
    'Texture': 25,
    'Gradient Flow': 30,
    'Wave Distortion': 35,
    'Fractal Overlay': 40,
    'Light Rays': 45,
    'Particle Storm': 50
  };
  score += effectRarity[traits.effect] || 5;
  
  // Renk paleti nadirligi (gelişmiş)
  const paletteRarity = {
    'Muted Pastel': 10,
    'Nocturne': 10,
    'Beachglass': 10,
    'Sepia Silk': 25,
    'Cosmic Purple': 20,
    'Forest Dream': 25,
    'Sunset Blaze': 30,
    'Ocean Deep': 30,
    'Aurora': 35
  };
  score += paletteRarity[traits.palette] || 10;
  
  // Özel özellikler
  if (traits.rings > 500) score += 15;
  if (traits.wobble > 0.14) score += 10;
  if (traits.gradientIntensity > 0.7) score += 10;
  if (traits.strokeW > 2.0) score += 8;
  if (traits.complexity > 0.8) score += 12;
  if (traits.harmonics > 3) score += 8;
  
  return Math.min(score, 100);
}

// Gelişmiş metadata oluşturucu
function createAdvancedMetadata(seed, traits, rarityScore) {
  return {
    name: `Advanced Spiral #${seed}`,
    description: "Next-generation generative spiral art featuring advanced mathematical algorithms, complex spiral combinations, and sophisticated visual effects",
    image: `advanced_spiral_${seed}.png`,
    external_url: `https://your-website.com/advanced-spiral/${seed}`,
    attributes: [
      {trait_type: "Palette", value: traits.palette},
      {trait_type: "Spiral Type", value: traits.spiralType},
      {trait_type: "Effect", value: traits.effect},
      {trait_type: "Ring Count", value: traits.rings},
      {trait_type: "Complexity", value: parseFloat(traits.complexity.toFixed(2))},
      {trait_type: "Harmonics", value: traits.harmonics},
      {trait_type: "Stroke Weight", value: parseFloat(traits.strokeW.toFixed(2))},
      {trait_type: "Wobble Intensity", value: parseFloat(traits.wobble.toFixed(3))},
      {trait_type: "Gradient Intensity", value: parseFloat(traits.gradientIntensity.toFixed(2))},
      {trait_type: "Rarity Score", value: rarityScore, display_type: "number"}
    ],
    properties: {
      seed: seed,
      algorithm: "Advanced Spiral Generator v3.0",
      resolution: `${CONFIG.WIDTH}x${CONFIG.HEIGHT}`,
      created: new Date().toISOString(),
      generation: "Advanced",
      spiralComplexity: traits.spiralType.includes('Spiral') ? "Multi-Spiral" : "Single-Spiral"
    }
  };
}

// Ana üretim fonksiyonu
function generateAdvancedNFTs() {
  console.log('🚀 Gelişmiş NFT Üretimi Başlatılıyor...');
  console.log(`📊 Hedef: ${CONFIG.NFT_COUNT} Gelişmiş NFT`);
  console.log(`🎨 Spiral Türleri: ${ADVANCED_SPIRAL_TYPES.length}`);
  console.log(`✨ Efekt Türleri: ${ADVANCED_EFFECTS.length}`);
  console.log(`🎨 Renk Paletleri: ${ENHANCED_PALETTES.length}\n`);
  
  ensureDir(CONFIG.OUTPUT_DIR);
  
  for (let i = 0; i < CONFIG.NFT_COUNT; i++) {
    const seed = Math.floor(Math.random() * 1e9);
    const traits = makeAdvancedTraits(seed);
    
    try {
      // Sanat eseri oluştur
      const canvas = drawAdvancedArt(seed, traits);
      
      // Dosyaları kaydet
      const imgBuffer = canvas.toBuffer('image/png');
      fs.writeFileSync(path.join(CONFIG.OUTPUT_DIR, `advanced_spiral_${seed}.png`), imgBuffer);
      
      // Metadata oluştur
      const rarityScore = calculateAdvancedRarity(traits);
      const metadata = createAdvancedMetadata(seed, traits, rarityScore);
      fs.writeFileSync(path.join(CONFIG.OUTPUT_DIR, `advanced_spiral_${seed}.json`), JSON.stringify(metadata, null, 2));
      
      console.log(`✅ Gelişmiş NFT ${i+1}/${CONFIG.NFT_COUNT} üretildi: Advanced Spiral #${seed} (${traits.spiralType} + ${traits.effect}) - Rarity: ${rarityScore}`);
      
    } catch (error) {
      console.error(`❌ NFT ${i+1} üretilirken hata:`, error.message);
    }
  }
  
  console.log(`\n🎉 Gelişmiş NFT üretimi tamamlandı!`);
  console.log(`📁 Çıktılar: ${CONFIG.OUTPUT_DIR}`);
}

// CLI kullanımı
if (require.main === module) {
  generateAdvancedNFTs();
}

module.exports = { generateAdvancedNFTs, CONFIG };
