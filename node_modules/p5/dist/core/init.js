import { p as p5 } from '../main-rEhlsQtb.js';
import { initialize } from './internationalization.js';
import '../constants-C2DVjshm.js';
import './transform.js';
import './structure.js';
import './environment.js';
import '../math/p5.Vector.js';
import '../rendering-CpHn8PfG.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../dom/p5.Element.js';
import '../dom/p5.File.js';
import '../io/p5.XML.js';
import '../p5.Renderer-DO9wIL55.js';
import '../image/filters.js';
import '../shape/custom_shapes.js';
import './States.js';
import '../io/utilities.js';
import 'file-saver';
import '../dom/p5.MediaElement.js';
import '../shape/2d_primitives.js';
import './helpers.js';
import '../shape/attributes.js';
import '../shape/curves.js';
import '../shape/vertex.js';
import '../color/setting.js';
import 'omggif';
import '../io/csv.js';
import 'gifenc';
import '../image/pixels.js';
import '../webgl/GeometryBuilder.js';
import '../math/p5.Matrix.js';
import '../math/Matrices/Matrix.js';
import '../math/Matrices/MatrixInterface.js';
import '../webgl/p5.Geometry.js';
import '../webgl/p5.DataArray.js';
import '../webgl/p5.Quat.js';
import '../webgl/p5.RenderBuffer.js';
import '../webgl/ShapeBuilder.js';
import 'libtess';
import '../webgl/GeometryBufferCache.js';
import '../image/const.js';
import '../math/trigonometry.js';
import '../image/filterRenderer2D.js';
import 'i18next';
import 'i18next-browser-languagedetector';

/**
 * This file setup global mode automatic instantiation
 *
 * if sketch is on window
 * assume "global" mode
 * and instantiate p5 automatically
 * otherwise do nothing
 *
 * @private
 * @return {Undefined}
 */
const _globalInit = () => {
  // Could have been any property defined within the p5 constructor.
  // If that property is already a part of the global object,
  // this code has already run before, likely due to a duplicate import
  if (typeof window._setupDone !== 'undefined') {
    console.warn(
      'p5.js seems to have been imported multiple times. Please remove the duplicate import'
    );
    return;
  }

  if (!window.mocha) {
    const p5ReadyEvent = new Event('p5Ready');
    window.dispatchEvent(p5ReadyEvent);

    // If there is a setup or draw function on the window
    // then instantiate p5 in "global" mode
    if (
      ((window.setup && typeof window.setup === 'function') ||
        (window.draw && typeof window.draw === 'function')) &&
      !p5.instance
    ) {
      new p5();
    }
  }
};

// make a promise that resolves when the document is ready
const waitForDocumentReady = () =>
  new Promise((resolve, reject) => {
    // if the page is ready, initialize p5 immediately
    if (document.readyState === 'complete') {
      resolve();
      // if the page is still loading, add an event listener
      // and initialize p5 as soon as it finishes loading
    } else {
      window.addEventListener('load', resolve, false);
    }
  });

// only load translations if we're using the full, un-minified library
const waitingForTranslator =
  typeof IS_MINIFIED === 'undefined' ? initialize() :
    Promise.resolve();

export { _globalInit, waitForDocumentReady, waitingForTranslator };
