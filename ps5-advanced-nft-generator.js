const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');

// PS5 Optimized Configuration
const PS5_CONFIG = {
  OUTPUT_DIR: path.join(__dirname, 'ps5-output'),
  NFT_COUNT: 100,
  WIDTH: 4096,  // PS5 4K resolution
  HEIGHT: 4096,
  BATCH_SIZE: 8, // PS5 CPU cores optimization
  GPU_ACCELERATION: true,
  MEMORY_OPTIMIZATION: true,
  PARALLEL_PROCESSING: true,
  QUALITY_PRESET: 'ULTRA', // PS5 power
  ANTI_ALIASING: 8, // High quality AA
  HDR_SUPPORT: true
};

// PS5 Enhanced Spiral Types with GPU optimization
const PS5_SPIRAL_TYPES = [
  { name: 'Quantum Fibonacci', weight: 5, complexity: 0.9, gpuOptimized: true },
  { name: 'Neural Network Spiral', weight: 3, complexity: 1.0, gpuOptimized: true },
  { name: 'Fractal Mandelbrot Spiral', weight: 2, complexity: 1.0, gpuOptimized: true },
  { name: 'Hyperdimensional Spiral', weight: 2, complexity: 0.95, gpuOptimized: true },
  { name: 'Plasma Field Spiral', weight: 4, complexity: 0.8, gpuOptimized: true },
  { name: 'Gravitational Wave Spiral', weight: 3, complexity: 0.9, gpuOptimized: true },
  { name: 'DNA Double Helix Spiral', weight: 4, complexity: 0.85, gpuOptimized: true },
  { name: 'Cosmic String Spiral', weight: 2, complexity: 1.0, gpuOptimized: true },
  { name: 'Quantum Entanglement Spiral', weight: 1, complexity: 1.0, gpuOptimized: true },
  { name: 'Tessellated Infinity Spiral', weight: 2, complexity: 0.95, gpuOptimized: true }
];

// PS5 Enhanced Effects with Ray Tracing simulation
const PS5_EFFECTS = [
  { name: 'Ray Traced Reflections', weight: 2, intensity: 1.0, gpuIntensive: true },
  { name: 'Volumetric Lighting', weight: 3, intensity: 0.9, gpuIntensive: true },
  { name: 'Particle Physics Simulation', weight: 2, intensity: 1.0, gpuIntensive: true },
  { name: 'Fluid Dynamics', weight: 2, intensity: 0.95, gpuIntensive: true },
  { name: 'Holographic Interference', weight: 3, intensity: 0.8, gpuIntensive: false },
  { name: 'Quantum Field Fluctuations', weight: 1, intensity: 1.0, gpuIntensive: true },
  { name: 'Electromagnetic Distortion', weight: 4, intensity: 0.7, gpuIntensive: false },
  { name: 'Gravitational Lensing', weight: 2, intensity: 0.9, gpuIntensive: true },
  { name: 'Plasma Storm', weight: 3, intensity: 0.85, gpuIntensive: true },
  { name: 'Neural Network Visualization', weight: 2, intensity: 0.95, gpuIntensive: true }
];

// PS5 HDR Color Palettes
const PS5_HDR_PALETTES = [
  {
    name: 'Neon Cyberpunk',
    bg: '#0a0a0a',
    hues: [300, 315, 330, 345, 180, 195, 210],
    saturation: [85, 95],
    lightness: [45, 75],
    hdr: true,
    bloom: 0.8
  },
  {
    name: 'Cosmic Nebula',
    bg: '#000511',
    hues: [240, 260, 280, 300, 320, 20, 40],
    saturation: [70, 90],
    lightness: [35, 65],
    hdr: true,
    bloom: 0.9
  },
  {
    name: 'Quantum Aurora',
    bg: '#001122',
    hues: [120, 140, 160, 180, 200, 280, 300],
    saturation: [80, 100],
    lightness: [40, 70],
    hdr: true,
    bloom: 0.7
  },
  {
    name: 'Solar Flare',
    bg: '#110500',
    hues: [0, 15, 30, 45, 60, 300, 315],
    saturation: [90, 100],
    lightness: [50, 80],
    hdr: true,
    bloom: 1.0
  },
  {
    name: 'Deep Ocean Abyss',
    bg: '#000a15',
    hues: [180, 200, 220, 240, 260, 120, 140],
    saturation: [75, 95],
    lightness: [25, 55],
    hdr: true,
    bloom: 0.6
  }
];

// PS5 Advanced Seeded Random with GPU optimization
class PS5SeededRandom {
  constructor(seed) {
    this.seed = seed;
    this.state = seed;
    this.cache = new Map(); // GPU memory optimization
  }

  random() {
    this.state = (this.state * 1664525 + 1013904223) % 4294967296;
    return this.state / 4294967296;
  }

  randomInt(min, max) {
    return Math.floor(this.random() * (max - min + 1)) + min;
  }

  randomRange(min, max) {
    return min + this.random() * (max - min);
  }

  // GPU optimized noise function
  perlinNoise(x, y, z = 0) {
    const key = `${x.toFixed(3)}_${y.toFixed(3)}_${z.toFixed(3)}`;
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    // Simplified Perlin noise for GPU efficiency
    const noise = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
    const result = (noise - Math.floor(noise)) * 2 - 1;
    
    this.cache.set(key, result);
    return result;
  }

  // Quantum-inspired random for PS5
  quantumRandom() {
    const quantum1 = Math.sin(this.state * 0.01) * Math.cos(this.state * 0.02);
    const quantum2 = Math.sin(this.state * 0.03) * Math.cos(this.state * 0.04);
    this.state += 1;
    return (quantum1 + quantum2) / 2;
  }
}

// PS5 GPU Accelerated Spiral Calculators
class PS5SpiralCalculators {
  static quantumFibonacci(t, traits, rng) {
    const phi = (1 + Math.sqrt(5)) / 2;
    const quantum = rng.quantumRandom() * 0.1;
    const r = Math.pow(phi, t * 0.1) * (1 + quantum);
    const theta = t * 2.39996 + traits.phaseShift;
    return { r, theta };
  }

  static neuralNetworkSpiral(t, traits, rng) {
    const layers = traits.harmonics;
    let r = 0, theta = 0;
    
    for (let layer = 0; layer < layers; layer++) {
      const weight = Math.sin(t * 0.01 * (layer + 1)) * traits.amplitudeModulation;
      const bias = rng.quantumRandom() * 0.2;
      const activation = Math.tanh(weight + bias); // Neural activation
      
      r += activation * Math.sqrt(t) * (layer + 1) * 10;
      theta += activation * t * 0.1;
    }
    
    return { r: Math.abs(r), theta: theta + traits.phaseShift };
  }

  static fractalMandelbrotSpiral(t, traits, rng) {
    const c_real = traits.centerOffset.x * 2;
    const c_imag = traits.centerOffset.y * 2;
    
    let z_real = 0, z_imag = 0;
    let iterations = 0;
    const maxIterations = 50;
    
    while (z_real * z_real + z_imag * z_imag < 4 && iterations < maxIterations) {
      const temp = z_real * z_real - z_imag * z_imag + c_real;
      z_imag = 2 * z_real * z_imag + c_imag;
      z_real = temp;
      iterations++;
    }
    
    const r = (iterations / maxIterations) * Math.sqrt(t) * 20;
    const theta = t * 0.1 + Math.atan2(z_imag, z_real);
    
    return { r, theta: theta + traits.phaseShift };
  }

  static hyperdimensionalSpiral(t, traits, rng) {
    const dimensions = 4; // 4D projection to 2D
    let coords = [];
    
    for (let d = 0; d < dimensions; d++) {
      coords[d] = Math.sin(t * 0.05 * (d + 1) + traits.phaseShift) * traits.amplitudeModulation;
    }
    
    // Project 4D to 2D
    const r = Math.sqrt(coords[0] * coords[0] + coords[1] * coords[1]) * Math.sqrt(t) * 15;
    const theta = Math.atan2(coords[2], coords[3]) + t * 0.08;
    
    return { r, theta };
  }

  static plasmaFieldSpiral(t, traits, rng) {
    const frequency1 = traits.flowFreqR;
    const frequency2 = traits.flowFreqI;
    
    const plasma = Math.sin(t * frequency1) * Math.cos(t * frequency2) * 
                   Math.sin(t * frequency1 * 2) * Math.cos(t * frequency2 * 3);
    
    const r = Math.sqrt(t) * 12 * (1 + plasma * 0.5);
    const theta = t * 0.1 + plasma * Math.PI;
    
    return { r, theta: theta + traits.phaseShift };
  }
}

function weightedPick(items, rng) {
  const totalWeight = items.reduce((sum, item) => sum + item.weight, 0);
  let random = rng() * totalWeight;
  
  for (const item of items) {
    random -= item.weight;
    if (random <= 0) return item;
  }
  
  return items[items.length - 1];
}

// PS5 Advanced Effects Engine
class PS5EffectsEngine {
  static rayTracedReflections(ctx, traits, pal, rng, centerX, centerY) {
    const reflectionCount = 20;
    const rayLength = 300;

    for (let i = 0; i < reflectionCount; i++) {
      const angle = (i / reflectionCount) * Math.PI * 2;
      const startX = Math.cos(angle) * 100;
      const startY = Math.sin(angle) * 100;

      // Simulate ray bouncing
      let currentX = startX;
      let currentY = startY;
      let currentAngle = angle;

      for (let bounce = 0; bounce < 3; bounce++) {
        const endX = currentX + Math.cos(currentAngle) * rayLength;
        const endY = currentY + Math.sin(currentAngle) * rayLength;

        const gradient = ctx.createLinearGradient(currentX, currentY, endX, endY);
        const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
        gradient.addColorStop(0, `hsla(${hue}, 80%, 60%, 0.1)`);
        gradient.addColorStop(1, `hsla(${hue}, 80%, 60%, 0)`);

        ctx.strokeStyle = gradient;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(currentX, currentY);
        ctx.lineTo(endX, endY);
        ctx.stroke();

        // Calculate reflection
        currentX = endX;
        currentY = endY;
        currentAngle += Math.PI + rng.randomRange(-0.5, 0.5);
      }
    }
  }

  static volumetricLighting(ctx, traits, pal, rng, centerX, centerY) {
    const lightRays = 30;
    const maxRadius = 400;

    for (let i = 0; i < lightRays; i++) {
      const angle = (i / lightRays) * Math.PI * 2;
      const intensity = Math.sin(i * 0.5) * 0.5 + 0.5;

      const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, maxRadius);
      const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];

      gradient.addColorStop(0, `hsla(${hue}, 70%, 70%, ${intensity * 0.3})`);
      gradient.addColorStop(0.5, `hsla(${hue}, 70%, 50%, ${intensity * 0.1})`);
      gradient.addColorStop(1, `hsla(${hue}, 70%, 30%, 0)`);

      ctx.save();
      ctx.rotate(angle);
      ctx.fillStyle = gradient;
      ctx.fillRect(-50, 0, 100, maxRadius);
      ctx.restore();
    }
  }

  static particlePhysicsSimulation(ctx, traits, pal, rng, centerX, centerY) {
    const particleCount = 200;
    const particles = [];

    // Initialize particles with physics properties
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: rng.randomRange(-200, 200),
        y: rng.randomRange(-200, 200),
        vx: rng.randomRange(-2, 2),
        vy: rng.randomRange(-2, 2),
        mass: rng.randomRange(0.5, 2),
        charge: rng.random() > 0.5 ? 1 : -1,
        hue: pal.hues[Math.floor(rng.random() * pal.hues.length)],
        life: 1.0
      });
    }

    // Simulate physics for multiple frames
    for (let frame = 0; frame < 60; frame++) {
      for (let i = 0; i < particles.length; i++) {
        const p1 = particles[i];

        // Apply forces from other particles
        for (let j = i + 1; j < particles.length; j++) {
          const p2 = particles[j];
          const dx = p2.x - p1.x;
          const dy = p2.y - p1.y;
          const distance = Math.sqrt(dx * dx + dy * dy) + 0.1;

          // Electromagnetic force
          const force = (p1.charge * p2.charge) / (distance * distance) * 0.1;
          const fx = (dx / distance) * force;
          const fy = (dy / distance) * force;

          p1.vx -= fx / p1.mass;
          p1.vy -= fy / p1.mass;
          p2.vx += fx / p2.mass;
          p2.vy += fy / p2.mass;
        }

        // Update position
        p1.x += p1.vx;
        p1.y += p1.vy;
        p1.life -= 0.01;

        // Draw particle trail
        if (p1.life > 0) {
          ctx.fillStyle = `hsla(${p1.hue}, 80%, 60%, ${p1.life * 0.3})`;
          ctx.beginPath();
          ctx.arc(p1.x, p1.y, p1.mass, 0, Math.PI * 2);
          ctx.fill();
        }
      }
    }
  }

  static fluidDynamics(ctx, traits, pal, rng, centerX, centerY) {
    const gridSize = 20;
    const width = 800;
    const height = 800;
    const velocityField = [];

    // Initialize velocity field
    for (let x = 0; x < width; x += gridSize) {
      for (let y = 0; y < height; y += gridSize) {
        const distance = Math.sqrt((x - width/2) * (x - width/2) + (y - height/2) * (y - height/2));
        const angle = Math.atan2(y - height/2, x - width/2);

        velocityField.push({
          x: x - width/2,
          y: y - height/2,
          vx: Math.cos(angle + Math.PI/2) * (1 / (distance + 1)) * 100,
          vy: Math.sin(angle + Math.PI/2) * (1 / (distance + 1)) * 100,
          pressure: rng.randomRange(0.5, 1.5)
        });
      }
    }

    // Draw fluid flow
    velocityField.forEach(field => {
      const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
      const alpha = field.pressure * 0.2;

      ctx.strokeStyle = `hsla(${hue}, 70%, 50%, ${alpha})`;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(field.x, field.y);
      ctx.lineTo(field.x + field.vx, field.y + field.vy);
      ctx.stroke();

      // Draw pressure visualization
      ctx.fillStyle = `hsla(${hue}, 60%, 40%, ${alpha * 0.5})`;
      ctx.beginPath();
      ctx.arc(field.x, field.y, field.pressure * 3, 0, Math.PI * 2);
      ctx.fill();
    });
  }
}

// PS5 Traits Generator
function makePS5Traits(seed) {
  const rng = new PS5SeededRandom(seed);

  const pal = weightedPick(PS5_HDR_PALETTES, () => rng.random());
  const spiralType = weightedPick(PS5_SPIRAL_TYPES, () => rng.random());
  const effect = weightedPick(PS5_EFFECTS, () => rng.random());

  return {
    seed: seed,
    palette: pal.name,
    spiralType: spiralType.name,
    effect: effect.name,
    // Enhanced PS5 parameters
    rings: rng.random() < 0.2 ? rng.randomInt(800, 1000) : rng.randomInt(600, 800),
    steps: rng.randomInt(100, 150),
    wobble: rng.random() < 0.15 ? rng.randomRange(0.18, 0.22) : rng.randomRange(0.10, 0.15),
    flowFreqR: rng.randomRange(0.01, 0.03),
    flowFreqI: rng.randomRange(0.01, 0.03),
    swirlOffset: rng.randomRange(0.4, 0.6),
    strokeW: rng.random() < 0.25 ? rng.randomRange(2.5, 3.5) : rng.randomRange(1.8, 2.5),
    alpha: rng.randomRange(0.3, 0.6),
    // PS5 specific enhancements
    hdrIntensity: rng.randomRange(0.5, 1.2),
    bloomRadius: rng.randomRange(10, 30),
    rayTracingQuality: rng.randomRange(0.7, 1.0),
    particleDensity: rng.randomRange(0.8, 1.5),
    quantumCoherence: rng.randomRange(0.3, 0.9),
    neuralComplexity: rng.randomInt(3, 8),
    dimensionalDepth: rng.randomRange(0.5, 1.0),
    // Advanced mathematical parameters
    complexity: rng.randomRange(0.6, 1.0),
    harmonics: rng.randomInt(3, 7),
    phaseShift: rng.randomRange(0, Math.PI * 2),
    amplitudeModulation: rng.randomRange(0.8, 1.8),
    frequencyModulation: rng.randomRange(0.9, 1.3),
    centerOffset: {x: rng.randomRange(-0.05, 0.05), y: rng.randomRange(-0.05, 0.05)},
    // PS5 GPU optimization flags
    gpuAccelerated: spiralType.gpuOptimized && effect.gpuIntensive,
    memoryOptimized: true,
    antiAliasing: PS5_CONFIG.ANTI_ALIASING
  };
}

module.exports = {
  PS5_CONFIG,
  PS5_SPIRAL_TYPES,
  PS5_EFFECTS,
  PS5_HDR_PALETTES,
  PS5SeededRandom,
  PS5SpiralCalculators,
  PS5EffectsEngine,
  makePS5Traits,
  weightedPick
};
