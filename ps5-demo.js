#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

// PS5 Modules
const { generatePS5NFTs } = require('./ps5-production-system');
const { PS5PreviewSystem } = require('./ps5-realtime-preview');
const { PS5BatchManager } = require('./ps5-batch-optimizer');
const { PS5_CONFIG } = require('./ps5-advanced-nft-generator');

// PS5 Demo Configuration
const DEMO_CONFIG = {
  DEMO_COUNT: 10,
  SHOW_PREVIEWS: true,
  RUN_BATCH_TEST: true,
  GENERATE_REPORT: true,
  OUTPUT_DIR: path.join(__dirname, 'ps5-demo-output')
};

class PS5Demo {
  constructor() {
    this.startTime = null;
    this.results = {
      previews: [],
      nfts: [],
      batchResults: null,
      performance: {}
    };
    this.ensureDemoDir();
  }

  ensureDemoDir() {
    if (!fs.existsSync(DEMO_CONFIG.OUTPUT_DIR)) {
      fs.mkdirSync(DEMO_CONFIG.OUTPUT_DIR, { recursive: true });
    }
  }

  async runFullDemo() {
    console.log('🎮 PS5 Quantum Spiral NFT Generator Demo');
    console.log('=========================================');
    console.log('🚀 PlayStation 5 gücüyle generatif sanat üretimi');
    console.log('⚡ 4K HDR, Ray Tracing, Quantum Algorithms');
    console.log('🎨 Neural Network Spirals & Advanced Mathematics\n');

    this.startTime = performance.now();

    try {
      // 1. Sistem Bilgileri
      await this.showSystemInfo();

      // 2. Gerçek Zamanlı Önizlemeler
      if (DEMO_CONFIG.SHOW_PREVIEWS) {
        await this.runPreviewDemo();
      }

      // 3. NFT Üretim Demo
      await this.runNFTGenerationDemo();

      // 4. Batch Processing Demo
      if (DEMO_CONFIG.RUN_BATCH_TEST) {
        await this.runBatchProcessingDemo();
      }

      // 5. Rapor Oluştur
      if (DEMO_CONFIG.GENERATE_REPORT) {
        await this.generateDemoReport();
      }

      console.log('\n🎉 PS5 Demo tamamlandı!');
      console.log(`📁 Sonuçlar: ${DEMO_CONFIG.OUTPUT_DIR}`);

    } catch (error) {
      console.error('❌ Demo sırasında hata:', error.message);
    }
  }

  async showSystemInfo() {
    console.log('🔧 Sistem Bilgileri');
    console.log('-------------------');
    console.log(`💻 Platform: ${process.platform}`);
    console.log(`🏗️  Mimari: ${process.arch}`);
    console.log(`📊 Node.js: ${process.version}`);
    console.log(`💾 Bellek: ${(process.memoryUsage().heapTotal / 1024 / 1024).toFixed(1)}MB`);
    console.log(`⚡ CPU Çekirdekleri: ${require('os').cpus().length}`);
    console.log(`🎮 PS5 Optimizasyonu: Aktif`);
    console.log(`🎨 Çözünürlük: ${PS5_CONFIG.WIDTH}x${PS5_CONFIG.HEIGHT} 4K HDR`);
    console.log(`🔥 GPU Hızlandırma: ${PS5_CONFIG.GPU_ACCELERATION ? 'Aktif' : 'Pasif'}`);
    console.log(`🧠 Paralel İşleme: ${PS5_CONFIG.PARALLEL_PROCESSING ? 'Aktif' : 'Pasif'}\n`);
  }

  async runPreviewDemo() {
    console.log('👁️  Gerçek Zamanlı Önizleme Demo');
    console.log('--------------------------------');

    const previewSystem = new PS5PreviewSystem();
    
    // Farklı spiral türleri için önizlemeler
    const spiralTypes = [
      'Quantum Fibonacci',
      'Neural Network Spiral',
      'Fractal Mandelbrot Spiral',
      'Hyperdimensional Spiral',
      'Plasma Field Spiral'
    ];

    for (let i = 0; i < Math.min(spiralTypes.length, 5); i++) {
      console.log(`🎨 Önizleme ${i + 1}/5 oluşturuluyor...`);
      
      const preview = previewSystem.generateRealtimePreview();
      if (preview) {
        this.results.previews.push(preview);
        console.log(`✅ ${preview.traits.spiralType} + ${preview.traits.effect}`);
      }
      
      // Kısa bekleme
      await this.sleep(500);
    }

    // Animasyonlu önizleme
    console.log('🎬 Animasyonlu önizleme oluşturuluyor...');
    try {
      const animatedPath = previewSystem.generateAnimatedPreview();
      console.log(`✅ Animasyon: ${animatedPath}`);
    } catch (error) {
      console.log('⚠️  Animasyon oluşturulamadı:', error.message);
    }

    console.log(`📊 Toplam ${this.results.previews.length} önizleme oluşturuldu\n`);
  }

  async runNFTGenerationDemo() {
    console.log('🎨 NFT Üretim Demo');
    console.log('------------------');

    // Küçük bir koleksiyon üret
    const originalCount = PS5_CONFIG.NFT_COUNT;
    PS5_CONFIG.NFT_COUNT = DEMO_CONFIG.DEMO_COUNT;
    PS5_CONFIG.OUTPUT_DIR = path.join(DEMO_CONFIG.OUTPUT_DIR, 'nfts');

    const nftStartTime = performance.now();
    
    try {
      await generatePS5NFTs();
      
      const nftEndTime = performance.now();
      const nftDuration = nftEndTime - nftStartTime;
      
      this.results.performance.nftGeneration = {
        duration: nftDuration,
        count: DEMO_CONFIG.DEMO_COUNT,
        averageTime: nftDuration / DEMO_CONFIG.DEMO_COUNT
      };

      console.log(`⚡ ${DEMO_CONFIG.DEMO_COUNT} NFT ${(nftDuration / 1000).toFixed(2)} saniyede üretildi`);
      console.log(`📈 Ortalama: ${(nftDuration / DEMO_CONFIG.DEMO_COUNT).toFixed(2)}ms/NFT\n`);

    } catch (error) {
      console.error('❌ NFT üretim hatası:', error.message);
    } finally {
      // Orijinal ayarları geri yükle
      PS5_CONFIG.NFT_COUNT = originalCount;
      PS5_CONFIG.OUTPUT_DIR = path.join(__dirname, 'ps5-output');
    }
  }

  async runBatchProcessingDemo() {
    console.log('⚡ Batch Processing Demo');
    console.log('-----------------------');

    const batchManager = new PS5BatchManager();
    
    try {
      await batchManager.initializeWorkerPool();

      // Demo task'ları oluştur
      const demoTasks = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        seed: Math.floor(Math.random() * 1e9),
        type: 'nft_generation'
      }));

      const batchStartTime = performance.now();
      
      batchManager.addBatchTasks(demoTasks, 2); // Yüksek öncelik
      await batchManager.startProcessing();
      
      const batchEndTime = performance.now();
      const batchDuration = batchEndTime - batchStartTime;

      this.results.performance.batchProcessing = {
        duration: batchDuration,
        taskCount: demoTasks.length,
        throughput: demoTasks.length / (batchDuration / 1000)
      };

      console.log(`🚀 Batch processing: ${(batchDuration / 1000).toFixed(2)} saniye`);
      console.log(`📊 Throughput: ${(demoTasks.length / (batchDuration / 1000)).toFixed(1)} task/saniye\n`);

      await batchManager.cleanup();

    } catch (error) {
      console.error('❌ Batch processing hatası:', error.message);
    }
  }

  async generateDemoReport() {
    console.log('📊 Demo Raporu Oluşturuluyor');
    console.log('----------------------------');

    const totalTime = performance.now() - this.startTime;
    
    const report = {
      timestamp: new Date().toISOString(),
      platform: 'PlayStation 5',
      totalDuration: totalTime,
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuCores: require('os').cpus().length,
        memoryUsage: process.memoryUsage()
      },
      ps5Config: PS5_CONFIG,
      results: this.results,
      performance: {
        ...this.results.performance,
        totalTime: totalTime,
        memoryEfficiency: this.calculateMemoryEfficiency(),
        cpuEfficiency: this.calculateCPUEfficiency()
      },
      recommendations: this.generateRecommendations()
    };

    // Raporu dosyaya kaydet
    const reportPath = path.join(DEMO_CONFIG.OUTPUT_DIR, 'ps5-demo-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // Konsol raporu
    console.log(`⏱️  Toplam Demo Süresi: ${(totalTime / 1000).toFixed(2)} saniye`);
    console.log(`🎨 Önizleme Sayısı: ${this.results.previews.length}`);
    console.log(`🖼️  NFT Sayısı: ${DEMO_CONFIG.DEMO_COUNT}`);
    
    if (this.results.performance.nftGeneration) {
      console.log(`⚡ NFT Üretim Hızı: ${this.results.performance.nftGeneration.averageTime.toFixed(2)}ms/NFT`);
    }
    
    if (this.results.performance.batchProcessing) {
      console.log(`🚀 Batch Throughput: ${this.results.performance.batchProcessing.throughput.toFixed(1)} task/s`);
    }

    console.log(`💾 Bellek Verimliliği: ${report.performance.memoryEfficiency.toFixed(1)}%`);
    console.log(`⚡ CPU Verimliliği: ${report.performance.cpuEfficiency.toFixed(1)}%`);
    console.log(`📄 Detaylı rapor: ${reportPath}\n`);
  }

  calculateMemoryEfficiency() {
    const memUsage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    return (1 - (memUsage.heapUsed / totalMemory)) * 100;
  }

  calculateCPUEfficiency() {
    // Basit CPU verimlilik hesaplaması
    const cpuCount = require('os').cpus().length;
    const expectedParallelism = Math.min(cpuCount, PS5_CONFIG.BATCH_SIZE);
    return (expectedParallelism / cpuCount) * 100;
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.results.performance.nftGeneration?.averageTime > 5000) {
      recommendations.push('NFT üretim süresi yüksek - GPU optimizasyonlarını kontrol edin');
    }

    if (this.results.performance.batchProcessing?.throughput < 10) {
      recommendations.push('Batch processing throughput düşük - worker sayısını artırın');
    }

    const memEfficiency = this.calculateMemoryEfficiency();
    if (memEfficiency < 70) {
      recommendations.push('Bellek kullanımı yüksek - garbage collection optimizasyonu gerekli');
    }

    if (recommendations.length === 0) {
      recommendations.push('Sistem optimal performans gösteriyor! 🎉');
    }

    return recommendations;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// CLI kullanımı
async function runDemo() {
  const demo = new PS5Demo();
  
  // Komut satırı argümanları
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    console.log('PS5 Quantum Spiral NFT Generator Demo');
    console.log('=====================================');
    console.log('Kullanım: node ps5-demo.js [seçenekler]');
    console.log('');
    console.log('Seçenekler:');
    console.log('  --help              Bu yardım mesajını göster');
    console.log('  --quick             Hızlı demo (sadece önizlemeler)');
    console.log('  --full              Tam demo (varsayılan)');
    console.log('  --batch-only        Sadece batch processing testi');
    console.log('  --count <sayı>      Demo NFT sayısı (varsayılan: 10)');
    return;
  }

  if (args.includes('--count')) {
    const countIndex = args.indexOf('--count');
    const count = parseInt(args[countIndex + 1]);
    if (count && count > 0) {
      DEMO_CONFIG.DEMO_COUNT = count;
    }
  }

  if (args.includes('--quick')) {
    DEMO_CONFIG.RUN_BATCH_TEST = false;
    DEMO_CONFIG.DEMO_COUNT = 3;
  }

  if (args.includes('--batch-only')) {
    DEMO_CONFIG.SHOW_PREVIEWS = false;
    DEMO_CONFIG.DEMO_COUNT = 0;
  }

  await demo.runFullDemo();
}

// Ana çalıştırma
if (require.main === module) {
  runDemo().catch(console.error);
}

module.exports = { PS5Demo, DEMO_CONFIG };
