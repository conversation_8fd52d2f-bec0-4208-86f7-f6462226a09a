{"version": 3, "sources": ["../src/index.js", "../src/constants.js", "../src/stream.js", "../src/lzwEncode.js", "../src/rgb-packing.js", "../src/pnnquant2.js", "../src/color.js", "../src/palettize.js"], "sourcesContent": ["import constants from \"./constants.js\";\nimport lzwEncode from \"./lzwEncode.js\";\nimport createStream from \"./stream.js\";\nimport quantize from \"./pnnquant2.js\";\n\nimport {\n  prequantize,\n  applyPalette,\n  nearestColorIndex,\n  nearestColor,\n  nearestColorIndexWithDistance,\n  snapColorsToPalette,\n} from \"./palettize.js\";\n\nfunction GIFEncoder(opt = {}) {\n  const { initialCapacity = 4096, auto = true } = opt;\n\n  // Stream all encoded data into this buffer\n  const stream = createStream(initialCapacity);\n\n  // Shared array data across all frames\n  const HSIZE = 5003; // 80% occupancy\n  const accum = new Uint8Array(256);\n  const htab = new Int32Array(HSIZE);\n  const codetab = new Int32Array(HSIZE);\n\n  let hasInit = false;\n\n  return {\n    reset() {\n      stream.reset();\n      hasInit = false;\n    },\n    finish() {\n      stream.writeByte(constants.trailer);\n    },\n    bytes() {\n      return stream.bytes();\n    },\n    bytesView() {\n      return stream.bytesView();\n    },\n    get buffer() {\n      return stream.buffer;\n    },\n    get stream() {\n      return stream;\n    },\n    writeHeader,\n    writeFrame(index, width, height, opts = {}) {\n      const {\n        transparent = false,\n        transparentIndex = 0x00,\n        delay = 0,\n        palette = null,\n        repeat = 0, // -1=once, 0=forever, >0=count\n        colorDepth = 8,\n        dispose = -1,\n      } = opts;\n\n      let first = false;\n      if (auto) {\n        // In 'auto' mode, the first time we write a frame\n        // we will write LSD/GCT/EXT\n        if (!hasInit) {\n          // have not yet init, we can consider this our first frame\n          first = true;\n          // in 'auto' mode, we also encode a header on first frame\n          // this is different than manual mode where you must encode\n          // header yoursef (or perhaps not write header altogether)\n          writeHeader();\n          hasInit = true;\n        }\n      } else {\n        // in manual mode, the first frame is determined by the options only\n        first = Boolean(opts.first);\n      }\n\n      width = Math.max(0, Math.floor(width));\n      height = Math.max(0, Math.floor(height));\n\n      // Write pre-frame details such as repeat count and global palette\n      if (first) {\n        if (!palette) {\n          throw new Error(\"First frame must include a { palette } option\");\n        }\n        encodeLogicalScreenDescriptor(\n          stream,\n          width,\n          height,\n          palette,\n          colorDepth\n        );\n        encodeColorTable(stream, palette);\n        if (repeat >= 0) {\n          encodeNetscapeExt(stream, repeat);\n        }\n      }\n\n      const delayTime = Math.round(delay / 10);\n      encodeGraphicControlExt(\n        stream,\n        dispose,\n        delayTime,\n        transparent,\n        transparentIndex\n      );\n\n      const useLocalColorTable = Boolean(palette) && !first;\n      encodeImageDescriptor(\n        stream,\n        width,\n        height,\n        useLocalColorTable ? palette : null\n      );\n      if (useLocalColorTable) encodeColorTable(stream, palette);\n      encodePixels(\n        stream,\n        index,\n        width,\n        height,\n        colorDepth,\n        accum,\n        htab,\n        codetab\n      );\n    },\n  };\n\n  function writeHeader() {\n    writeUTFBytes(stream, \"GIF89a\");\n  }\n}\n\nfunction encodeGraphicControlExt(\n  stream,\n  dispose,\n  delay,\n  transparent,\n  transparentIndex\n) {\n  stream.writeByte(0x21); // extension introducer\n  stream.writeByte(0xf9); // GCE label\n  stream.writeByte(4); // data block size\n\n  if (transparentIndex < 0) {\n    transparentIndex = 0x00;\n    transparent = false;\n  }\n\n  var transp, disp;\n  if (!transparent) {\n    transp = 0;\n    disp = 0; // dispose = no action\n  } else {\n    transp = 1;\n    disp = 2; // force clear if using transparent color\n  }\n\n  if (dispose >= 0) {\n    disp = dispose & 7; // user override\n  }\n\n  disp <<= 2;\n\n  const userInput = 0;\n\n  // packed fields\n  stream.writeByte(\n    0 | // 1:3 reserved\n      disp | // 4:6 disposal\n      userInput | // 7 user input - 0 = none\n      transp // 8 transparency flag\n  );\n\n  writeUInt16(stream, delay); // delay x 1/100 sec\n  stream.writeByte(transparentIndex || 0x00); // transparent color index\n  stream.writeByte(0); // block terminator\n}\n\nfunction encodeLogicalScreenDescriptor(\n  stream,\n  width,\n  height,\n  palette,\n  colorDepth = 8\n) {\n  const globalColorTableFlag = 1;\n  const sortFlag = 0;\n  const globalColorTableSize = colorTableSize(palette.length) - 1;\n  const fields =\n    (globalColorTableFlag << 7) |\n    ((colorDepth - 1) << 4) |\n    (sortFlag << 3) |\n    globalColorTableSize;\n  const backgroundColorIndex = 0;\n  const pixelAspectRatio = 0;\n  writeUInt16(stream, width);\n  writeUInt16(stream, height);\n  stream.writeBytes([fields, backgroundColorIndex, pixelAspectRatio]);\n}\n\nfunction encodeNetscapeExt(stream, repeat) {\n  stream.writeByte(0x21); // extension introducer\n  stream.writeByte(0xff); // app extension label\n  stream.writeByte(11); // block size\n  writeUTFBytes(stream, \"NETSCAPE2.0\"); // app id + auth code\n  stream.writeByte(3); // sub-block size\n  stream.writeByte(1); // loop sub-block id\n  writeUInt16(stream, repeat); // loop count (extra iterations, 0=repeat forever)\n  stream.writeByte(0); // block terminator\n}\n\nfunction encodeColorTable(stream, palette) {\n  const colorTableLength = 1 << colorTableSize(palette.length);\n  for (let i = 0; i < colorTableLength; i++) {\n    let color = [0, 0, 0];\n    if (i < palette.length) {\n      color = palette[i];\n    }\n    stream.writeByte(color[0]);\n    stream.writeByte(color[1]);\n    stream.writeByte(color[2]);\n  }\n}\n\nfunction encodeImageDescriptor(stream, width, height, localPalette) {\n  stream.writeByte(0x2c); // image separator\n\n  writeUInt16(stream, 0); // x position\n  writeUInt16(stream, 0); // y position\n  writeUInt16(stream, width); // image size\n  writeUInt16(stream, height);\n\n  if (localPalette) {\n    const interlace = 0;\n    const sorted = 0;\n    const palSize = colorTableSize(localPalette.length) - 1;\n    // local palette\n    stream.writeByte(\n      0x80 | // 1 local color table 1=yes\n        interlace | // 2 interlace - 0=no\n        sorted | // 3 sorted - 0=no\n        0 | // 4-5 reserved\n        palSize // 6-8 size of color table\n    );\n  } else {\n    // global palette\n    stream.writeByte(0);\n  }\n}\n\nfunction encodePixels(\n  stream,\n  index,\n  width,\n  height,\n  colorDepth = 8,\n  accum,\n  htab,\n  codetab\n) {\n  lzwEncode(width, height, index, colorDepth, stream, accum, htab, codetab);\n}\n\n// Utilities\n\nfunction writeUInt16(stream, short) {\n  stream.writeByte(short & 0xff);\n  stream.writeByte((short >> 8) & 0xff);\n}\n\nfunction writeUTFBytes(stream, text) {\n  for (var i = 0; i < text.length; i++) {\n    stream.writeByte(text.charCodeAt(i));\n  }\n}\n\nfunction colorTableSize(length) {\n  return Math.max(Math.ceil(Math.log2(length)), 1);\n}\n\nexport {\n  GIFEncoder,\n  quantize,\n  prequantize,\n  applyPalette,\n  nearestColorIndex,\n  nearestColor,\n  nearestColorIndexWithDistance,\n  snapColorsToPalette,\n};\n\nexport default GIFEncoder;\n", "export default {\n  signature: \"G<PERSON>\",\n  version: \"89a\",\n  trailer: 0x3B,\n  extensionIntroducer: 0x21,\n  applicationExtensionLabel: 0xFF,\n  graphicControlExtensionLabel: 0xF9,\n  imageSeparator: 0x2C,\n  // Header\n  signatureSize: 3,\n  versionSize: 3,\n  globalColorTableFlagMask: 0b10000000,\n  colorResolutionMask: 0b01110000,\n  sortFlagMask: 0b00001000,\n  globalColorTableSizeMask: 0b00000111,\n  // Application extension\n  applicationIdentifierSize: 8,\n  applicationAuthCodeSize: 3,\n  // Graphic control extension\n  disposalMethodMask: 0b00011100,\n  userInputFlagMask: 0b00000010,\n  transparentColorFlagMask: 0b00000001,\n  // Image descriptor\n  localColorTableFlagMask: 0b10000000,\n  interlaceFlagMask: 0b01000000,\n  idSortFlagMask: 0b00100000,\n  localColorTableSizeMask: 0b00000111\n}\n", "export default function createStream(initialCapacity = 256) {\n  let cursor = 0;\n  let contents = new Uint8Array(initialCapacity);\n\n  return {\n    get buffer() {\n      return contents.buffer;\n    },\n    reset() {\n      cursor = 0;\n    },\n    bytesView() {\n      return contents.subarray(0, cursor);\n    },\n    bytes() {\n      return contents.slice(0, cursor);\n    },\n    writeByte(byte) {\n      expand(cursor + 1);\n      contents[cursor] = byte;\n      cursor++;\n    },\n    writeBytes(data, offset = 0, byteLength = data.length) {\n      expand(cursor + byteLength);\n      for (let i = 0; i < byteLength; i++) {\n        contents[cursor++] = data[i + offset];\n      }\n    },\n    writeBytesView(data, offset = 0, byteLength = data.byteLength) {\n      expand(cursor + byteLength);\n      contents.set(data.subarray(offset, offset + byteLength), cursor);\n      cursor += byteLength;\n    },\n  };\n\n  function expand(newCapacity) {\n    var prevCapacity = contents.length;\n    if (prevCapacity >= newCapacity) return; // No need to expand, the storage was already large enough.\n    // Don't expand strictly to the given requested limit if it's only a very small increase, but instead geometrically grow capacity.\n    // For small filesizes (<1MB), perform size*2 geometric increase, but for large sizes, do a much more conservative size*1.125 increase to\n    // avoid overshooting the allocation cap by a very large margin.\n    var CAPACITY_DOUBLING_MAX = 1024 * 1024;\n    newCapacity = Math.max(\n      newCapacity,\n      (prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2.0 : 1.125)) >>>\n        0\n    );\n    if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256); // At minimum allocate 256b for each file when expanding.\n    const oldContents = contents;\n    contents = new Uint8Array(newCapacity); // Allocate new storage.\n    if (cursor > 0) contents.set(oldContents.subarray(0, cursor), 0);\n  }\n}\n", "/*\n  LZWEncoder.js\n  Authors\n  <PERSON> (original Java version - <EMAIL>)\n  <PERSON><PERSON><PERSON><PERSON> (AS3 version - bytearray.org)\n  <PERSON> (JS version - <EMAIL>)\n  Acknowledgements\n  GIFCOMPR.C - GIF Image compression routines\n  Lempel-Ziv compression based on 'compress'. GIF modifications by\n  <PERSON> (<EMAIL>)\n  GIF Image compression - modified 'compress'\n  Based on: compress.c - File compression ala IEEE Computer, June 1984.\n  By Authors: <AUTHORS>\n  <PERSON> (decvax!mcvax!jim)\n  <PERSON> (decvax!vax135!petsd!peora!srd)\n  <PERSON> (decvax!decwrl!turtlevax!ken)\n  <PERSON> (decvax!ihnp4!ames!jaw)\n  <PERSON> (decvax!vax135!petsd!joe)\n  <PERSON> (@mattdesl - V8/JS optimizations)\n  <PERSON><PERSON> (@p01 - JS optimization)\n*/\n\nimport createStream from \"./stream.js\";\n\nconst EOF = -1;\nconst BITS = 12;\nconst DEFAULT_HSIZE = 5003; // 80% occupancy\nconst MASKS = [\n  0x0000,\n  0x0001,\n  0x0003,\n  0x0007,\n  0x000f,\n  0x001f,\n  0x003f,\n  0x007f,\n  0x00ff,\n  0x01ff,\n  0x03ff,\n  0x07ff,\n  0x0fff,\n  0x1fff,\n  0x3fff,\n  0x7fff,\n  0xffff,\n];\n\nfunction lzwEncode(\n  width,\n  height,\n  pixels,\n  colorDepth,\n  outStream = createStream(512),\n  accum = new Uint8Array(256),\n  htab = new Int32Array(DEFAULT_HSIZE),\n  codetab = new Int32Array(DEFAULT_HSIZE)\n) {\n  const hsize = htab.length;\n  const initCodeSize = Math.max(2, colorDepth);\n\n  accum.fill(0);\n  codetab.fill(0);\n  htab.fill(-1);\n\n  let cur_accum = 0;\n  let cur_bits = 0;\n\n  // Algorithm: use open addressing double hashing (no chaining) on the\n  // prefix code / next character combination. We do a variant of Knuth's\n  // algorithm D (vol. 3, sec. 6.4) along with G. Knott's relatively-prime\n  // secondary probe. Here, the modular division first probe is gives way\n  // to a faster exclusive-or manipulation. Also do block compression with\n  // an adaptive reset, whereby the code table is cleared when the compression\n  // ratio decreases, but after the table fills. The variable-length output\n  // codes are re-sized at this point, and a special CLEAR code is generated\n  // for the decompressor. Late addition: construct the table according to\n  // file size for noticeable speed improvement on small files. Please direct\n  // questions about this implementation to ames!jaw.\n\n  // compress and write the pixel data\n  const init_bits = initCodeSize + 1;\n\n  // Set up the globals: g_init_bits - initial number of bits\n  const g_init_bits = init_bits;\n\n  // Set up the necessary values\n\n  // block compression parameters -- after all codes are used up,\n  // and compression rate changes, start over.\n  let clear_flg = false;\n  let n_bits = g_init_bits;\n  let maxcode = (1 << n_bits) - 1;\n\n  const ClearCode = 1 << (init_bits - 1);\n  const EOFCode = ClearCode + 1;\n  let free_ent = ClearCode + 2;\n  let a_count = 0; // clear packet\n\n  let ent = pixels[0];\n\n  let hshift = 0;\n  for (let fcode = hsize; fcode < 65536; fcode *= 2) {\n    ++hshift;\n  }\n  hshift = 8 - hshift; // set hash code range bound\n\n  outStream.writeByte(initCodeSize); // write \"initial code size\" byte\n\n  output(ClearCode);\n\n  const length = pixels.length;\n  for (let idx = 1; idx < length; idx++) {\n    next_block: {\n      const c = pixels[idx];\n      const fcode = (c << BITS) + ent;\n      let i = (c << hshift) ^ ent; // xor hashing\n      if (htab[i] === fcode) {\n        ent = codetab[i];\n        break next_block;\n      }\n\n      const disp = i === 0 ? 1 : hsize - i; // secondary hash (after G. Knott)\n      while (htab[i] >= 0) {\n        // non-empty slot\n        i -= disp;\n        if (i < 0) i += hsize;\n        if (htab[i] === fcode) {\n          ent = codetab[i];\n          break next_block;\n        }\n      }\n      output(ent);\n      ent = c;\n      if (free_ent < 1 << BITS) {\n        codetab[i] = free_ent++; // code -> hashtable\n        htab[i] = fcode;\n      } else {\n        // Clear out the hash table\n        // table clear for block compress\n        htab.fill(-1);\n        free_ent = ClearCode + 2;\n        clear_flg = true;\n        output(ClearCode);\n      }\n    }\n  }\n\n  // Put out the final code.\n  output(ent);\n  output(EOFCode);\n\n  outStream.writeByte(0); // write block terminator\n  return outStream.bytesView();\n\n  function output(code) {\n    cur_accum &= MASKS[cur_bits];\n\n    if (cur_bits > 0) cur_accum |= code << cur_bits;\n    else cur_accum = code;\n\n    cur_bits += n_bits;\n\n    while (cur_bits >= 8) {\n      // Add a character to the end of the current packet, and if it is 254\n      // characters, flush the packet to disk.\n      accum[a_count++] = cur_accum & 0xff;\n      if (a_count >= 254) {\n        outStream.writeByte(a_count);\n        outStream.writeBytesView(accum, 0, a_count);\n        a_count = 0;\n      }\n      cur_accum >>= 8;\n      cur_bits -= 8;\n    }\n\n    // If the next entry is going to be too big for the code size,\n    // then increase it, if possible.\n    if (free_ent > maxcode || clear_flg) {\n      if (clear_flg) {\n        n_bits = g_init_bits;\n        maxcode = (1 << n_bits) - 1;\n        clear_flg = false;\n      } else {\n        ++n_bits;\n        maxcode = n_bits === BITS ? (1 << n_bits) : (1 << n_bits) - 1;\n      }\n    }\n\n    if (code == EOFCode) {\n      // At EOF, write the rest of the buffer.\n      while (cur_bits > 0) {\n        // Add a character to the end of the current packet, and if it is 254\n        // characters, flush the packet to disk.\n        accum[a_count++] = cur_accum & 0xff;\n        if (a_count >= 254) {\n          outStream.writeByte(a_count);\n          outStream.writeBytesView(accum, 0, a_count);\n          a_count = 0;\n        }\n        cur_accum >>= 8;\n        cur_bits -= 8;\n      }\n      // Flush the packet to disk, and reset the accumulator\n      if (a_count > 0) {\n        outStream.writeByte(a_count);\n        outStream.writeBytesView(accum, 0, a_count);\n        a_count = 0;\n      }\n    }\n  }\n}\n\nexport default lzwEncode;\n", "export function uint32_to_rgba(color) {\n  var a = (color >> 24) & 0xff;\n  var b = (color >> 16) & 0xff;\n  var g = (color >> 8) & 0xff;\n  var r = color & 0xff;\n  return [r, g, b, a];\n}\n\nexport function rgba_to_uint32(r, g, b, a) {\n  return (a << 24) | (b << 16) | (g << 8) | r;\n}\n\nexport function rgb888_to_rgb565(r, g, b) {\n  return ((r << 8) & 0xf800) | ((g << 2) & 0x03e0) | (b >> 3);\n}\n\nexport function rgba8888_to_rgba4444(r, g, b, a) {\n  return (r >> 4) | (g & 0xf0) | ((b & 0xf0) << 4) | ((a & 0xf0) << 8);\n}\n\nexport function rgb888_to_rgb444(r, g, b) {\n  return ((r >> 4) << 8) | (g & 0xf0) | (b >> 4);\n}\n\n// Alternative 565 ?\n// return ((r & 0xf8) << 8) + ((g & 0xfc) << 3) + (b >> 3);\n\n// Alternative 4444 ?\n// ((a & 0xf0) << 8) | ((r & 0xf0) << 4) | (g & 0xf0) | (b >> 4);\n", "// Modified from:\n// https://github.com/mcychan/PnnQuant.js/blob/master/src/pnnquant.js\n\n/* Fast pairwise nearest neighbor based algorithm for multilevel thresholding\nCopyright (C) 2004-2019 <PERSON> and <PERSON>\nCopyright (c) 2018-2021 <PERSON>\n* error measure; time used is proportional to number of bins squared - WJ */\n\nimport {\n  rgb888_to_rgb565,\n  rgb888_to_rgb444,\n  rgba8888_to_rgba4444,\n} from \"./rgb-packing.js\";\n\nfunction clamp(value, min, max) {\n  return value < min ? min : value > max ? max : value;\n}\n\nfunction sqr(value) {\n  return value * value;\n}\n\nfunction find_nn(bins, idx, hasAlpha) {\n  var nn = 0;\n  var err = 1e100;\n\n  const bin1 = bins[idx];\n  const n1 = bin1.cnt;\n  const wa = bin1.ac;\n  const wr = bin1.rc;\n  const wg = bin1.gc;\n  const wb = bin1.bc;\n  for (var i = bin1.fw; i != 0; i = bins[i].fw) {\n    const bin = bins[i];\n    const n2 = bin.cnt;\n    const nerr2 = (n1 * n2) / (n1 + n2);\n    if (nerr2 >= err) continue;\n\n    var nerr = 0;\n    if (hasAlpha) {\n      nerr += nerr2 * sqr(bin.ac - wa);\n      if (nerr >= err) continue;\n    }\n\n    nerr += nerr2 * sqr(bin.rc - wr);\n    if (nerr >= err) continue;\n\n    nerr += nerr2 * sqr(bin.gc - wg);\n    if (nerr >= err) continue;\n\n    nerr += nerr2 * sqr(bin.bc - wb);\n    if (nerr >= err) continue;\n    err = nerr;\n    nn = i;\n  }\n  bin1.err = err;\n  bin1.nn = nn;\n}\n\nfunction create_bin() {\n  return {\n    ac: 0,\n    rc: 0,\n    gc: 0,\n    bc: 0,\n    cnt: 0,\n    nn: 0,\n    fw: 0,\n    bk: 0,\n    tm: 0,\n    mtm: 0,\n    err: 0,\n  };\n}\n\nfunction bin_add_rgb(bin, r, g, b) {\n  bin.rc += r;\n  bin.gc += g;\n  bin.bc += b;\n  bin.cnt++;\n}\n\nfunction create_bin_list(data, format) {\n  const bincount = format === \"rgb444\" ? 4096 : 65536;\n  const bins = new Array(bincount);\n  const size = data.length;\n\n  /* Build histogram */\n  // Note: Instead of introducing branching/conditions\n  // within a very hot per-pixel iteration, we just duplicate the code\n  // for each new condition\n  if (format === \"rgba4444\") {\n    for (let i = 0; i < size; ++i) {\n      const color = data[i];\n      const a = (color >> 24) & 0xff;\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n\n      // reduce to rgb4444 16-bit uint\n      const index = rgba8888_to_rgba4444(r, g, b, a);\n      let bin = index in bins ? bins[index] : (bins[index] = create_bin());\n      bin.rc += r;\n      bin.gc += g;\n      bin.bc += b;\n      bin.ac += a;\n      bin.cnt++;\n    }\n  }\n  \n  else if (format === \"rgb444\") {\n    for (let i = 0; i < size; ++i) {\n      const color = data[i];\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n\n      // reduce to rgb444 12-bit uint\n      const index = rgb888_to_rgb444(r, g, b);\n      let bin = index in bins ? bins[index] : (bins[index] = create_bin());\n      bin.rc += r;\n      bin.gc += g;\n      bin.bc += b;\n      bin.cnt++;\n    }\n  } else {\n    for (let i = 0; i < size; ++i) {\n      const color = data[i];\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n\n      // reduce to rgb565 16-bit uint\n      const index = rgb888_to_rgb565(r, g, b);\n      let bin = index in bins ? bins[index] : (bins[index] = create_bin());\n      bin.rc += r;\n      bin.gc += g;\n      bin.bc += b;\n      bin.cnt++;\n    }\n  }\n  return bins;\n}\n\nexport default function quantize(rgba, maxColors, opts = {}) {\n  const {\n    format = \"rgb565\",\n    clearAlpha = true,\n    clearAlphaColor = 0x00,\n    clearAlphaThreshold = 0,\n    oneBitAlpha = false,\n  } = opts;\n\n  if (!rgba || !rgba.buffer) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  if (!(rgba instanceof Uint8Array) && !(rgba instanceof Uint8ClampedArray)) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  \n  const data = new Uint32Array(rgba.buffer);\n\n  let useSqrt = opts.useSqrt !== false;\n\n  // format can be:\n  // rgb565 (default)\n  // rgb444\n  // rgba4444\n\n  const hasAlpha = format === \"rgba4444\";\n  const bins = create_bin_list(data, format);\n  const bincount = bins.length;\n  const bincountMinusOne = bincount - 1;\n  const heap = new Uint32Array(bincount + 1);\n\n  /* Cluster nonempty bins at one end of array */\n  var maxbins = 0;\n  for (var i = 0; i < bincount; ++i) {\n    const bin = bins[i];\n    if (bin != null) {\n      var d = 1.0 / bin.cnt;\n      if (hasAlpha) bin.ac *= d;\n      bin.rc *= d;\n      bin.gc *= d;\n      bin.bc *= d;\n      bins[maxbins++] = bin;\n    }\n  }\n\n  if (sqr(maxColors) / maxbins < 0.022) {\n    useSqrt = false;\n  }\n\n  var i = 0;\n  for (; i < maxbins - 1; ++i) {\n    bins[i].fw = i + 1;\n    bins[i + 1].bk = i;\n    if (useSqrt) bins[i].cnt = Math.sqrt(bins[i].cnt);\n  }\n  if (useSqrt) bins[i].cnt = Math.sqrt(bins[i].cnt);\n\n  var h, l, l2;\n  /* Initialize nearest neighbors and build heap of them */\n  for (i = 0; i < maxbins; ++i) {\n    find_nn(bins, i, false);\n    /* Push slot on heap */\n    var err = bins[i].err;\n    for (l = ++heap[0]; l > 1; l = l2) {\n      l2 = l >> 1;\n      if (bins[(h = heap[l2])].err <= err) break;\n      heap[l] = h;\n    }\n    heap[l] = i;\n  }\n\n  /* Merge bins which increase error the least */\n  var extbins = maxbins - maxColors;\n  for (i = 0; i < extbins; ) {\n    var tb;\n    /* Use heap to find which bins to merge */\n    for (;;) {\n      var b1 = heap[1];\n      tb = bins[b1]; /* One with least error */\n      /* Is stored error up to date? */\n      if (tb.tm >= tb.mtm && bins[tb.nn].mtm <= tb.tm) break;\n      if (tb.mtm == bincountMinusOne)\n        /* Deleted node */ b1 = heap[1] = heap[heap[0]--];\n      /* Too old error value */ else {\n        find_nn(bins, b1, false);\n        tb.tm = i;\n      }\n      /* Push slot down */\n      var err = bins[b1].err;\n      for (l = 1; (l2 = l + l) <= heap[0]; l = l2) {\n        if (l2 < heap[0] && bins[heap[l2]].err > bins[heap[l2 + 1]].err) l2++;\n        if (err <= bins[(h = heap[l2])].err) break;\n        heap[l] = h;\n      }\n      heap[l] = b1;\n    }\n\n    /* Do a merge */\n    var nb = bins[tb.nn];\n    var n1 = tb.cnt;\n    var n2 = nb.cnt;\n    var d = 1.0 / (n1 + n2);\n    if (hasAlpha) tb.ac = d * (n1 * tb.ac + n2 * nb.ac);\n    tb.rc = d * (n1 * tb.rc + n2 * nb.rc);\n    tb.gc = d * (n1 * tb.gc + n2 * nb.gc);\n    tb.bc = d * (n1 * tb.bc + n2 * nb.bc);\n    tb.cnt += nb.cnt;\n    tb.mtm = ++i;\n\n    /* Unchain deleted bin */\n    bins[nb.bk].fw = nb.fw;\n    bins[nb.fw].bk = nb.bk;\n    nb.mtm = bincountMinusOne;\n  }\n\n  // let palette = new Uint32Array(maxColors);\n  let palette = [];\n\n  /* Fill palette */\n  var k = 0;\n  for (i = 0; ; ++k) {\n    let r = clamp(Math.round(bins[i].rc), 0, 0xff);\n    let g = clamp(Math.round(bins[i].gc), 0, 0xff);\n    let b = clamp(Math.round(bins[i].bc), 0, 0xff);\n\n    let a = 0xff;\n    if (hasAlpha) {\n      a = clamp(Math.round(bins[i].ac), 0, 0xff);\n      if (oneBitAlpha) {\n        const threshold = typeof oneBitAlpha === \"number\" ? oneBitAlpha : 127;\n        a = a <= threshold ? 0x00 : 0xff;\n      }\n      if (clearAlpha && a <= clearAlphaThreshold) {\n        r = g = b = clearAlphaColor;\n        a = 0x00;\n      }\n    }\n\n    const color = hasAlpha ? [r, g, b, a] : [r, g, b];\n    const exists = existsInPalette(palette, color);\n    if (!exists) palette.push(color);\n    if ((i = bins[i].fw) == 0) break;\n  }\n\n  return palette;\n}\n\nfunction existsInPalette(palette, color) {\n  for (let i = 0; i < palette.length; i++) {\n    const p = palette[i];\n    let matchesRGB =\n      p[0] === color[0] && p[1] === color[1] && p[2] === color[2];\n    let matchesAlpha =\n      p.length >= 4 && color.length >= 4 ? p[3] === color[3] : true;\n    if (matchesRGB && matchesAlpha) return true;\n  }\n  return false;\n}\n\n// TODO: Further 'clean' palette by merging nearly-identical colors?\n", "function rgb2y(r, g, b) {\n  return r * 0.29889531 + g * 0.58662247 + b * 0.11448223;\n}\nfunction rgb2i(r, g, b) {\n  return r * 0.59597799 - g * 0.2741761 - b * 0.32180189;\n}\nfunction rgb2q(r, g, b) {\n  return r * 0.21147017 - g * 0.52261711 + b * 0.31114694;\n}\n\nexport function colorDifferenceYIQSquared(yiqA, yiqB) {\n  const y = yiqA[0] - yiqB[0];\n  const i = yiqA[1] - yiqB[1];\n  const q = yiqA[2] - yiqB[2];\n  const a = alpha(yiqA) - alpha(yiqB);\n  return y * y * 0.5053 + i * i * 0.299 + q * q * 0.1957 + a * a;\n}\n\nfunction alpha(array) {\n  return array[3] != null ? array[3] : 0xff;\n}\n\nexport function colorDifferenceYIQ(yiqA, yiqB) {\n  return Math.sqrt(colorDifferenceYIQSquared(yiqA, yiqB));\n}\n\nexport function colorDifferenceRGBToYIQSquared(rgb1, rgb2) {\n  const [r1, g1, b1] = rgb1;\n  const [r2, g2, b2] = rgb2;\n  const y = rgb2y(r1, g1, b1) - rgb2y(r2, g2, b2),\n    i = rgb2i(r1, g1, b1) - rgb2i(r2, g2, b2),\n    q = rgb2q(r1, g1, b1) - rgb2q(r2, g2, b2);\n  const a = alpha(rgb1) - alpha(rgb2);\n  return y * y * 0.5053 + i * i * 0.299 + q * q * 0.1957 + a * a;\n}\n\nexport function colorDifferenceRGBToYIQ(rgb1, rgb2) {\n  return Math.sqrt(colorDifferenceRGBToYIQSquared(rgb1, rgb2));\n}\n\nexport function euclideanDistanceSquared(a, b) {\n  var sum = 0;\n  var n;\n  for (n = 0; n < a.length; n++) {\n    const dx = a[n] - b[n];\n    sum += dx * dx;\n  }\n  return sum;\n}\n\nexport function euclideanDistance(a, b) {\n  return Math.sqrt(euclideanDistanceSquared(a, b));\n}\n", "import {\n  rgb888_to_rgb444,\n  rgb888_to_rgb565,\n  rgba8888_to_rgba4444,\n} from \"./rgb-packing.js\";\n\nimport { euclideanDistanceSquared } from \"./color.js\";\n\nfunction roundStep(byte, step) {\n  return step > 1 ? Math.round(byte / step) * step : byte;\n}\n\nexport function prequantize(\n  rgba,\n  { roundRGB = 5, roundAlpha = 10, oneBitAlpha = null } = {}\n) {\n  const data = new Uint32Array(rgba.buffer);\n  for (let i = 0; i < data.length; i++) {\n    const color = data[i];\n    let a = (color >> 24) & 0xff;\n    let b = (color >> 16) & 0xff;\n    let g = (color >> 8) & 0xff;\n    let r = color & 0xff;\n\n    a = roundStep(a, roundAlpha);\n    if (oneBitAlpha) {\n      const threshold = typeof oneBitAlpha === \"number\" ? oneBitAlpha : 127;\n      a = a <= threshold ? 0x00 : 0xff;\n    }\n    r = roundStep(r, roundRGB);\n    g = roundStep(g, roundRGB);\n    b = roundStep(b, roundRGB);\n\n    data[i] = (a << 24) | (b << 16) | (g << 8) | (r << 0);\n  }\n}\n\nexport function applyPalette(rgba, palette, format = \"rgb565\") {\n  if (!rgba || !rgba.buffer) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  if (!(rgba instanceof Uint8Array) && !(rgba instanceof Uint8ClampedArray)) {\n    throw new Error('quantize() expected RGBA Uint8Array data');\n  }\n  if (palette.length > 256) {\n    throw new Error('applyPalette() only works with 256 colors or less');\n  }\n\n  const data = new Uint32Array(rgba.buffer);\n  const length = data.length;\n  const bincount = format === \"rgb444\" ? 4096 : 65536;\n  const index = new Uint8Array(length);\n  const cache = new Array(bincount);\n  const hasAlpha = format === \"rgba4444\";\n\n  // Some duplicate code below due to very hot code path\n  // Introducing branching/conditions shows some significant impact\n  if (format === \"rgba4444\") {\n    for (let i = 0; i < length; i++) {\n      const color = data[i];\n      const a = (color >> 24) & 0xff;\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n      const key = rgba8888_to_rgba4444(r, g, b, a);\n      const idx = key in cache ? cache[key] : (cache[key] = nearestColorIndexRGBA(r, g, b, a, palette));\n      index[i] = idx;\n    }\n  } else {\n    const rgb888_to_key = format === \"rgb444\" ? rgb888_to_rgb444 : rgb888_to_rgb565;\n    for (let i = 0; i < length; i++) {\n      const color = data[i];\n      const b = (color >> 16) & 0xff;\n      const g = (color >> 8) & 0xff;\n      const r = color & 0xff;\n      const key = rgb888_to_key(r, g, b);\n      const idx = key in cache ? cache[key] : (cache[key] = nearestColorIndexRGB(r, g, b, palette));\n      index[i] = idx;\n    }\n  }\n\n  return index;\n}\n\nfunction nearestColorIndexRGBA(r, g, b, a, palette) {\n  let k = 0;\n  let mindist = 1e100;\n  for (let i = 0; i < palette.length; i++) {\n    const px2 = palette[i];\n    const a2 = px2[3];\n    let curdist = sqr(a2 - a);\n    if (curdist > mindist) continue;\n    const r2 = px2[0];\n    curdist += sqr(r2 - r);\n    if (curdist > mindist) continue;\n    const g2 = px2[1];\n    curdist += sqr(g2 - g);\n    if (curdist > mindist) continue;\n    const b2 = px2[2];\n    curdist += sqr(b2 - b);\n    if (curdist > mindist) continue;\n    mindist = curdist;\n    k = i;\n  }\n  return k;\n}\n\nfunction nearestColorIndexRGB(r, g, b, palette) {\n  let k = 0;\n  let mindist = 1e100;\n  for (let i = 0; i < palette.length; i++) {\n    const px2 = palette[i];\n    const r2 = px2[0];\n    let curdist = sqr(r2 - r);\n    if (curdist > mindist) continue;\n    const g2 = px2[1];\n    curdist += sqr(g2 - g);\n    if (curdist > mindist) continue;\n    const b2 = px2[2];\n    curdist += sqr(b2 - b);\n    if (curdist > mindist) continue;\n    mindist = curdist;\n    k = i;\n  }\n  return k;\n}\n\nexport function snapColorsToPalette(palette, knownColors, threshold = 5) {\n  if (!palette.length || !knownColors.length) return;\n\n  const paletteRGB = palette.map((p) => p.slice(0, 3));\n  const thresholdSq = threshold * threshold;\n  const dim = palette[0].length;\n  for (let i = 0; i < knownColors.length; i++) {\n    let color = knownColors[i];\n    if (color.length < dim) {\n      // palette is RGBA, known is RGB\n      color = [color[0], color[1], color[2], 0xff];\n    } else if (color.length > dim) {\n      // palette is RGB, known is RGBA\n      color = color.slice(0, 3);\n    } else {\n      // make sure we always copy known colors\n      color = color.slice();\n    }\n    const r = nearestColorIndexWithDistance(\n      paletteRGB,\n      color.slice(0, 3),\n      euclideanDistanceSquared\n    );\n    const idx = r[0];\n    const distanceSq = r[1];\n    if (distanceSq > 0 && distanceSq <= thresholdSq) {\n      palette[idx] = color;\n    }\n  }\n}\n\nfunction sqr(a) {\n  return a * a;\n}\n\nexport function nearestColorIndex(\n  colors,\n  pixel,\n  distanceFn = euclideanDistanceSquared\n) {\n  let minDist = Infinity;\n  let minDistIndex = -1;\n  for (let j = 0; j < colors.length; j++) {\n    const paletteColor = colors[j];\n    const dist = distanceFn(pixel, paletteColor);\n    if (dist < minDist) {\n      minDist = dist;\n      minDistIndex = j;\n    }\n  }\n  return minDistIndex;\n}\n\nexport function nearestColorIndexWithDistance(\n  colors,\n  pixel,\n  distanceFn = euclideanDistanceSquared\n) {\n  let minDist = Infinity;\n  let minDistIndex = -1;\n  for (let j = 0; j < colors.length; j++) {\n    const paletteColor = colors[j];\n    const dist = distanceFn(pixel, paletteColor);\n    if (dist < minDist) {\n      minDist = dist;\n      minDistIndex = j;\n    }\n  }\n  return [minDistIndex, minDist];\n}\n\nexport function nearestColor(\n  colors,\n  pixel,\n  distanceFn = euclideanDistanceSquared\n) {\n  return colors[nearestColorIndex(colors, pixel, distanceFn)];\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAO,oBAAQ;AAAA,EACb,WAAW;AAAA,EACX,SAAS;AAAA,EACT,SAAS;AAAA,EACT,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,gBAAgB;AAAA,EAEhB,eAAe;AAAA,EACf,aAAa;AAAA,EACb,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,0BAA0B;AAAA,EAE1B,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EAEzB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAE1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,yBAAyB;AAAA;;;AC1BZ,sBAAsB,kBAAkB,KAAK;AAC1D,MAAI,SAAS;AACb,MAAI,WAAW,IAAI,WAAW;AAE9B,SAAO;AAAA,QACD,SAAS;AACX,aAAO,SAAS;AAAA;AAAA,IAElB,QAAQ;AACN,eAAS;AAAA;AAAA,IAEX,YAAY;AACV,aAAO,SAAS,SAAS,GAAG;AAAA;AAAA,IAE9B,QAAQ;AACN,aAAO,SAAS,MAAM,GAAG;AAAA;AAAA,IAE3B,UAAU,MAAM;AACd,aAAO,SAAS;AAChB,eAAS,UAAU;AACnB;AAAA;AAAA,IAEF,WAAW,MAAM,SAAS,GAAG,aAAa,KAAK,QAAQ;AACrD,aAAO,SAAS;AAChB,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,iBAAS,YAAY,KAAK,IAAI;AAAA;AAAA;AAAA,IAGlC,eAAe,MAAM,SAAS,GAAG,aAAa,KAAK,YAAY;AAC7D,aAAO,SAAS;AAChB,eAAS,IAAI,KAAK,SAAS,QAAQ,SAAS,aAAa;AACzD,gBAAU;AAAA;AAAA;AAId,kBAAgB,aAAa;AAC3B,QAAI,eAAe,SAAS;AAC5B,QAAI,gBAAgB;AAAa;AAIjC,QAAI,wBAAwB,OAAO;AACnC,kBAAc,KAAK,IACjB,aACC,eAAgB,gBAAe,wBAAwB,IAAM,WAC5D;AAEJ,QAAI,gBAAgB;AAAG,oBAAc,KAAK,IAAI,aAAa;AAC3D,UAAM,cAAc;AACpB,eAAW,IAAI,WAAW;AAC1B,QAAI,SAAS;AAAG,eAAS,IAAI,YAAY,SAAS,GAAG,SAAS;AAAA;AAAA;;;ACzBlE,IAAM,OAAO;AACb,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAGF,mBACE,OACA,QACA,QACA,YACA,YAAY,aAAa,MACzB,QAAQ,IAAI,WAAW,MACvB,OAAO,IAAI,WAAW,gBACtB,UAAU,IAAI,WAAW,gBACzB;AACA,QAAM,QAAQ,KAAK;AACnB,QAAM,eAAe,KAAK,IAAI,GAAG;AAEjC,QAAM,KAAK;AACX,UAAQ,KAAK;AACb,OAAK,KAAK;AAEV,MAAI,YAAY;AAChB,MAAI,WAAW;AAef,QAAM,YAAY,eAAe;AAGjC,QAAM,cAAc;AAMpB,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI,UAAW,MAAK,UAAU;AAE9B,QAAM,YAAY,KAAM,YAAY;AACpC,QAAM,UAAU,YAAY;AAC5B,MAAI,WAAW,YAAY;AAC3B,MAAI,UAAU;AAEd,MAAI,MAAM,OAAO;AAEjB,MAAI,SAAS;AACb,WAAS,QAAQ,OAAO,QAAQ,OAAO,SAAS,GAAG;AACjD,MAAE;AAAA;AAEJ,WAAS,IAAI;AAEb,YAAU,UAAU;AAEpB,SAAO;AAEP,QAAM,SAAS,OAAO;AACtB,WAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,gBAAY;AACV,YAAM,IAAI,OAAO;AACjB,YAAM,QAAS,MAAK,QAAQ;AAC5B,UAAI,IAAK,KAAK,SAAU;AACxB,UAAI,KAAK,OAAO,OAAO;AACrB,cAAM,QAAQ;AACd;AAAA;AAGF,YAAM,OAAO,MAAM,IAAI,IAAI,QAAQ;AACnC,aAAO,KAAK,MAAM,GAAG;AAEnB,aAAK;AACL,YAAI,IAAI;AAAG,eAAK;AAChB,YAAI,KAAK,OAAO,OAAO;AACrB,gBAAM,QAAQ;AACd;AAAA;AAAA;AAGJ,aAAO;AACP,YAAM;AACN,UAAI,WAAW,KAAK,MAAM;AACxB,gBAAQ,KAAK;AACb,aAAK,KAAK;AAAA,aACL;AAGL,aAAK,KAAK;AACV,mBAAW,YAAY;AACvB,oBAAY;AACZ,eAAO;AAAA;AAAA;AAAA;AAMb,SAAO;AACP,SAAO;AAEP,YAAU,UAAU;AACpB,SAAO,UAAU;AAEjB,kBAAgB,MAAM;AACpB,iBAAa,MAAM;AAEnB,QAAI,WAAW;AAAG,mBAAa,QAAQ;AAAA;AAClC,kBAAY;AAEjB,gBAAY;AAEZ,WAAO,YAAY,GAAG;AAGpB,YAAM,aAAa,YAAY;AAC/B,UAAI,WAAW,KAAK;AAClB,kBAAU,UAAU;AACpB,kBAAU,eAAe,OAAO,GAAG;AACnC,kBAAU;AAAA;AAEZ,oBAAc;AACd,kBAAY;AAAA;AAKd,QAAI,WAAW,WAAW,WAAW;AACnC,UAAI,WAAW;AACb,iBAAS;AACT,kBAAW,MAAK,UAAU;AAC1B,oBAAY;AAAA,aACP;AACL,UAAE;AACF,kBAAU,WAAW,OAAQ,KAAK,SAAW,MAAK,UAAU;AAAA;AAAA;AAIhE,QAAI,QAAQ,SAAS;AAEnB,aAAO,WAAW,GAAG;AAGnB,cAAM,aAAa,YAAY;AAC/B,YAAI,WAAW,KAAK;AAClB,oBAAU,UAAU;AACpB,oBAAU,eAAe,OAAO,GAAG;AACnC,oBAAU;AAAA;AAEZ,sBAAc;AACd,oBAAY;AAAA;AAGd,UAAI,UAAU,GAAG;AACf,kBAAU,UAAU;AACpB,kBAAU,eAAe,OAAO,GAAG;AACnC,kBAAU;AAAA;AAAA;AAAA;AAAA;AAMlB,IAAO,oBAAQ;;;ACxMR,0BAA0B,GAAG,GAAG,GAAG;AACxC,SAAS,KAAK,IAAK,QAAY,KAAK,IAAK,MAAW,KAAK;AAAA;AAGpD,8BAA8B,GAAG,GAAG,GAAG,GAAG;AAC/C,SAAQ,KAAK,IAAM,IAAI,MAAU,KAAI,QAAS,IAAO,KAAI,QAAS;AAAA;AAG7D,0BAA0B,GAAG,GAAG,GAAG;AACxC,SAAS,KAAK,KAAM,IAAM,IAAI,MAAS,KAAK;AAAA;;;ACP9C,eAAe,OAAO,KAAK,KAAK;AAC9B,SAAO,QAAQ,MAAM,MAAM,QAAQ,MAAM,MAAM;AAAA;AAGjD,aAAa,OAAO;AAClB,SAAO,QAAQ;AAAA;AAGjB,iBAAiB,MAAM,KAAK,UAAU;AACpC,MAAI,KAAK;AACT,MAAI,MAAM;AAEV,QAAM,OAAO,KAAK;AAClB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,WAAS,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI;AAC5C,UAAM,MAAM,KAAK;AACjB,UAAM,KAAK,IAAI;AACf,UAAM,QAAS,KAAK,KAAO,MAAK;AAChC,QAAI,SAAS;AAAK;AAElB,QAAI,OAAO;AACX,QAAI,UAAU;AACZ,cAAQ,QAAQ,IAAI,IAAI,KAAK;AAC7B,UAAI,QAAQ;AAAK;AAAA;AAGnB,YAAQ,QAAQ,IAAI,IAAI,KAAK;AAC7B,QAAI,QAAQ;AAAK;AAEjB,YAAQ,QAAQ,IAAI,IAAI,KAAK;AAC7B,QAAI,QAAQ;AAAK;AAEjB,YAAQ,QAAQ,IAAI,IAAI,KAAK;AAC7B,QAAI,QAAQ;AAAK;AACjB,UAAM;AACN,SAAK;AAAA;AAEP,OAAK,MAAM;AACX,OAAK,KAAK;AAAA;AAGZ,sBAAsB;AACpB,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA;AAAA;AAWT,yBAAyB,MAAM,QAAQ;AACrC,QAAM,WAAW,WAAW,WAAW,OAAO;AAC9C,QAAM,OAAO,IAAI,MAAM;AACvB,QAAM,OAAO,KAAK;AAMlB,MAAI,WAAW,YAAY;AACzB,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC7B,YAAM,QAAQ,KAAK;AACnB,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,IAAK;AACzB,YAAM,IAAI,QAAQ;AAGlB,YAAM,QAAQ,qBAAqB,GAAG,GAAG,GAAG;AAC5C,UAAI,MAAM,SAAS,OAAO,KAAK,SAAU,KAAK,SAAS;AACvD,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI;AAAA;AAAA,aAIC,WAAW,UAAU;AAC5B,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC7B,YAAM,QAAQ,KAAK;AACnB,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,IAAK;AACzB,YAAM,IAAI,QAAQ;AAGlB,YAAM,QAAQ,iBAAiB,GAAG,GAAG;AACrC,UAAI,MAAM,SAAS,OAAO,KAAK,SAAU,KAAK,SAAS;AACvD,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI;AAAA;AAAA,SAED;AACL,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC7B,YAAM,QAAQ,KAAK;AACnB,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,IAAK;AACzB,YAAM,IAAI,QAAQ;AAGlB,YAAM,QAAQ,iBAAiB,GAAG,GAAG;AACrC,UAAI,MAAM,SAAS,OAAO,KAAK,SAAU,KAAK,SAAS;AACvD,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI;AAAA;AAAA;AAGR,SAAO;AAAA;AAGM,kBAAkB,MAAM,WAAW,OAAO,IAAI;AAC3D,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,cAAc;AAAA,MACZ;AAEJ,MAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ;AACzB,UAAM,IAAI,MAAM;AAAA;AAElB,MAAI,CAAE,iBAAgB,eAAe,CAAE,iBAAgB,oBAAoB;AACzE,UAAM,IAAI,MAAM;AAAA;AAGlB,QAAM,OAAO,IAAI,YAAY,KAAK;AAElC,MAAI,UAAU,KAAK,YAAY;AAO/B,QAAM,WAAW,WAAW;AAC5B,QAAM,OAAO,gBAAgB,MAAM;AACnC,QAAM,WAAW,KAAK;AACtB,QAAM,mBAAmB,WAAW;AACpC,QAAM,OAAO,IAAI,YAAY,WAAW;AAGxC,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjC,UAAM,MAAM,KAAK;AACjB,QAAI,OAAO,MAAM;AACf,UAAI,IAAI,IAAM,IAAI;AAClB,UAAI;AAAU,YAAI,MAAM;AACxB,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,WAAK,aAAa;AAAA;AAAA;AAItB,MAAI,IAAI,aAAa,UAAU,OAAO;AACpC,cAAU;AAAA;AAGZ,MAAI,IAAI;AACR,SAAO,IAAI,UAAU,GAAG,EAAE,GAAG;AAC3B,SAAK,GAAG,KAAK,IAAI;AACjB,SAAK,IAAI,GAAG,KAAK;AACjB,QAAI;AAAS,WAAK,GAAG,MAAM,KAAK,KAAK,KAAK,GAAG;AAAA;AAE/C,MAAI;AAAS,SAAK,GAAG,MAAM,KAAK,KAAK,KAAK,GAAG;AAE7C,MAAI,GAAG,GAAG;AAEV,OAAK,IAAI,GAAG,IAAI,SAAS,EAAE,GAAG;AAC5B,YAAQ,MAAM,GAAG;AAEjB,QAAI,MAAM,KAAK,GAAG;AAClB,SAAK,IAAI,EAAE,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI;AACjC,WAAK,KAAK;AACV,UAAI,KAAM,IAAI,KAAK,KAAM,OAAO;AAAK;AACrC,WAAK,KAAK;AAAA;AAEZ,SAAK,KAAK;AAAA;AAIZ,MAAI,UAAU,UAAU;AACxB,OAAK,IAAI,GAAG,IAAI,WAAW;AACzB,QAAI;AAEJ,eAAS;AACP,UAAI,KAAK,KAAK;AACd,WAAK,KAAK;AAEV,UAAI,GAAG,MAAM,GAAG,OAAO,KAAK,GAAG,IAAI,OAAO,GAAG;AAAI;AACjD,UAAI,GAAG,OAAO;AACO,aAAK,KAAK,KAAK,KAAK,KAAK;AAAA,WACf;AAC7B,gBAAQ,MAAM,IAAI;AAClB,WAAG,KAAK;AAAA;AAGV,UAAI,MAAM,KAAK,IAAI;AACnB,WAAK,IAAI,GAAI,MAAK,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI;AAC3C,YAAI,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI;AAAK;AACjE,YAAI,OAAO,KAAM,IAAI,KAAK,KAAM;AAAK;AACrC,aAAK,KAAK;AAAA;AAEZ,WAAK,KAAK;AAAA;AAIZ,QAAI,KAAK,KAAK,GAAG;AACjB,QAAI,KAAK,GAAG;AACZ,QAAI,KAAK,GAAG;AACZ,QAAI,IAAI,IAAO,MAAK;AACpB,QAAI;AAAU,SAAG,KAAK,IAAK,MAAK,GAAG,KAAK,KAAK,GAAG;AAChD,OAAG,KAAK,IAAK,MAAK,GAAG,KAAK,KAAK,GAAG;AAClC,OAAG,KAAK,IAAK,MAAK,GAAG,KAAK,KAAK,GAAG;AAClC,OAAG,KAAK,IAAK,MAAK,GAAG,KAAK,KAAK,GAAG;AAClC,OAAG,OAAO,GAAG;AACb,OAAG,MAAM,EAAE;AAGX,SAAK,GAAG,IAAI,KAAK,GAAG;AACpB,SAAK,GAAG,IAAI,KAAK,GAAG;AACpB,OAAG,MAAM;AAAA;AAIX,MAAI,UAAU;AAGd,MAAI,IAAI;AACR,OAAK,IAAI,KAAK,EAAE,GAAG;AACjB,QAAI,IAAI,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,GAAG;AACzC,QAAI,IAAI,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,GAAG;AACzC,QAAI,IAAI,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,GAAG;AAEzC,QAAI,IAAI;AACR,QAAI,UAAU;AACZ,UAAI,MAAM,KAAK,MAAM,KAAK,GAAG,KAAK,GAAG;AACrC,UAAI,aAAa;AACf,cAAM,YAAY,OAAO,gBAAgB,WAAW,cAAc;AAClE,YAAI,KAAK,YAAY,IAAO;AAAA;AAE9B,UAAI,cAAc,KAAK,qBAAqB;AAC1C,YAAI,IAAI,IAAI;AACZ,YAAI;AAAA;AAAA;AAIR,UAAM,QAAQ,WAAW,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG;AAC/C,UAAM,SAAS,gBAAgB,SAAS;AACxC,QAAI,CAAC;AAAQ,cAAQ,KAAK;AAC1B,QAAK,KAAI,KAAK,GAAG,OAAO;AAAG;AAAA;AAG7B,SAAO;AAAA;AAGT,yBAAyB,SAAS,OAAO;AACvC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,IAAI,QAAQ;AAClB,QAAI,aACF,EAAE,OAAO,MAAM,MAAM,EAAE,OAAO,MAAM,MAAM,EAAE,OAAO,MAAM;AAC3D,QAAI,eACF,EAAE,UAAU,KAAK,MAAM,UAAU,IAAI,EAAE,OAAO,MAAM,KAAK;AAC3D,QAAI,cAAc;AAAc,aAAO;AAAA;AAEzC,SAAO;AAAA;;;ACpQF,kCAAkC,GAAG,GAAG;AAC7C,MAAI,MAAM;AACV,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,UAAM,KAAK,EAAE,KAAK,EAAE;AACpB,WAAO,KAAK;AAAA;AAEd,SAAO;AAAA;;;ACvCT,mBAAmB,MAAM,MAAM;AAC7B,SAAO,OAAO,IAAI,KAAK,MAAM,OAAO,QAAQ,OAAO;AAAA;AAG9C,qBACL,MACA,CAAE,WAAW,GAAG,aAAa,IAAI,cAAc,QAAS,IACxD;AACA,QAAM,OAAO,IAAI,YAAY,KAAK;AAClC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,QAAQ,KAAK;AACnB,QAAI,IAAK,SAAS,KAAM;AACxB,QAAI,IAAK,SAAS,KAAM;AACxB,QAAI,IAAK,SAAS,IAAK;AACvB,QAAI,IAAI,QAAQ;AAEhB,QAAI,UAAU,GAAG;AACjB,QAAI,aAAa;AACf,YAAM,YAAY,OAAO,gBAAgB,WAAW,cAAc;AAClE,UAAI,KAAK,YAAY,IAAO;AAAA;AAE9B,QAAI,UAAU,GAAG;AACjB,QAAI,UAAU,GAAG;AACjB,QAAI,UAAU,GAAG;AAEjB,SAAK,KAAM,KAAK,KAAO,KAAK,KAAO,KAAK,IAAM,KAAK;AAAA;AAAA;AAIhD,sBAAsB,MAAM,SAAS,SAAS,UAAU;AAC7D,MAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ;AACzB,UAAM,IAAI,MAAM;AAAA;AAElB,MAAI,CAAE,iBAAgB,eAAe,CAAE,iBAAgB,oBAAoB;AACzE,UAAM,IAAI,MAAM;AAAA;AAElB,MAAI,QAAQ,SAAS,KAAK;AACxB,UAAM,IAAI,MAAM;AAAA;AAGlB,QAAM,OAAO,IAAI,YAAY,KAAK;AAClC,QAAM,SAAS,KAAK;AACpB,QAAM,WAAW,WAAW,WAAW,OAAO;AAC9C,QAAM,QAAQ,IAAI,WAAW;AAC7B,QAAM,QAAQ,IAAI,MAAM;AACxB,QAAM,WAAW,WAAW;AAI5B,MAAI,WAAW,YAAY;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,QAAQ,KAAK;AACnB,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,IAAK;AACzB,YAAM,IAAI,QAAQ;AAClB,YAAM,MAAM,qBAAqB,GAAG,GAAG,GAAG;AAC1C,YAAM,MAAM,OAAO,QAAQ,MAAM,OAAQ,MAAM,OAAO,sBAAsB,GAAG,GAAG,GAAG,GAAG;AACxF,YAAM,KAAK;AAAA;AAAA,SAER;AACL,UAAM,gBAAgB,WAAW,WAAW,mBAAmB;AAC/D,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,QAAQ,KAAK;AACnB,YAAM,IAAK,SAAS,KAAM;AAC1B,YAAM,IAAK,SAAS,IAAK;AACzB,YAAM,IAAI,QAAQ;AAClB,YAAM,MAAM,cAAc,GAAG,GAAG;AAChC,YAAM,MAAM,OAAO,QAAQ,MAAM,OAAQ,MAAM,OAAO,qBAAqB,GAAG,GAAG,GAAG;AACpF,YAAM,KAAK;AAAA;AAAA;AAIf,SAAO;AAAA;AAGT,+BAA+B,GAAG,GAAG,GAAG,GAAG,SAAS;AAClD,MAAI,IAAI;AACR,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,MAAM,QAAQ;AACpB,UAAM,KAAK,IAAI;AACf,QAAI,UAAU,KAAI,KAAK;AACvB,QAAI,UAAU;AAAS;AACvB,UAAM,KAAK,IAAI;AACf,eAAW,KAAI,KAAK;AACpB,QAAI,UAAU;AAAS;AACvB,UAAM,KAAK,IAAI;AACf,eAAW,KAAI,KAAK;AACpB,QAAI,UAAU;AAAS;AACvB,UAAM,KAAK,IAAI;AACf,eAAW,KAAI,KAAK;AACpB,QAAI,UAAU;AAAS;AACvB,cAAU;AACV,QAAI;AAAA;AAEN,SAAO;AAAA;AAGT,8BAA8B,GAAG,GAAG,GAAG,SAAS;AAC9C,MAAI,IAAI;AACR,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAM,MAAM,QAAQ;AACpB,UAAM,KAAK,IAAI;AACf,QAAI,UAAU,KAAI,KAAK;AACvB,QAAI,UAAU;AAAS;AACvB,UAAM,KAAK,IAAI;AACf,eAAW,KAAI,KAAK;AACpB,QAAI,UAAU;AAAS;AACvB,UAAM,KAAK,IAAI;AACf,eAAW,KAAI,KAAK;AACpB,QAAI,UAAU;AAAS;AACvB,cAAU;AACV,QAAI;AAAA;AAEN,SAAO;AAAA;AAGF,6BAA6B,SAAS,aAAa,YAAY,GAAG;AACvE,MAAI,CAAC,QAAQ,UAAU,CAAC,YAAY;AAAQ;AAE5C,QAAM,aAAa,QAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG;AACjD,QAAM,cAAc,YAAY;AAChC,QAAM,MAAM,QAAQ,GAAG;AACvB,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,QAAI,QAAQ,YAAY;AACxB,QAAI,MAAM,SAAS,KAAK;AAEtB,cAAQ,CAAC,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AAAA,eAC9B,MAAM,SAAS,KAAK;AAE7B,cAAQ,MAAM,MAAM,GAAG;AAAA,WAClB;AAEL,cAAQ,MAAM;AAAA;AAEhB,UAAM,IAAI,8BACR,YACA,MAAM,MAAM,GAAG,IACf;AAEF,UAAM,MAAM,EAAE;AACd,UAAM,aAAa,EAAE;AACrB,QAAI,aAAa,KAAK,cAAc,aAAa;AAC/C,cAAQ,OAAO;AAAA;AAAA;AAAA;AAKrB,cAAa,GAAG;AACd,SAAO,IAAI;AAAA;AAGN,2BACL,QACA,OACA,aAAa,0BACb;AACA,MAAI,UAAU;AACd,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,eAAe,OAAO;AAC5B,UAAM,OAAO,WAAW,OAAO;AAC/B,QAAI,OAAO,SAAS;AAClB,gBAAU;AACV,qBAAe;AAAA;AAAA;AAGnB,SAAO;AAAA;AAGF,uCACL,QACA,OACA,aAAa,0BACb;AACA,MAAI,UAAU;AACd,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,eAAe,OAAO;AAC5B,UAAM,OAAO,WAAW,OAAO;AAC/B,QAAI,OAAO,SAAS;AAClB,gBAAU;AACV,qBAAe;AAAA;AAAA;AAGnB,SAAO,CAAC,cAAc;AAAA;AAGjB,sBACL,QACA,OACA,aAAa,0BACb;AACA,SAAO,OAAO,kBAAkB,QAAQ,OAAO;AAAA;;;AP7LjD,oBAAoB,MAAM,IAAI;AAC5B,QAAM,CAAE,kBAAkB,MAAM,OAAO,QAAS;AAGhD,QAAM,SAAS,aAAa;AAG5B,QAAM,QAAQ;AACd,QAAM,QAAQ,IAAI,WAAW;AAC7B,QAAM,OAAO,IAAI,WAAW;AAC5B,QAAM,UAAU,IAAI,WAAW;AAE/B,MAAI,UAAU;AAEd,SAAO;AAAA,IACL,QAAQ;AACN,aAAO;AACP,gBAAU;AAAA;AAAA,IAEZ,SAAS;AACP,aAAO,UAAU,kBAAU;AAAA;AAAA,IAE7B,QAAQ;AACN,aAAO,OAAO;AAAA;AAAA,IAEhB,YAAY;AACV,aAAO,OAAO;AAAA;AAAA,QAEZ,SAAS;AACX,aAAO,OAAO;AAAA;AAAA,QAEZ,SAAS;AACX,aAAO;AAAA;AAAA,IAET;AAAA,IACA,WAAW,OAAO,OAAO,QAAQ,OAAO,IAAI;AAC1C,YAAM;AAAA,QACJ,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAEJ,UAAI,QAAQ;AACZ,UAAI,MAAM;AAGR,YAAI,CAAC,SAAS;AAEZ,kBAAQ;AAIR;AACA,oBAAU;AAAA;AAAA,aAEP;AAEL,gBAAQ,QAAQ,KAAK;AAAA;AAGvB,cAAQ,KAAK,IAAI,GAAG,KAAK,MAAM;AAC/B,eAAS,KAAK,IAAI,GAAG,KAAK,MAAM;AAGhC,UAAI,OAAO;AACT,YAAI,CAAC,SAAS;AACZ,gBAAM,IAAI,MAAM;AAAA;AAElB,sCACE,QACA,OACA,QACA,SACA;AAEF,yBAAiB,QAAQ;AACzB,YAAI,UAAU,GAAG;AACf,4BAAkB,QAAQ;AAAA;AAAA;AAI9B,YAAM,YAAY,KAAK,MAAM,QAAQ;AACrC,8BACE,QACA,SACA,WACA,aACA;AAGF,YAAM,qBAAqB,QAAQ,YAAY,CAAC;AAChD,4BACE,QACA,OACA,QACA,qBAAqB,UAAU;AAEjC,UAAI;AAAoB,yBAAiB,QAAQ;AACjD,mBACE,QACA,OACA,OACA,QACA,YACA,OACA,MACA;AAAA;AAAA;AAKN,yBAAuB;AACrB,kBAAc,QAAQ;AAAA;AAAA;AAI1B,iCACE,QACA,SACA,OACA,aACA,kBACA;AACA,SAAO,UAAU;AACjB,SAAO,UAAU;AACjB,SAAO,UAAU;AAEjB,MAAI,mBAAmB,GAAG;AACxB,uBAAmB;AACnB,kBAAc;AAAA;AAGhB,MAAI,QAAQ;AACZ,MAAI,CAAC,aAAa;AAChB,aAAS;AACT,WAAO;AAAA,SACF;AACL,aAAS;AACT,WAAO;AAAA;AAGT,MAAI,WAAW,GAAG;AAChB,WAAO,UAAU;AAAA;AAGnB,WAAS;AAET,QAAM,YAAY;AAGlB,SAAO,UACL,IACE,OACA,YACA;AAGJ,cAAY,QAAQ;AACpB,SAAO,UAAU,oBAAoB;AACrC,SAAO,UAAU;AAAA;AAGnB,uCACE,QACA,OACA,QACA,SACA,aAAa,GACb;AACA,QAAM,uBAAuB;AAC7B,QAAM,WAAW;AACjB,QAAM,uBAAuB,eAAe,QAAQ,UAAU;AAC9D,QAAM,SACH,wBAAwB,IACvB,aAAa,KAAM,IACpB,YAAY,IACb;AACF,QAAM,uBAAuB;AAC7B,QAAM,mBAAmB;AACzB,cAAY,QAAQ;AACpB,cAAY,QAAQ;AACpB,SAAO,WAAW,CAAC,QAAQ,sBAAsB;AAAA;AAGnD,2BAA2B,QAAQ,QAAQ;AACzC,SAAO,UAAU;AACjB,SAAO,UAAU;AACjB,SAAO,UAAU;AACjB,gBAAc,QAAQ;AACtB,SAAO,UAAU;AACjB,SAAO,UAAU;AACjB,cAAY,QAAQ;AACpB,SAAO,UAAU;AAAA;AAGnB,0BAA0B,QAAQ,SAAS;AACzC,QAAM,mBAAmB,KAAK,eAAe,QAAQ;AACrD,WAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,QAAI,QAAQ,CAAC,GAAG,GAAG;AACnB,QAAI,IAAI,QAAQ,QAAQ;AACtB,cAAQ,QAAQ;AAAA;AAElB,WAAO,UAAU,MAAM;AACvB,WAAO,UAAU,MAAM;AACvB,WAAO,UAAU,MAAM;AAAA;AAAA;AAI3B,+BAA+B,QAAQ,OAAO,QAAQ,cAAc;AAClE,SAAO,UAAU;AAEjB,cAAY,QAAQ;AACpB,cAAY,QAAQ;AACpB,cAAY,QAAQ;AACpB,cAAY,QAAQ;AAEpB,MAAI,cAAc;AAChB,UAAM,YAAY;AAClB,UAAM,SAAS;AACf,UAAM,UAAU,eAAe,aAAa,UAAU;AAEtD,WAAO,UACL,MACE,YACA,SACA,IACA;AAAA,SAEC;AAEL,WAAO,UAAU;AAAA;AAAA;AAIrB,sBACE,QACA,OACA,OACA,QACA,aAAa,GACb,OACA,MACA,SACA;AACA,oBAAU,OAAO,QAAQ,OAAO,YAAY,QAAQ,OAAO,MAAM;AAAA;AAKnE,qBAAqB,QAAQ,OAAO;AAClC,SAAO,UAAU,QAAQ;AACzB,SAAO,UAAW,SAAS,IAAK;AAAA;AAGlC,uBAAuB,QAAQ,MAAM;AACnC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAO,UAAU,KAAK,WAAW;AAAA;AAAA;AAIrC,wBAAwB,QAAQ;AAC9B,SAAO,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,UAAU;AAAA;AAchD,IAAO,cAAQ;", "names": []}