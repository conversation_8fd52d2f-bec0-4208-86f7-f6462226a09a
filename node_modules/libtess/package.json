{"name": "libtess", "version": "1.2.2", "description": "Polygon tesselation library, ported from SGI's GLU implementation.", "homepage": "https://github.com/brendankenny/libtess.js/", "repository": {"type": "git", "url": "https://github.com/brendankenny/libtess.js.git"}, "main": "libtess.min.js", "files": ["libtess.min.js", "libtess.cat.js", "libtess.debug.js", "LICENSE"], "scripts": {"prepublish": "gulp build", "test": "gulp test"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://vector.io/)"], "license": "SGI-B-2.0", "bugs": "https://github.com/brendankenny/libtess.js/issues", "keywords": ["polygon", "tesselation", "tessellation", "triangulation", "GLUtesselator"], "devDependencies": {"browserify": "^12.0.1", "chai": "^3.3.0", "glob": "^6.0.1", "google-closure-compiler": "^20151216.0.0", "gulp": "^3.8.8", "gulp-concat": "^2.4.2", "gulp-coveralls": "^0.1.4", "gulp-filter": "^3.0.1", "gulp-istanbul": "^0.10.1", "gulp-jscs": "^3.0.1", "gulp-jshint": "^2.0.0", "gulp-mocha": "^2.1.3", "gulp-newer": "^1.1.0", "gulp-rename": "^1.2.0", "gulp-replace": "^0.5.4", "jshint": "^2.8.0", "jshint-stylish": "^2.0.1", "mocha": "^2.3.3", "rfolderify": "^1.3.0", "rocambole": "^0.7.0", "rocambole-token": "^1.2.1", "vinyl-map": "^1.0.1", "vinyl-source-stream": "^1.0.0"}}