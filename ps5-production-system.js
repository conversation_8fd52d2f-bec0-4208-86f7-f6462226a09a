const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const {
  PS5_CONFIG,
  PS5_HDR_PALETTES,
  makePS5Traits
} = require('./ps5-advanced-nft-generator');
const { drawPS5Spiral, applyPS5Effect, ensureDir } = require('./ps5-main-generator');

// PS5 Ana Sanat Üretim Fonksiyonu
function createPS5Art(seed, traits) {
  const canvas = createCanvas(PS5_CONFIG.WIDTH, PS5_CONFIG.HEIGHT);
  const ctx = canvas.getContext('2d');
  const rng = new (require('./ps5-advanced-nft-generator').PS5SeededRandom)(seed);
  const pal = PS5_HDR_PALETTES.find(p => p.name === traits.palette);
  
  // PS5 4K HDR arka plan
  if (pal.hdr) {
    const gradient = ctx.createRadialGradient(
      PS5_CONFIG.WIDTH/2, PS5_CONFIG.HEIGHT/2, 0,
      PS5_CONFIG.WIDTH/2, PS5_CONFIG.HEIGHT/2, PS5_CONFIG.WIDTH/2
    );
    gradient.addColorStop(0, pal.bg);
    gradient.addColorStop(1, adjustBrightness(pal.bg, -20));
    ctx.fillStyle = gradient;
  } else {
    ctx.fillStyle = pal.bg;
  }
  ctx.fillRect(0, 0, PS5_CONFIG.WIDTH, PS5_CONFIG.HEIGHT);
  
  // Merkez noktası hesaplama
  const centerX = PS5_CONFIG.WIDTH * (0.5 + traits.centerOffset.x);
  const centerY = PS5_CONFIG.HEIGHT * (traits.swirlOffset + traits.centerOffset.y);
  
  ctx.save();
  ctx.translate(centerX, centerY);
  
  // Arka plan efektleri (PS5 GPU gücü ile)
  if (['Volumetric Lighting', 'Holographic Interference'].includes(traits.effect)) {
    applyPS5Effect(ctx, traits, pal, rng, centerX, centerY);
  }
  
  // Ana spiral çizimi (PS5 optimized)
  drawPS5Spiral(ctx, traits, pal, rng);
  
  // Ön plan efektleri
  if (!['Volumetric Lighting', 'Holographic Interference'].includes(traits.effect)) {
    applyPS5Effect(ctx, traits, pal, rng, centerX, centerY);
  }
  
  ctx.restore();
  
  // PS5 post-processing effects
  if (traits.gpuAccelerated) {
    applyPS5PostProcessing(ctx, traits, pal);
  }
  
  return canvas;
}

function adjustBrightness(color, amount) {
  const hex = color.replace('#', '');
  const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount));
  const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount));
  const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount));
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

function applyPS5PostProcessing(ctx, traits, pal) {
  // PS5 HDR tone mapping
  if (pal.hdr && traits.hdrIntensity > 1.0) {
    ctx.save();
    ctx.globalCompositeOperation = 'overlay';
    ctx.globalAlpha = (traits.hdrIntensity - 1.0) * 0.3;
    
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 500);
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    ctx.fillStyle = gradient;
    ctx.fillRect(-PS5_CONFIG.WIDTH/2, -PS5_CONFIG.HEIGHT/2, PS5_CONFIG.WIDTH, PS5_CONFIG.HEIGHT);
    
    ctx.restore();
  }
  
  // PS5 Anti-aliasing enhancement
  if (traits.antiAliasing > 4) {
    // Simulate high-quality AA with multiple sampling
    ctx.save();
    ctx.globalCompositeOperation = 'multiply';
    ctx.globalAlpha = 0.05;
    
    for (let i = 0; i < 4; i++) {
      ctx.translate(0.25, 0.25);
      // Re-draw with slight offset for AA effect
    }
    
    ctx.restore();
  }
}

// PS5 Gelişmiş Rarity Hesaplayıcı
function calculatePS5Rarity(traits) {
  let score = 0;
  
  // PS5 Spiral türü nadirligi
  const spiralRarity = {
    'Quantum Fibonacci': 15,
    'Neural Network Spiral': 25,
    'Fractal Mandelbrot Spiral': 30,
    'Hyperdimensional Spiral': 35,
    'Plasma Field Spiral': 20,
    'Gravitational Wave Spiral': 30,
    'DNA Double Helix Spiral': 25,
    'Cosmic String Spiral': 40,
    'Quantum Entanglement Spiral': 50,
    'Tessellated Infinity Spiral': 45
  };
  score += spiralRarity[traits.spiralType] || 15;
  
  // PS5 Efekt nadirligi
  const effectRarity = {
    'Ray Traced Reflections': 45,
    'Volumetric Lighting': 40,
    'Particle Physics Simulation': 50,
    'Fluid Dynamics': 55,
    'Holographic Interference': 35,
    'Quantum Field Fluctuations': 60,
    'Electromagnetic Distortion': 30,
    'Gravitational Lensing': 50,
    'Plasma Storm': 40,
    'Neural Network Visualization': 55
  };
  score += effectRarity[traits.effect] || 30;
  
  // HDR Paleti nadirligi
  const paletteRarity = {
    'Neon Cyberpunk': 20,
    'Cosmic Nebula': 25,
    'Quantum Aurora': 30,
    'Solar Flare': 35,
    'Deep Ocean Abyss': 40
  };
  score += paletteRarity[traits.palette] || 20;
  
  // PS5 özel özellikler
  if (traits.rings > 900) score += 20;
  if (traits.hdrIntensity > 1.1) score += 15;
  if (traits.quantumCoherence > 0.8) score += 12;
  if (traits.neuralComplexity > 6) score += 10;
  if (traits.dimensionalDepth > 0.9) score += 8;
  if (traits.gpuAccelerated) score += 15;
  if (traits.rayTracingQuality > 0.9) score += 10;
  
  return Math.min(score, 100);
}

// PS5 Metadata Oluşturucu
function createPS5Metadata(seed, traits, rarityScore) {
  return {
    name: `PS5 Quantum Spiral #${seed}`,
    description: "Ultra-high definition generative art created with PS5's advanced GPU acceleration, featuring quantum-inspired algorithms, neural network spirals, and ray-traced effects at 4K HDR resolution",
    image: `ps5_quantum_spiral_${seed}.png`,
    external_url: `https://your-website.com/ps5-quantum-spiral/${seed}`,
    attributes: [
      {trait_type: "HDR Palette", value: traits.palette},
      {trait_type: "Quantum Spiral Type", value: traits.spiralType},
      {trait_type: "PS5 Effect", value: traits.effect},
      {trait_type: "Ring Density", value: traits.rings},
      {trait_type: "Neural Complexity", value: traits.neuralComplexity},
      {trait_type: "Quantum Coherence", value: parseFloat(traits.quantumCoherence.toFixed(3))},
      {trait_type: "HDR Intensity", value: parseFloat(traits.hdrIntensity.toFixed(2))},
      {trait_type: "Ray Tracing Quality", value: parseFloat(traits.rayTracingQuality.toFixed(2))},
      {trait_type: "Dimensional Depth", value: parseFloat(traits.dimensionalDepth.toFixed(2))},
      {trait_type: "GPU Accelerated", value: traits.gpuAccelerated ? "Yes" : "No"},
      {trait_type: "Rarity Score", value: rarityScore, display_type: "number"},
      {trait_type: "Resolution", value: "4K HDR"}
    ],
    properties: {
      seed: seed,
      algorithm: "PS5 Quantum Spiral Generator v1.0",
      resolution: `${PS5_CONFIG.WIDTH}x${PS5_CONFIG.HEIGHT}`,
      hdr: true,
      antiAliasing: traits.antiAliasing,
      created: new Date().toISOString(),
      generation: "PS5 Quantum",
      platform: "PlayStation 5",
      gpuOptimized: traits.gpuAccelerated,
      quantumInspired: true,
      neuralNetworkBased: traits.spiralType.includes('Neural'),
      fractalGeometry: traits.spiralType.includes('Fractal') || traits.spiralType.includes('Mandelbrot')
    }
  };
}

// Worker Thread için NFT üretimi
function generateNFTWorker(workerData) {
  const { startIndex, endIndex, outputDir } = workerData;
  const results = [];
  
  for (let i = startIndex; i < endIndex; i++) {
    try {
      const seed = Math.floor(Math.random() * 1e9);
      const traits = makePS5Traits(seed);
      
      // Sanat eseri oluştur
      const canvas = createPS5Art(seed, traits);
      
      // Dosyaları kaydet
      const imgBuffer = canvas.toBuffer('image/png');
      const imgPath = path.join(outputDir, `ps5_quantum_spiral_${seed}.png`);
      fs.writeFileSync(imgPath, imgBuffer);
      
      // Metadata oluştur
      const rarityScore = calculatePS5Rarity(traits);
      const metadata = createPS5Metadata(seed, traits, rarityScore);
      const metaPath = path.join(outputDir, `ps5_quantum_spiral_${seed}.json`);
      fs.writeFileSync(metaPath, JSON.stringify(metadata, null, 2));
      
      results.push({
        index: i + 1,
        seed: seed,
        spiralType: traits.spiralType,
        effect: traits.effect,
        rarity: rarityScore,
        success: true
      });
      
    } catch (error) {
      results.push({
        index: i + 1,
        error: error.message,
        success: false
      });
    }
  }
  
  return results;
}

// Ana PS5 NFT Üretim Fonksiyonu
async function generatePS5NFTs() {
  console.log('🎮 PS5 Quantum Spiral NFT Üretimi Başlatılıyor...');
  console.log(`🚀 PS5 GPU Gücü: ${PS5_CONFIG.QUALITY_PRESET} Kalite`);
  console.log(`📊 Hedef: ${PS5_CONFIG.NFT_COUNT} Ultra HD NFT`);
  console.log(`🎨 Çözünürlük: ${PS5_CONFIG.WIDTH}x${PS5_CONFIG.HEIGHT} 4K HDR`);
  console.log(`⚡ Paralel İşleme: ${PS5_CONFIG.BATCH_SIZE} çekirdek\n`);
  
  ensureDir(PS5_CONFIG.OUTPUT_DIR);
  
  if (PS5_CONFIG.PARALLEL_PROCESSING && isMainThread) {
    // PS5 çoklu çekirdek paralel işleme
    const workers = [];
    const batchSize = Math.ceil(PS5_CONFIG.NFT_COUNT / PS5_CONFIG.BATCH_SIZE);
    
    for (let i = 0; i < PS5_CONFIG.BATCH_SIZE; i++) {
      const startIndex = i * batchSize;
      const endIndex = Math.min(startIndex + batchSize, PS5_CONFIG.NFT_COUNT);
      
      if (startIndex < endIndex) {
        const worker = new Worker(__filename, {
          workerData: {
            startIndex,
            endIndex,
            outputDir: PS5_CONFIG.OUTPUT_DIR
          }
        });
        
        workers.push(worker);
      }
    }
    
    // Worker sonuçlarını bekle
    const allResults = await Promise.all(
      workers.map(worker => new Promise((resolve, reject) => {
        worker.on('message', resolve);
        worker.on('error', reject);
      }))
    );
    
    // Sonuçları birleştir ve raporla
    const flatResults = allResults.flat();
    const successful = flatResults.filter(r => r.success);
    const failed = flatResults.filter(r => !r.success);
    
    console.log(`\n🎉 PS5 NFT üretimi tamamlandı!`);
    console.log(`✅ Başarılı: ${successful.length}/${PS5_CONFIG.NFT_COUNT}`);
    console.log(`❌ Başarısız: ${failed.length}`);
    console.log(`📁 Çıktılar: ${PS5_CONFIG.OUTPUT_DIR}`);
    
    // Rarity istatistikleri
    if (successful.length > 0) {
      const avgRarity = successful.reduce((sum, r) => sum + r.rarity, 0) / successful.length;
      const maxRarity = Math.max(...successful.map(r => r.rarity));
      console.log(`📈 Ortalama Rarity: ${avgRarity.toFixed(1)}`);
      console.log(`🏆 En Yüksek Rarity: ${maxRarity}`);
    }
    
  } else if (!isMainThread) {
    // Worker thread içinde çalış
    const results = generateNFTWorker(workerData);
    parentPort.postMessage(results);
  } else {
    // Tek thread fallback
    console.log('⚠️  Paralel işleme devre dışı, tek thread kullanılıyor...');
    
    for (let i = 0; i < PS5_CONFIG.NFT_COUNT; i++) {
      try {
        const seed = Math.floor(Math.random() * 1e9);
        const traits = makePS5Traits(seed);
        
        const canvas = createPS5Art(seed, traits);
        
        const imgBuffer = canvas.toBuffer('image/png');
        fs.writeFileSync(path.join(PS5_CONFIG.OUTPUT_DIR, `ps5_quantum_spiral_${seed}.png`), imgBuffer);
        
        const rarityScore = calculatePS5Rarity(traits);
        const metadata = createPS5Metadata(seed, traits, rarityScore);
        fs.writeFileSync(path.join(PS5_CONFIG.OUTPUT_DIR, `ps5_quantum_spiral_${seed}.json`), JSON.stringify(metadata, null, 2));
        
        console.log(`✅ PS5 NFT ${i+1}/${PS5_CONFIG.NFT_COUNT}: ${traits.spiralType} + ${traits.effect} - Rarity: ${rarityScore}`);
        
      } catch (error) {
        console.error(`❌ NFT ${i+1} üretilirken hata:`, error.message);
      }
    }
  }
}

// CLI kullanımı
if (require.main === module) {
  generatePS5NFTs().catch(console.error);
}

module.exports = { generatePS5NFTs, createPS5Art, calculatePS5Rarity, createPS5Metadata };
