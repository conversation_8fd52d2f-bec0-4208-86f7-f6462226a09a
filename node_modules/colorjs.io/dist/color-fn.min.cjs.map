{"version": 3, "file": "color-fn.min.cjs", "sources": ["../src/multiply-matrices.js", "../src/util.js", "../src/hooks.js", "../src/adapt.js", "../src/defaults.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/deltaE/deltaEJz.js", "../src/spaces/ictcp.js", "../src/deltaE/deltaEITP.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/toGamut.js", "../src/to.js", "../src/serialize.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/luminance.js", "../src/contrast/WCAG21.js", "../src/contrast/APCA.js", "../src/contrast/Michelson.js", "../src/contrast/Weber.js", "../src/contrast/Lstar.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js", "../src/contrast.js", "../src/variations.js", "../src/equals.js"], "sourcesContent": ["// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n"], "names": ["multiplyMatrices", "A", "B", "m", "length", "Array", "isArray", "map", "x", "p", "B_cols", "_", "i", "product", "row", "col", "ret", "c", "isString", "str", "type", "o", "Object", "prototype", "toString", "call", "match", "toLowerCase", "serializeNumber", "n", "precision", "unit", "isNone", "integer", "digits", "Math", "log10", "abs", "multiplier", "floor", "toPrecision", "Number", "isNaN", "none", "<PERSON><PERSON><PERSON>", "angleFactor", "deg", "grad", "rad", "PI", "turn", "interpolate", "start", "end", "mapRange", "from", "to", "value", "interpolateInv", "copySign", "sign", "spow", "base", "exp", "zdiv", "d", "<PERSON>s", "add", "name", "callback", "first", "arguments", "for<PERSON>ach", "this", "run", "env", "context", "hooks", "WHITES", "D50", "D65", "<PERSON><PERSON><PERSON><PERSON>", "adapt", "W1", "W2", "XYZ", "options", "TypeError", "M", "defaults", "gamut_mapping", "deltaE", "verbose", "globalThis", "process", "NODE_ENV", "warn", "msg", "console", "noneTypes", "Set", "coerceCoords", "space", "format", "coords", "types", "entries", "id", "coordMeta", "coordGrammar", "arg", "providedType", "find", "has", "coordName", "raw", "fromRange", "range", "to<PERSON><PERSON><PERSON>", "refRange", "util.mapRange", "parse", "meta", "String", "trim", "color", "parsed", "isNumberRegex", "unitValueRegex", "singleArgument", "parts", "args", "replace", "$0", "rawArg", "unitlessArg", "slice", "test", "NaN", "startsWith", "alpha", "push", "rawName", "rawArgs", "util.parseFunction", "shift", "alternateId", "substring", "ids", "indexOf", "pop", "ColorSpace", "all", "colorSpec", "getFormat", "includes", "filter", "specId", "keys", "assign", "formatId", "spaceId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registryId", "registry", "cssId", "formats", "lastAlpha", "arr", "getColor", "get", "undefined", "constructor", "aliases", "fromBase", "toBase", "white", "gamutSpace", "isPolar", "isUnbounded", "inGamut", "referred", "defineProperty", "<PERSON><PERSON><PERSON>", "reverse", "writable", "enumerable", "configurable", "epsilon", "equals", "values", "every", "min", "max", "coord", "processFormat", "connectionSpace", "connectionSpaceIndex", "myPath", "path", "otherPath", "Error", "getMinCoords", "static", "register", "alias", "alternatives", "resolveCoord", "ref", "workingSpace", "coordType", "split", "coordId", "index", "normalizedCoord", "join", "s", "coordFormats", "outputType", "suffix", "serializeCoords", "xyz_d65", "y", "z", "RGBColorSpace", "r", "g", "b", "XYZ_D65", "toXYZ_M", "fromXYZ_M", "rgb", "xyz", "super", "getAll", "prop", "setAll", "set", "object", "returns", "XYZ_D50", "ε3", "κ", "lab", "l", "a", "xyz_d50", "f", "cbrt", "Lab", "pow", "constrain", "angle", "lch", "h", "hue", "L", "atan2", "sqrt", "constrainAngle", "LCH", "Lightness", "Chroma", "<PERSON><PERSON>", "cos", "sin", "Gfactor", "π", "r2d", "d2r", "pow7", "x2", "deltaE2000", "sample", "kL", "kC", "kH", "L1", "a1", "b1", "C1", "L2", "a2", "b2", "C2", "C7", "G", "adash1", "adash2", "Cdash1", "Cdash2", "h1", "h2", "hdiff", "hsum", "habs", "hdash", "Ldash", "Cdash", "Cdash7", "lsq", "SL", "SC", "T", "SH", "RC", "dE", "XYZtoLMS_M", "LMStoXYZ_M", "LMStoLab_M", "LabtoLMS_M", "OKLab", "LMSg", "val", "LMS", "oklab", "deltaEOK", "ε", "clone", "distance", "color1", "color2", "coords1", "coords2", "reduce", "acc", "c1", "c2", "deltaE76", "deltaECMC", "H1", "H2", "C4", "F", "XYZ_Abs_D65", "v", "AbsXYZ", "c3", "pinv", "d0", "XYZtoCone_M", "ConetoXYZ_M", "ConetoIab_M", "IabtoCone_M", "Jzazbz", "jz", "az", "bz", "Xa", "Ya", "<PERSON>a", "PQLMS", "Iz", "Jz", "Xm", "Ym", "jzczhz", "cz", "hz", "jzazbz", "deltaEJz", "Jz1", "Cz1", "Hz1", "Jz2", "Cz2", "Hz2", "m1", "m2", "im1", "im2", "LMStoIPT_M", "IPTtoLMS_M", "ictcp", "ct", "cp", "LMStoICtCp", "ICtCp", "ICtCptoLMS", "deltaEITP", "I1", "T1", "P1", "I2", "T2", "P2", "<PERSON><PERSON><PERSON><PERSON>", "adaptedCoefInv", "tau", "cat16", "cat16Inv", "surroundMap", "dark", "dim", "average", "hueQuadMap", "e", "H", "rad2deg", "deg2rad", "fl", "temp", "environment", "refWhite", "adaptingLuminance", "backgroundLuminance", "surround", "discounting", "xyzW", "la", "yb", "yw", "rgbW", "nc", "k4", "flRoot", "nbb", "ncb", "dRgb", "dRgbInv", "rgbCW", "rgbAW", "aW", "viewingConditions", "fromCam16", "cam16", "J", "Q", "C", "hRad", "Hp", "hi", "hii", "ei", "eii", "invHueQuadrature", "cosh", "sinh", "<PERSON><PERSON>", "t", "et", "p1", "p2", "rgb_c", "adapted", "constant", "cabs", "unadapt", "toCam16", "xyzd65", "xyz100", "rgbA", "hp", "lo", "mid", "bisectLeft", "hueQuadrature", "j", "fromLstar", "lstar", "toHct", "hct", "attempt", "last", "Infinity", "delta", "fromHct", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertUcsAb", "log", "hrad", "deltaEHCT", "t1", "t2", "deltaEMethods", "GMAPPRESET", "method", "jnd", "deltaEMethod", "blackWhiteClamp", "channel", "toGamut", "util.isString", "spaceColor", "toGamutCSS", "hasOwnProperty", "de", "clipped", "channelMeta", "util.isNone", "mapSpace", "mappedColor", "order", "parseFloat", "calcEpsilon", "low", "high", "bounds", "COLORS", "WHITE", "BLACK", "origin", "JND", "oklchSpace", "origin_OKLCH", "black", "clip", "_color", "destColor", "spaceCoords", "util.clamp", "min_inGamut", "current", "E", "chroma", "serialize", "customOptions", "DEFAULT_FORMAT", "checkInGamut", "util.serializeNumber", "unshift", "strAlpha", "noAlpha", "commas", "REC2020Linear", "REC2020", "RGB", "P3Linear", "sRGBLinear", "KEYWORDS", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "whitesmoke", "yellow", "yellowgreen", "fill", "coordGrammarNumber", "sRGB", "rgb_number", "rgba", "rgba_number", "hex", "component", "parseInt", "collapse", "round", "collapsible", "padStart", "keyword", "P3", "supportsNone", "display_space", "CSS", "supports", "getLuminance", "contrastWCAG21", "Y1", "Y2", "blkThrs", "blkClmp", "fclamp", "Y", "linearize", "contrastAPCA", "background", "foreground", "S", "Sapc", "R", "lumTxt", "lumBg", "Ytxt", "Ybg", "BoW", "<PERSON><PERSON><PERSON><PERSON>", "denom", "<PERSON><PERSON><PERSON><PERSON>", "contrastLstar", "lab_d65", "phi", "contrastDeltaPhi", "Lstr1", "Lstr2", "deltaPhiStar", "contrast", "SQRT2", "uv", "X", "Z", "rest", "isRange", "rangeArgs", "colors", "outputSpace", "progression", "premultiplied", "interpolationSpace", "arc", "angles", "angleDiff", "angles.adjust", "HSL", "hsl", "k", "hsla", "HSV", "hsv", "hwb", "w", "sum", "A98Linear", "a98rgb", "ProPhotoLinear", "prophoto", "oklch", "U_PRIME_WHITE", "V_PRIME_WHITE", "<PERSON><PERSON>", "u", "up", "vp", "isFinite", "LCHuv", "m_r0", "m_r1", "m_r2", "m_g0", "m_g1", "m_g2", "m_b0", "m_b1", "m_b2", "distanceFromOriginAngle", "slope", "intercept", "calculateBoundingLines", "sub1", "sub2", "s1r", "s2r", "s3r", "s1g", "s2g", "s3g", "s1b", "s2b", "s3b", "r0s", "r0i", "r1s", "r1i", "g0s", "g0i", "g1s", "g1i", "b0s", "b0i", "b1s", "b1i", "calcMaxChromaHsluv", "lines", "hueRad", "r0", "r1", "g0", "g1", "b0", "hsluv", "distanceFromOrigin", "calcMaxChromaHpluv", "hpluv", "minv", "rec2100Pq", "scale", "rec2100Hlg", "CATs", "defineCAT", "toCone_M", "fromCone_M", "scaled_cone_M", "D55", "D75", "F2", "F7", "F11", "ACES", "ACEScg", "ACES_min_nonzero", "ACES_cc_max", "log2", "acescc", "algorithm", "algorithms", "contrastAlgorithms", "amount", "fallbackColor", "some", "colorRange", "maxDeltaE", "steps", "maxSteps", "rangeOptions", "totalDelta", "actualSteps", "ceil", "step", "max<PERSON><PERSON><PERSON>", "cur", "prev", "splice"], "mappings": "aACe,SAASA,EAAkBC,EAAGC,GAC5C,IAAIC,EAAIF,EAAEG,OAELC,MAAMC,QAAQL,EAAE,MAEpBA,EAAI,CAACA,IAGDI,MAAMC,QAAQJ,EAAE,MAEpBA,EAAIA,EAAEK,KAAIC,GAAK,CAACA,MAGjB,IAAIC,EAAIP,EAAE,GAAGE,OACTM,EAASR,EAAE,GAAGK,KAAI,CAACI,EAAGC,IAAMV,EAAEK,KAAIC,GAAKA,EAAEI,OACzCC,EAAUZ,EAAEM,KAAIO,GAAOJ,EAAOH,KAAIQ,IACrC,IAAIC,EAAM,EAEV,IAAKX,MAAMC,QAAQQ,GAAM,CACxB,IAAK,IAAIG,KAAKF,EACbC,GAAOF,EAAMG,EAGd,OAAOD,CACP,CAED,IAAK,IAAIJ,EAAI,EAAGA,EAAIE,EAAIV,OAAQQ,IAC/BI,GAAOF,EAAIF,IAAMG,EAAIH,IAAM,GAG5B,OAAOI,CAAG,MAOX,OAJU,IAANb,IACHU,EAAUA,EAAQ,IAGT,IAANJ,EACII,EAAQN,KAAIC,GAAKA,EAAE,KAGpBK,CACR,CChCO,SAASK,EAAUC,GACzB,MAAqB,WAAdC,EAAKD,EACb,CAOO,SAASC,EAAMC,GAGrB,OAFUC,OAAOC,UAAUC,SAASC,KAAKJ,GAE7BK,MAAM,wBAAwB,IAAM,IAAIC,aACrD,CAEO,SAASC,EAAiBC,GAAGC,UAACA,EAASC,KAAEA,IAC/C,OAAIC,EAAOH,GACH,OA2BF,SAAsBA,EAAGC,GAC/B,GAAU,IAAND,EACH,OAAO,EAER,IAAII,IAAYJ,EACZK,EAAS,EACTD,GAAWH,IACdI,EAA2C,IAAhCC,KAAKC,MAAMD,KAAKE,IAAIJ,KAEhC,MAAMK,EAAa,KAASR,EAAYI,GACxC,OAAOC,KAAKI,MAAMV,EAAIS,EAAa,IAAOA,CAC3C,CAnCQE,CAAYX,EAAGC,IAAcC,GAAQ,GAC7C,CAOO,SAASC,EAAQH,GACvB,OAAOY,OAAOC,MAAMb,IAAOA,aAAaY,QAAUZ,GAAGc,IACtD,CAKO,SAASC,EAAUf,GACzB,OAAOG,EAAOH,GAAK,EAAIA,CACxB,CAoBA,MAAMgB,EAAc,CACnBC,IAAK,EACLC,KAAM,GACNC,IAAK,IAAMb,KAAKc,GAChBC,KAAM,KAmFA,SAASC,EAAaC,EAAOC,EAAK5C,GACxC,OAAIiC,MAAMU,GACFC,EAGJX,MAAMW,GACFD,EAGDA,GAASC,EAAMD,GAAS3C,CAChC,CAMO,SAAS6C,EAAUC,EAAMC,EAAIC,GACnC,OAAON,EAAYK,EAAG,GAAIA,EAAG,GALvB,SAAyBJ,EAAOC,EAAKI,GAC3C,OAAQA,EAAQL,IAAUC,EAAMD,EACjC,CAGkCM,CAAeH,EAAK,GAAIA,EAAK,GAAIE,GACnE,CAoCO,SAASE,EAAUH,EAAID,GAC7B,OAAOpB,KAAKyB,KAAKJ,KAAQrB,KAAKyB,KAAKL,GAAQC,GAAMA,CAClD,CAQO,SAASK,EAAMC,EAAMC,GAC3B,OAAOJ,EAASxB,KAAKE,IAAIyB,IAASC,EAAKD,EACxC,CAQO,SAASE,EAAMnC,EAAGoC,GACxB,OAAc,IAANA,EAAW,EAAIpC,EAAIoC,CAC5B,CCpOO,MAAMC,EACZ,GAAAC,CAAKC,EAAMC,EAAUC,GACpB,GAA2B,iBAAhBC,UAAU,IASpBlE,MAAMC,QAAQ8D,GAAQA,EAAO,CAACA,IAAOI,SAAQ,SAAUJ,GACvDK,KAAKL,GAAQK,KAAKL,IAAS,GAEvBC,GACHI,KAAKL,GAAME,EAAQ,UAAY,QAAQD,EAExC,GAAEI,WAbF,IAAK,IAAIL,KAAQG,UAAU,GAC1BE,KAAKN,IAAIC,EAAMG,UAAU,GAAGH,GAAOG,UAAU,GAa/C,CAED,GAAAG,CAAKN,EAAMO,GACVF,KAAKL,GAAQK,KAAKL,IAAS,GAC3BK,KAAKL,GAAMI,SAAQ,SAAUH,GAC5BA,EAAS5C,KAAKkD,GAAOA,EAAIC,QAAUD,EAAIC,QAAUD,EAAKA,EACzD,GACE,EAMG,MAACE,EAAQ,IAAIX,EC/BLY,EAAS,CAErBC,IAAK,CAAC,MAAS,MAAQ,EAAS,MAA0B,OAC1DC,IAAK,CAAC,MAAS,KAAQ,EAAS,MAA0B,OAGpD,SAASC,EAAUb,GACzB,OAAI/D,MAAMC,QAAQ8D,GACVA,EAGDU,EAAOV,EACf,CAGe,SAASc,EAAOC,EAAIC,EAAIC,EAAKC,EAAU,CAAA,GAIrD,GAHAH,EAAKF,EAASE,GACdC,EAAKH,EAASG,IAETD,IAAOC,EACX,MAAM,IAAIG,UAAU,kCAAmCJ,EAAc,GAAT,SAAeA,GAAOC,EAAW,GAAN,MAAYA,EAAY,GAAP,QAGzG,GAAID,IAAOC,EAEV,OAAOC,EAGR,IAAIV,EAAM,CAACQ,KAAIC,KAAIC,MAAKC,WAwBxB,GAtBAT,EAAMH,IAAI,6BAA8BC,GAEnCA,EAAIa,IACJb,EAAIQ,KAAOL,EAAOE,KAAOL,EAAIS,KAAON,EAAOC,IAC9CJ,EAAIa,EAAI,CACP,CAAE,mBAAoB,qBAAuB,oBAC7C,CAAE,mBAAqB,mBAAqB,qBAC5C,EAAG,oBAAsB,oBAAsB,oBAGxCb,EAAIQ,KAAOL,EAAOC,KAAOJ,EAAIS,KAAON,EAAOE,MAEnDL,EAAIa,EAAI,CACP,CAAE,kBAAoB,mBAAqB,oBAC3C,EAAG,kBAAoB,mBAAoB,qBAC3C,CAAE,qBAAuB,oBAAsB,sBAKlDX,EAAMH,IAAI,2BAA4BC,GAElCA,EAAIa,EACP,OAAOxF,EAAiB2E,EAAIa,EAAGb,EAAIU,KAGnC,MAAM,IAAIE,UAAU,qEAEtB,CC5DA,IAAeE,EAAA,CACdC,cAAe,MACf5D,UAAW,EACX6D,OAAQ,KACRC,QAA+D,SAAtDC,YAAYC,SAASnB,KAAKoB,UAAUpE,cAC7CqE,KAAM,SAAeC,GAChBxB,KAAKmB,SACRC,YAAYK,SAASF,OAAOC,EAE7B,GCLF,MAAME,EAAY,IAAIC,IAAI,CAAC,WAAY,eAAgB,YAUvD,SAASC,EAAcC,EAAOC,EAAQnC,EAAMoC,GAC3C,IAAIC,EAAQnF,OAAOoF,QAAQJ,EAAME,QAAQjG,KAAI,EAAEoG,EAAIC,GAAYhG,KAC9D,IAMIQ,EANAyF,EAAeN,EAAOM,aAAajG,GACnCkG,EAAMN,EAAO5F,GACbmG,EAAeD,GAAK1F,KAaxB,GAPCA,EADG0F,EAAInE,KACAkE,EAAaG,MAAK/F,GAAKkF,EAAUc,IAAIhG,KAGrC4F,EAAaG,MAAK/F,GAAKA,GAAK8F,KAI/B3F,EAAM,CAEV,IAAI8F,EAAYN,EAAUxC,MAAQuC,EAClC,MAAM,IAAIpB,UAAU,GAAGwB,GAAgBD,EAAIK,uBAAuBD,QAAgB9C,MAClF,CAED,IAAIgD,EAAYhG,EAAKiG,MAEA,iBAAjBN,IACHK,IAAc,CAAC,EAAG,IAGnB,IAAIE,EAAUV,EAAUS,OAAST,EAAUW,SAM3C,OAJIH,GAAaE,IAChBd,EAAO5F,GAAK4G,EAAcJ,EAAWE,EAASd,EAAO5F,KAG/CQ,CAAI,IAGZ,OAAOqF,CACR,CAUe,SAASgB,EAAOtG,GAAKuG,KAACA,GAAQ,CAAA,GAC5C,IAAI/C,EAAM,CAACxD,IAAOwG,OAAOxG,IAAMyG,QAG/B,GAFA/C,EAAMH,IAAI,cAAeC,GAErBA,EAAIkD,MACP,OAAOlD,EAAIkD,MAKZ,GAFAlD,EAAImD,OJQE,SAAwB3G,GAC9B,IAAKA,EACJ,OAGDA,EAAMA,EAAIyG,OAEV,MACMG,EAAgB,aAChBC,EAAiB,oBACjBC,EAAiB,6CACvB,IAAIC,EAAQ/G,EAAIO,MAJQ,wBAMxB,GAAIwG,EAAO,CAEV,IAAIC,EAAO,GA6CX,OA5CAD,EAAM,GAAGE,QAAQH,GAAgB,CAACI,EAAIC,KACrC,IAAI5G,EAAQ4G,EAAO5G,MAAMsG,GACrBlB,EAAMwB,EAEV,GAAI5G,EAAO,CACV,IAAIK,EAAOL,EAAM,GAEb6G,EAAczB,EAAI0B,MAAM,GAAIzG,EAAK3B,QAExB,MAAT2B,GAEH+E,EAAM,IAAIrE,OAAO8F,EAAc,KAC/BzB,EAAI1F,KAAO,iBAIX0F,EAAM,IAAIrE,OAAO8F,EAAc1F,EAAYd,IAC3C+E,EAAI1F,KAAO,UACX0F,EAAI/E,KAAOA,EAEZ,MACQgG,EAAcU,KAAK3B,IAE3BA,EAAM,IAAIrE,OAAOqE,GACjBA,EAAI1F,KAAO,YAEK,SAAR0F,IACRA,EAAM,IAAIrE,OAAOiG,KACjB5B,EAAInE,MAAO,GAGR0F,EAAGM,WAAW,OAEjB7B,EAAMA,aAAerE,OAASqE,EAAM,IAAIrE,OAAOqE,GAC/CA,EAAI8B,OAAQ,GAGM,iBAAR9B,GAAoBA,aAAerE,SAC7CqE,EAAIK,IAAMmB,GAGXH,EAAKU,KAAK/B,EAAI,IAGR,CACN1C,KAAM8D,EAAM,GAAGvG,cACfmH,QAASZ,EAAM,GACfa,QAASb,EAAM,GAGfC,OAED,CACF,CI7Eca,CAAmBrE,EAAIxD,KAEhCwD,EAAImD,OAAQ,CAEf,IAAI1D,EAAOO,EAAImD,OAAO1D,KAEtB,GAAa,UAATA,EAAkB,CAErB,IAAIuC,EAAKhC,EAAImD,OAAOK,KAAKc,QAErBC,EAAcvC,EAAGgC,WAAW,MAAQhC,EAAGwC,UAAU,GAAK,KAAKxC,IAC3DyC,EAAM,CAACzC,EAAIuC,GACXN,EAAQjE,EAAImD,OAAOiB,QAAQM,QAAQ,KAAO,EAAI1E,EAAImD,OAAOK,KAAKmB,MAAQ,EAE1E,IAAK,IAAIhD,KAASiD,EAAWC,IAAK,CACjC,IAAIC,EAAYnD,EAAMoD,UAAU,SAEhC,GAAID,IACCL,EAAIO,SAASF,EAAU9C,KAAO8C,EAAUL,KAAKQ,QAAQC,GAAWT,EAAIO,SAASE,KAASzJ,QAAQ,CAIjG,MAAMoG,EAASlF,OAAOwI,KAAKxD,EAAME,QAAQjG,KAAI,CAACI,EAAGC,IAAM+D,EAAImD,OAAOK,KAAKvH,IAAM,IAE7E,IAAI6F,EAmBJ,OAjBIgD,EAAU5C,eACbJ,EAAQJ,EAAaC,EAAOmD,EAAW,QAASjD,IAG7CkB,GACHpG,OAAOyI,OAAOrC,EAAM,CAACsC,SAAU,QAASvD,UAGrCgD,EAAU9C,GAAGgC,WAAW,QAAUhC,EAAGgC,WAAW,OACnDlD,EAASO,KAAK,GAAGM,EAAMlC,gGACaqF,EAAU9C,wBAAwBA,OAEnEA,EAAGgC,WAAW,QAAUc,EAAU9C,GAAGgC,WAAW,OACnDlD,EAASO,KAAK,GAAGM,EAAMlC,qEACIqF,EAAU9C,iCAAiCA,OAGhE,CAACsD,QAAS3D,EAAMK,GAAIH,SAAQoC,QACnC,CAEF,CAGD,IAAIsB,EAAa,GACbC,EAAaxD,KAAM4C,EAAWa,SAAWzD,EAAKuC,EAClD,GAAIiB,KAAcZ,EAAWa,SAAU,CAEtC,IAAIC,EAAQd,EAAWa,SAASD,GAAYG,SAASzC,OAAOlB,GAExD0D,IACHH,EAAa,sBAAsBG,MAEpC,CAED,MAAM,IAAI9E,UAAU,sBAAsBoB,QAAWuD,GAAc,qBACnE,CAEA,IAAK,IAAI5D,KAASiD,EAAWC,IAAK,CAEjC,IAAIjD,EAASD,EAAMoD,UAAUtF,GAC7B,GAAImC,GAA0B,aAAhBA,EAAOnF,KAAqB,CACzC,IAAIwH,EAAQ,GAERrC,EAAOgE,YJUMC,EIViB7F,EAAImD,OAAOK,KJW1CqC,EAAIA,EAAIpK,OAAS,IIX+BwI,SAClDA,EAAQjE,EAAImD,OAAOK,KAAKmB,OAGzB,IAEI7C,EAFAD,EAAS7B,EAAImD,OAAOK,KAYxB,OARI5B,EAAOM,eACVJ,EAAQJ,EAAaC,EAAOC,EAAQnC,EAAMoC,IAGvCkB,GACHpG,OAAOyI,OAAOrC,EAAM,CAACsC,SAAUzD,EAAOnC,KAAMqC,UAGtC,CACNwD,QAAS3D,EAAMK,GACfH,SAAQoC,QAET,CACD,CAEF,MAGA,IAAK,IAAItC,KAASiD,EAAWC,IAC5B,IAAK,IAAIQ,KAAY1D,EAAMgE,QAAS,CACnC,IAAI/D,EAASD,EAAMgE,QAAQN,GAE3B,GAAoB,WAAhBzD,EAAOnF,KACV,SAGD,GAAImF,EAAOkC,OAASlC,EAAOkC,KAAK9D,EAAIxD,KACnC,SAGD,IAAI0G,EAAQtB,EAAOkB,MAAM9C,EAAIxD,KAE7B,GAAI0G,EAOH,OANAA,EAAMe,QAAU,EAEZlB,IACHA,EAAKsC,SAAWA,GAGVnC,CAER,CJvCG,IAAe2C,EI6CrB,MAAM,IAAIjF,UAAU,mBAAmBpE,kCACxC,CC5Le,SAASsJ,EAAU5C,GACjC,GAAIxH,MAAMC,QAAQuH,GACjB,OAAOA,EAAMtH,IAAIkK,GAGlB,IAAK5C,EACJ,MAAM,IAAItC,UAAU,yBAGjBrE,EAAS2G,KACZA,EAAQJ,EAAMI,IAIf,IAAIvB,EAAQuB,EAAMvB,OAASuB,EAAMoC,QAWjC,OATM3D,aAAiBiD,IAEtB1B,EAAMvB,MAAQiD,EAAWmB,IAAIpE,SAGVqE,IAAhB9C,EAAMe,QACTf,EAAMe,MAAQ,GAGRf,CACR,CCzBe,MAAM0B,EACpB,WAAAqB,CAAatF,GACZb,KAAKkC,GAAKrB,EAAQqB,GAClBlC,KAAKL,KAAOkB,EAAQlB,KACpBK,KAAKX,KAAOwB,EAAQxB,KAAOyF,EAAWmB,IAAIpF,EAAQxB,MAAQ,KAC1DW,KAAKoG,QAAUvF,EAAQuF,QAEnBpG,KAAKX,OACRW,KAAKqG,SAAWxF,EAAQwF,SACxBrG,KAAKsG,OAASzF,EAAQyF,QAKvB,IAAIvE,EAASlB,EAAQkB,QAAU/B,KAAKX,KAAK0C,OAEzC,IAAK,IAAIpC,KAAQoC,EACV,SAAUA,EAAOpC,KACtBoC,EAAOpC,GAAMA,KAAOA,GAGtBK,KAAK+B,OAASA,EAId,IAAIwE,EAAQ1F,EAAQ0F,OAASvG,KAAKX,KAAKkH,OAAS,MAChDvG,KAAKuG,MAAQ/F,EAAS+F,GAItBvG,KAAK6F,QAAUhF,EAAQgF,SAAW,CAAA,EAElC,IAAK,IAAIlG,KAAQK,KAAK6F,QAAS,CAC9B,IAAI/D,EAAS9B,KAAK6F,QAAQlG,GAC1BmC,EAAOnF,OAAS,WAChBmF,EAAOnC,OAASA,CAChB,CAEIK,KAAK6F,QAAQzC,OAAOlB,KACxBlC,KAAK6F,QAAQzC,MAAQ,IACjBpD,KAAK6F,QAAQzC,OAAS,CAAE,EAC3BlB,GAAIrB,EAAQ+E,OAAS5F,KAAKkC,KAMxBrB,EAAQ2F,WAEXxG,KAAKwG,WAAoC,SAAvB3F,EAAQ2F,WAAwBxG,KAAO8E,EAAWmB,IAAIpF,EAAQ2F,YAI5ExG,KAAKyG,QAERzG,KAAKwG,WAAaxG,KAAKX,KAGvBW,KAAKwG,WAAcxG,KAKjBA,KAAKwG,WAAWE,cACnB1G,KAAK2G,QAAU,CAAC5E,EAAQlB,KAChB,GAKTb,KAAK4G,SAAW/F,EAAQ+F,SAGxB/J,OAAOgK,eAAe7G,KAAM,OAAQ,CACnChB,MAAO8H,EAAQ9G,MAAM+G,UACrBC,UAAU,EACVC,YAAY,EACZC,cAAc,IAGf9G,EAAMH,IAAI,sBAAuBD,KACjC,CAED,OAAA2G,CAAS5E,GAAQoF,QAACA,EAxFT,OAwFwB,CAAA,GAChC,IAAKnH,KAAKoH,OAAOpH,KAAKwG,YAErB,OADAzE,EAAS/B,KAAKjB,GAAGiB,KAAKwG,WAAYzE,GAC3B/B,KAAKwG,WAAWG,QAAQ5E,EAAQ,CAACoF,YAGzC,IAAIhF,EAAYtF,OAAOwK,OAAOrH,KAAK+B,QAEnC,OAAOA,EAAOuF,OAAM,CAAC9K,EAAGL,KACvB,IAAI8G,EAAOd,EAAUhG,GAErB,GAAkB,UAAd8G,EAAKtG,MAAoBsG,EAAKL,MAAO,CACxC,GAAI5E,OAAOC,MAAMzB,GAEhB,OAAO,EAGR,IAAK+K,EAAKC,GAAOvE,EAAKL,MACtB,YAAgBsD,IAARqB,GAAqB/K,GAAK+K,EAAMJ,UACxBjB,IAARsB,GAAqBhL,GAAKgL,EAAML,EACxC,CAED,OAAO,CAAI,GAEZ,CAED,eAAIT,GACH,OAAO7J,OAAOwK,OAAOrH,KAAK+B,QAAQuF,OAAMG,KAAW,UAAWA,IAC9D,CAED,SAAI7B,GACH,OAAO5F,KAAK6F,SAASzC,OAAOlB,IAAMlC,KAAKkC,EACvC,CAED,WAAIuE,GACH,IAAK,IAAIvE,KAAMlC,KAAK+B,OACnB,GAA6B,UAAzB/B,KAAK+B,OAAOG,GAAIvF,KACnB,OAAO,EAIT,OAAO,CACP,CAED,SAAAsI,CAAWnD,GACV,GAAsB,iBAAXA,EAEV,OADAA,EAAS4F,EAAc5F,EAAQ9B,MAIhC,IAAIzD,EASJ,OANCA,EAFc,YAAXuF,EAEGjF,OAAOwK,OAAOrH,KAAK6F,SAAS,GAG5B7F,KAAK6F,QAAQ/D,GAGhBvF,GACHA,EAAMmL,EAAcnL,EAAKyD,MAClBzD,GAGD,IACP,CAQD,MAAA6K,CAAQvF,GACP,QAAKA,IAIE7B,OAAS6B,GAAS7B,KAAKkC,KAAOL,GAAS7B,KAAKkC,KAAOL,EAAMK,GAChE,CAED,EAAAnD,CAAI8C,EAAOE,GACV,GAAyB,IAArBjC,UAAUnE,OAAc,CAC3B,MAAMyH,EAAQ4C,EAASnE,IACtBA,EAAOE,GAAU,CAACqB,EAAMvB,MAAOuB,EAAMrB,OACtC,CAID,GAFAF,EAAQiD,EAAWmB,IAAIpE,GAEnB7B,KAAKoH,OAAOvF,GAEf,OAAOE,EAIRA,EAASA,EAAOjG,KAAIU,GAAKwB,OAAOC,MAAMzB,GAAK,EAAIA,IAG/C,IAGImL,EAAiBC,EAHjBC,EAAS7H,KAAK8H,KACdC,EAAYlG,EAAMiG,KAItB,IAAK,IAAI3L,EAAI,EAAGA,EAAI0L,EAAOlM,QACtBkM,EAAO1L,GAAGiL,OAAOW,EAAU5L,IADGA,IAEjCwL,EAAkBE,EAAO1L,GACzByL,EAAuBzL,EAOzB,IAAKwL,EAEJ,MAAM,IAAIK,MAAM,uCAAuChI,YAAY6B,oCAIpE,IAAK,IAAI1F,EAAI0L,EAAOlM,OAAS,EAAGQ,EAAIyL,EAAsBzL,IACzD4F,EAAS8F,EAAO1L,GAAGmK,OAAOvE,GAI3B,IAAK,IAAI5F,EAAIyL,EAAuB,EAAGzL,EAAI4L,EAAUpM,OAAQQ,IAC5D4F,EAASgG,EAAU5L,GAAGkK,SAAStE,GAGhC,OAAOA,CACP,CAED,IAAAjD,CAAM+C,EAAOE,GACZ,GAAyB,IAArBjC,UAAUnE,OAAc,CAC3B,MAAMyH,EAAQ4C,EAASnE,IACtBA,EAAOE,GAAU,CAACqB,EAAMvB,MAAOuB,EAAMrB,OACtC,CAID,OAFAF,EAAQiD,EAAWmB,IAAIpE,IAEV9C,GAAGiB,KAAM+B,EACtB,CAED,QAAAhF,GACC,MAAO,GAAGiD,KAAKL,SAASK,KAAKkC,KAC7B,CAED,YAAA+F,GACC,IAAI1L,EAAM,GAEV,IAAK,IAAI2F,KAAMlC,KAAK+B,OAAQ,CAC3B,IAAIkB,EAAOjD,KAAK+B,OAAOG,GACnBU,EAAQK,EAAKL,OAASK,EAAKH,SAC/BvG,EAAI6H,KAAKxB,GAAO2E,KAAO,EACvB,CAED,OAAOhL,CACP,CAED2L,gBAAkB,CAAA,EAGlB,cAAWnD,GACV,MAAO,IAAI,IAAIpD,IAAI9E,OAAOwK,OAAOvC,EAAWa,WAC5C,CAED,eAAOwC,CAAUjG,EAAIL,GAQpB,GAPyB,IAArB/B,UAAUnE,SAEbuG,GADAL,EAAQ/B,UAAU,IACPoC,IAGZL,EAAQ7B,KAAKiG,IAAIpE,GAEb7B,KAAK2F,SAASzD,IAAOlC,KAAK2F,SAASzD,KAAQL,EAC9C,MAAM,IAAImG,MAAM,wCAAwC9F,MAKzD,GAHAlC,KAAK2F,SAASzD,GAAML,EAGK,IAArB/B,UAAUnE,QAAgBkG,EAAMuE,QACnC,IAAK,IAAIgC,KAASvG,EAAMuE,QACvBpG,KAAKmI,SAASC,EAAOvG,GAIvB,OAAOA,CACP,CAMD,UAAOoE,CAAKpE,KAAUwG,GACrB,IAAKxG,GAASA,aAAiBiD,EAC9B,OAAOjD,EAKR,GAAgB,WAFFlF,EAAKkF,GAEO,CAEzB,IAAItF,EAAMuI,EAAWa,SAAS9D,EAAM3E,eAEpC,IAAKX,EACJ,MAAM,IAAIuE,UAAU,mCAAmCe,MAGxD,OAAOtF,CACP,CAED,GAAI8L,EAAa1M,OAChB,OAAOmJ,EAAWmB,OAAOoC,GAG1B,MAAM,IAAIvH,UAAU,GAAGe,+BACvB,CAUD,mBAAOyG,CAAcC,EAAKC,GACzB,IACI3G,EAAO4F,EADPgB,EAAY9L,EAAK4L,GA4BrB,GAzBkB,WAAdE,EACCF,EAAIrD,SAAS,MAEfrD,EAAO4F,GAASc,EAAIG,MAAM,MAI1B7G,EAAO4F,GAAS,CAAA,CAAGc,GAGb3M,MAAMC,QAAQ0M,IACrB1G,EAAO4F,GAASc,GAIjB1G,EAAQ0G,EAAI1G,MACZ4F,EAAQc,EAAII,SAGb9G,EAAQiD,EAAWmB,IAAIpE,GAElBA,IACJA,EAAQ2G,IAGJ3G,EACJ,MAAM,IAAIf,UAAU,uCAAuCyH,4EAK5D,GAFAE,EAAY9L,EAAK8K,GAEC,WAAdgB,GAAwC,WAAdA,GAA0BhB,GAAS,EAAG,CAEnE,IAAIxE,EAAOpG,OAAOoF,QAAQJ,EAAME,QAAQ0F,GAExC,GAAIxE,EACH,MAAO,CAACpB,QAAOK,GAAIe,EAAK,GAAI2F,MAAOnB,KAAUxE,EAAK,GAEnD,CAEDpB,EAAQiD,EAAWmB,IAAIpE,GAEvB,IAAIgH,EAAkBpB,EAAMvK,cAExBf,EAAI,EACR,IAAK,IAAI+F,KAAML,EAAME,OAAQ,CAC5B,IAAIkB,EAAOpB,EAAME,OAAOG,GAExB,GAAIA,EAAGhF,gBAAkB2L,GAAmB5F,EAAKtD,MAAMzC,gBAAkB2L,EACxE,MAAO,CAAChH,QAAOK,KAAI0G,MAAOzM,KAAM8G,GAGjC9G,GACA,CAED,MAAM,IAAI2E,UAAU,OAAO2G,0BAA8B5F,EAAMlC,8BAA8B9C,OAAOwI,KAAKxD,EAAME,QAAQ+G,KAAK,QAC5H,CAEDZ,sBAAwB,CACvBvL,KAAM,YACNgD,KAAM,SAIR,SAASmH,EAASjF,GACjB,IAAItF,EAAM,CAACsF,GAEX,IAAK,IAAIkH,EAAIlH,EAAOkH,EAAIA,EAAE1J,MACzB9C,EAAI6H,KAAK2E,GAGV,OAAOxM,CACR,CAEA,SAASmL,EAAe5F,GAAQC,OAACA,GAAU,CAAA,GAC1C,GAAID,EAAOC,SAAWD,EAAOM,aAAc,CAC1CN,EAAOnF,OAAS,WAChBmF,EAAOnC,OAAS,QAGhBmC,EAAOM,aAAiCN,EAAOC,ONlO3BjG,KAAIsG,GACjBA,EAAasG,MAAM,KAAK5M,KAAIa,IAElC,IAAIiG,GADJjG,EAAOA,EAAKwG,QACKlG,MAAM,6CAEvB,GAAI2F,EAAO,CACV,IAAIrG,EAAM,IAAI2G,OAAON,EAAM,IAE3B,OADArG,EAAIqG,MAAQ,EAAEA,EAAM,IAAKA,EAAM,IACxBrG,CACP,CAED,OAAOI,CAAI,MMyNZ,IAAIqM,EAAenM,OAAOoF,QAAQF,GAAQjG,KAAI,EAAEoG,EAAIC,GAAYhG,KAE/D,IAAI8M,EAAanH,EAAOM,aAAajG,GAAG,GAEpCwG,EAAYR,EAAUS,OAAST,EAAUW,SACzCD,EAAUoG,EAAWrG,MAAOsG,EAAS,GAWzC,MARkB,gBAAdD,GACHpG,EAAU,CAAC,EAAG,KACdqG,EAAS,KAEa,WAAdD,IACRC,EAAS,OAGF,CAACvG,YAAWE,UAASqG,SAAO,IAGrCpH,EAAOqH,gBAAkB,CAACpH,EAAQ1E,IAC1B0E,EAAOjG,KAAI,CAACU,EAAGL,KACrB,IAAIwG,UAACA,EAASE,QAAEA,EAAOqG,OAAEA,GAAUF,EAAa7M,GAQhD,OANIwG,GAAaE,IAChBrG,EAAIqC,EAAS8D,EAAWE,EAASrG,IAGlCA,EAAIW,EAAgBX,EAAG,CAACa,YAAWC,KAAM4L,GAEjC,GAGV,CAED,OAAOpH,CACR,CCrbA,IAAesH,EAAA,IAAItE,EAAW,CAC7B5C,GAAI,UACJvC,KAAM,UACNoC,OAAQ,CACPhG,EAAG,CAAC4D,KAAM,KACV0J,EAAG,CAAC1J,KAAM,KACV2J,EAAG,CAAC3J,KAAM,MAEX4G,MAAO,MACPV,QAAS,CACRzC,MAAO,CACNuB,IAAK,CAAC,UAAW,SAGnByB,QAAS,CAAC,SCPI,MAAMmD,UAAsBzE,EAU1C,WAAAqB,CAAatF,GACPA,EAAQkB,SACZlB,EAAQkB,OAAS,CAChByH,EAAG,CACF5G,MAAO,CAAC,EAAG,GACXjD,KAAM,OAEP8J,EAAG,CACF7G,MAAO,CAAC,EAAG,GACXjD,KAAM,SAEP+J,EAAG,CACF9G,MAAO,CAAC,EAAG,GACXjD,KAAM,UAKJkB,EAAQxB,OACZwB,EAAQxB,KAAOsK,GAGZ9I,EAAQ+I,SAAW/I,EAAQgJ,YAC9BhJ,EAAQyF,SAAWwD,IAClB,IAAIC,EAAMxO,EAAiBsF,EAAQ+I,QAASE,GAO5C,OALI9J,KAAKuG,QAAUvG,KAAKX,KAAKkH,QAE5BwD,EAAMtJ,EAAMT,KAAKuG,MAAOvG,KAAKX,KAAKkH,MAAOwD,IAGnCA,CAAG,EAGXlJ,EAAQwF,WAAa0D,IACpBA,EAAMtJ,EAAMT,KAAKX,KAAKkH,MAAOvG,KAAKuG,MAAOwD,GAClCxO,EAAiBsF,EAAQgJ,UAAWE,KAI7ClJ,EAAQ+F,WAAa,UAErBoD,MAAMnJ,EACN,ECrDa,SAASoJ,EAAQ7G,EAAOvB,GAGtC,OAFAuB,EAAQ4C,EAAS5C,IAEZvB,GAASuB,EAAMvB,MAAMuF,OAAOvF,GAEzBuB,EAAMrB,OAAOgC,SAGrBlC,EAAQiD,EAAWmB,IAAIpE,IACV/C,KAAKsE,EACnB,CCfe,SAAS6C,EAAK7C,EAAO8G,GACnC9G,EAAQ4C,EAAS5C,GAEjB,IAAIvB,MAACA,EAAK+G,MAAEA,GAAS9D,EAAWwD,aAAa4B,EAAM9G,EAAMvB,OAEzD,OADaoI,EAAO7G,EAAOvB,GACb+G,EACf,CCPe,SAASuB,EAAQ/G,EAAOvB,EAAOE,GAK7C,OAJAqB,EAAQ4C,EAAS5C,GAEjBvB,EAAQiD,EAAWmB,IAAIpE,GACvBuB,EAAMrB,OAASF,EAAM9C,GAAGqE,EAAMvB,MAAOE,GAC9BqB,CACR,CCDe,SAASgH,EAAKhH,EAAO8G,EAAMlL,GAGzC,GAFAoE,EAAQ4C,EAAS5C,GAEQ,IAArBtD,UAAUnE,QAAuC,WAAvBgB,EAAKmD,UAAU,IAAkB,CAE9D,IAAIuK,EAASvK,UAAU,GACvB,IAAK,IAAI9D,KAAKqO,EACbD,EAAIhH,EAAOpH,EAAGqO,EAAOrO,GAEtB,KACI,CACiB,mBAAVgD,IACVA,EAAQA,EAAMiH,EAAI7C,EAAO8G,KAG1B,IAAIrI,MAACA,EAAK+G,MAAEA,GAAS9D,EAAWwD,aAAa4B,EAAM9G,EAAMvB,OACrDE,EAASkI,EAAO7G,EAAOvB,GAC3BE,EAAO6G,GAAS5J,EAChBmL,EAAO/G,EAAOvB,EAAOE,EACrB,CAED,OAAOqB,CACR,CDnBA+G,EAAOG,QAAU,QCqBjBF,EAAIE,QAAU,QC5Bd,IAAeC,EAAA,IAAIzF,EAAW,CAC7B5C,GAAI,UACJvC,KAAM,UACN4G,MAAO,MACPlH,KAAMsK,EACNtD,SAAUtE,GAAUtB,EAAMkJ,EAAQpD,MAAO,MAAOxE,GAChDuE,OAAQvE,GAAUtB,EAAM,MAAOkJ,EAAQpD,MAAOxE,KCL/C,MACMyI,EAAK,GAAK,IACVC,EAAI,MAAQ,GAElB,IAAIlE,EAAQlG,EAAOC,IAEnB,IAAeoK,EAAA,IAAI5F,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdnD,KAAM,aAEPiL,EAAG,CACF9H,SAAU,EAAE,IAAK,MAElB4G,EAAG,CACF5G,SAAU,EAAE,IAAK,OAMpByD,MAACA,EAEAlH,KAAMwL,EAGN,QAAAxE,CAAUzF,GAET,IAGIkK,EAHMlK,EAAI9E,KAAI,CAACkD,EAAO7C,IAAM6C,EAAQuH,EAAMpK,KAGlCL,KAAIkD,GAASA,EAlCjB,oBAkC6BtB,KAAKqN,KAAK/L,IAAUyL,EAAIzL,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAM8L,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAID,MAAAxE,CAAQ0E,GAEP,IAAIF,EAAI,GAaR,OAZAA,EAAE,IAAME,EAAI,GAAK,IAAM,IACvBF,EAAE,GAAKE,EAAI,GAAK,IAAMF,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAKE,EAAI,GAAK,IAGb,CACTF,EAAE,GAAON,EAAK9M,KAAKuN,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,EACrEO,EAAI,GAAK,EAAKtN,KAAKuN,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKP,EAC1DK,EAAE,GAAON,EAAK9M,KAAKuN,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,GAI3D3O,KAAI,CAACkD,EAAO7C,IAAM6C,EAAQuH,EAAMpK,IAC3C,EAED0J,QAAS,CACR6E,IAAO,CACN3I,OAAQ,CAAC,0BAA2B,gCAAiC,qCCtEjE,SAASmJ,EAAWC,GAC1B,OAASA,EAAQ,IAAO,KAAO,GAChC,CCEA,IAAeC,EAAA,IAAItG,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdnD,KAAM,aAEPnD,EAAG,CACFsG,SAAU,CAAC,EAAG,KACdnD,KAAM,UAEP0L,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,QAIRN,KAAM2L,EACN,QAAA3E,CAAU2E,GAET,IACIM,GADCC,EAAGX,EAAGlB,GAAKsB,EAWhB,OANCM,EADG5N,KAAKE,IAAIgN,GAFH,KAEalN,KAAKE,IAAI8L,GAFtB,IAGHzF,IAGmB,IAAnBvG,KAAK8N,MAAM9B,EAAGkB,GAAWlN,KAAKc,GAG9B,CACN+M,EACA7N,KAAK+N,KAAKb,GAAK,EAAIlB,GAAK,GACxBgC,EAAeJ,GAEhB,EACD,MAAAhF,CAAQqF,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGN5N,MAAM6N,KACTA,EAAM,GAEA,CACNF,EACAC,EAASnO,KAAKqO,IAAID,EAAMpO,KAAKc,GAAK,KAClCqN,EAASnO,KAAKsO,IAAIF,EAAMpO,KAAKc,GAAK,KAEnC,EAEDqH,QAAS,CACRuF,IAAO,CACNrJ,OAAQ,CAAC,0BAA2B,0BAA2B,0BClDlE,MAAMkK,EAAU,IAAM,EAChBC,EAAIxO,KAAKc,GACT2N,EAAM,IAAMD,EACZE,EAAMF,EAAI,IAEhB,SAASG,EAAMtQ,GAGd,MAAMuQ,EAAKvQ,EAAIA,EAGf,OAFWuQ,EAAKA,EAAKA,EAAKvQ,CAG3B,CAEe,SAAQwQ,EAAEnJ,EAAOoJ,GAAQC,GAACA,EAAK,EAACC,GAAEA,EAAK,EAACC,GAAEA,EAAK,GAAK,KACjEvJ,EAAOoJ,GAAUxG,EAAS,CAAC5C,EAAOoJ,IAanC,IAAKI,EAAIC,EAAIC,GAAMpC,EAAI5L,KAAKsE,GACxB2J,EAAK3B,EAAItM,KAAK4L,EAAK,CAACkC,EAAIC,EAAIC,IAAK,IAChCE,EAAIC,EAAIC,GAAMxC,EAAI5L,KAAK0N,GACxBW,EAAK/B,EAAItM,KAAK4L,EAAK,CAACsC,EAAIC,EAAIC,IAAK,GAMjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAGN,IAIIC,EAAKf,GAJGU,EAAKI,GAAM,GAMnBE,EAAI,IAAO,EAAI3P,KAAK+N,KAAK2B,GAAMA,EAAKnB,KAIpCqB,GAAU,EAAID,GAAKR,EACnBU,GAAU,EAAIF,GAAKJ,EAGnBO,EAAS9P,KAAK+N,KAAK6B,GAAU,EAAIR,GAAM,GACvCW,EAAS/P,KAAK+N,KAAK8B,GAAU,EAAIL,GAAM,GAKvCQ,EAAiB,IAAXJ,GAAuB,IAAPR,EAAY,EAAIpP,KAAK8N,MAAMsB,EAAIQ,GACrDK,EAAiB,IAAXJ,GAAuB,IAAPL,EAAY,EAAIxP,KAAK8N,MAAM0B,EAAIK,GAErDG,EAAK,IACRA,GAAM,EAAIxB,GAEPyB,EAAK,IACRA,GAAM,EAAIzB,GAGXwB,GAAMvB,EACNwB,GAAMxB,EAGN,IAOI,EAPA,EAAKa,EAAKJ,EACV,EAAKa,EAASD,EAGdI,EAAQD,EAAKD,EACbG,EAAOH,EAAKC,EACZG,EAAOpQ,KAAKE,IAAIgQ,GAGhBJ,EAASC,GAAW,EACvB,EAAK,EAEGK,GAAQ,IAChB,EAAKF,EAEGA,EAAQ,IAChB,EAAKA,EAAQ,IAELA,GAAS,IACjB,EAAKA,EAAQ,IAGb5M,EAASO,KAAK,gCAIf,IAUIwM,EAVA,EAAK,EAAIrQ,KAAK+N,KAAKgC,EAASD,GAAU9P,KAAKsO,IAAI,EAAKI,EAAM,GAG1D4B,GAASpB,EAAKI,GAAM,EACpBiB,GAAST,EAASC,GAAU,EAC5BS,EAAS7B,EAAK4B,GAOjBF,EADGP,EAASC,GAAW,EACfI,EAEAC,GAAQ,IACRD,EAAO,EAEPA,EAAO,KACNA,EAAO,KAAO,GAGdA,EAAO,KAAO,EAQxB,IAAIM,GAAOH,EAAQ,KAAO,EACtBI,EAAK,EAAM,KAAQD,EAAOzQ,KAAK+N,KAAK,GAAK0C,GAGzCE,EAAK,EAAI,KAAQJ,EAGjBK,EAAI,EACRA,GAAM,IAAO5Q,KAAKqO,KAAUgC,EAAQ,IAAO3B,GAC3CkC,GAAM,IAAO5Q,KAAKqO,IAAM,EAAIgC,EAAe3B,GAC3CkC,GAAM,IAAO5Q,KAAKqO,KAAM,EAAIgC,EAAS,GAAM3B,GAC3CkC,GAAM,GAAO5Q,KAAKqO,KAAM,EAAIgC,EAAS,IAAM3B,GAI3C,IAAImC,EAAK,EAAI,KAAQN,EAAQK,EAMzB,EAAK,GAAK5Q,KAAK4B,KAAK,IAAOyO,EAAQ,KAAO,KAAO,GACjDS,EAAK,EAAI9Q,KAAK+N,KAAKyC,GAAUA,EAASjC,IAItCwC,GAAM,GAAMhC,EAAK2B,KAAQ,EAI7B,OAHAK,IAAO,GAAM/B,EAAK2B,KAAQ,EAC1BI,IAAO,GAAM9B,EAAK4B,KAAQ,EAC1BE,IANU,EAAI/Q,KAAKsO,IAAI,EAAI,EAAKI,GAAOoC,GAM3B,GAAM9B,EAAK2B,KAAQ,GAAM1B,EAAK4B,IACnC7Q,KAAK+N,KAAKgD,EAElB,CC5KA,MAAMC,EAAa,CAClB,CAAE,iBAAoB,mBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,oBAGtCC,EAAa,CAClB,CAAG,oBAAqB,kBAAqB,mBAC7C,EAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAExCC,EAAa,CAClB,CAAE,iBAAqB,mBAAqB,mBAC5C,CAAE,oBAAqB,iBAAqB,kBAC5C,CAAE,kBAAqB,mBAAqB,oBAGvCC,EAAa,CAClB,CAAE,EAAqB,kBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,qBAG7C,IAAeC,EAAA,IAAIhK,EAAW,CAC7B5C,GAAI,QACJvC,KAAM,QACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,GACdnD,KAAM,aAEPiL,EAAG,CACF9H,SAAU,EAAE,GAAK,KAElB4G,EAAG,CACF5G,SAAU,EAAE,GAAK,MAKnByD,MAAO,MACPlH,KAAMsK,EACN,QAAAtD,CAAUzF,GAET,IAGImO,EAHMxT,EAAiBmT,EAAY9N,GAGxB9E,KAAIkT,GAAOtR,KAAKqN,KAAKiE,KAEpC,OAAOzT,EAAiBqT,EAAYG,EAEpC,EACD,MAAAzI,CAAQwI,GAEP,IAGIG,EAHO1T,EAAiBsT,EAAYC,GAGzBhT,KAAIkT,GAAOA,GAAO,IAEjC,OAAOzT,EAAiBoT,EAAYM,EACpC,EAEDpJ,QAAS,CACRqJ,MAAS,CACRnN,OAAQ,CAAC,0BAA2B,gCAAiC,qCChEzD,SAAAoN,EAAU/L,EAAOoJ,IAC9BpJ,EAAOoJ,GAAUxG,EAAS,CAAC5C,EAAOoJ,IAKnC,IAAKI,EAAIC,EAAIC,GAAMoC,EAAMpQ,KAAKsE,IACzB4J,EAAIC,EAAIC,GAAMgC,EAAMpQ,KAAK0N,GAC1B,EAAKI,EAAKI,EACV,EAAKH,EAAKI,EACV,EAAKH,EAAKI,EACd,OAAOxP,KAAK+N,KAAK,GAAM,EAAI,GAAM,EAAI,GAAM,EAC5C,CCfA,MAAM2D,EAAI,MAMK,SAASzI,EAASvD,EAAOvB,GAAOsF,QAACA,EAAUiI,GAAK,IAC9DhM,EAAQ4C,EAAS5C,GAEZvB,IACJA,EAAQuB,EAAMvB,OAGfA,EAAQiD,EAAWmB,IAAIpE,GACvB,IAAIE,EAASqB,EAAMrB,OAMnB,OAJIF,IAAUuB,EAAMvB,QACnBE,EAASF,EAAM/C,KAAKsE,IAGdvB,EAAM8E,QAAQ5E,EAAQ,CAACoF,WAC/B,CCxBe,SAASkI,EAAOjM,GAC9B,MAAO,CACNvB,MAAOuB,EAAMvB,MACbE,OAAQqB,EAAMrB,OAAOgC,QACrBI,MAAOf,EAAMe,MAEf,CCDe,SAASmL,EAAUC,EAAQC,EAAQ3N,EAAQ,OAIzD,IAAI4N,GAHJ5N,EAAQiD,EAAWmB,IAAIpE,IAGH/C,KAAKyQ,GACrBG,EAAU7N,EAAM/C,KAAK0Q,GAEzB,OAAO9R,KAAK+N,KAAKgE,EAAQE,QAAO,CAACC,EAAKC,EAAI1T,KACzC,IAAI2T,EAAKJ,EAAQvT,GACjB,OAAI8B,MAAM4R,IAAO5R,MAAM6R,GACfF,EAGDA,GAAOE,EAAKD,IAAO,CAAC,GACzB,GACJ,CCjBe,SAASE,GAAU3M,EAAOoJ,GAExC,OAAO8C,EAASlM,EAAOoJ,EAAQ,MAChC,CCMA,MACMJ,GADI1O,KAAKc,GACC,IAED,SAAAwR,GAAU5M,EAAOoJ,GAAQ7B,EAACA,EAAI,EAACnO,EAAEA,EAAI,GAAK,KACvD4G,EAAOoJ,GAAUxG,EAAS,CAAC5C,EAAOoJ,IAUnC,IAAKI,EAAIC,EAAIC,GAAMpC,EAAI5L,KAAKsE,KACrB2J,EAAIkD,GAAM7E,EAAItM,KAAK4L,EAAK,CAACkC,EAAIC,EAAIC,KACnCE,EAAIC,EAAIC,GAAMxC,EAAI5L,KAAK0N,GACxBW,EAAK/B,EAAItM,KAAK4L,EAAK,CAACsC,EAAIC,EAAIC,IAAK,GAYjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAON,IAAI,EAAKP,EAAKI,EACV,EAAKD,EAAKI,EAOV+C,GALKrD,EAAKI,IAKE,GAJPH,EAAKI,IAIc,EAAM,GAAM,EAmBpCkB,EAAK,KACLxB,GAAM,KACTwB,EAAM,QAAWxB,GAAO,EAAI,OAAUA,IAIvC,IAGI0B,EAHAD,EAAO,MAAStB,GAAO,EAAI,MAASA,GAAO,KAI3C/O,OAAOC,MAAMgS,KAChBA,EAAK,GAIL3B,EADG2B,GAAM,KAAOA,GAAM,IAClB,IAAOvS,KAAKE,IAAI,GAAMF,KAAKqO,KAAKkE,EAAK,KAAO7D,KAG5C,IAAO1O,KAAKE,IAAI,GAAMF,KAAKqO,KAAKkE,EAAK,IAAM7D,KAKhD,IAAI+D,EAAKzS,KAAKuN,IAAI8B,EAAI,GAClBqD,EAAI1S,KAAK+N,KAAK0E,GAAMA,EAAK,OAIzB1B,GAAM,GAAM9D,EAAIyD,KAAQ,EAI5B,OAHAK,IAAO,GAAMjS,EAAI6R,KAAQ,EACzBI,GAAOyB,GALE7B,GAAO+B,EAAI9B,EAAK,EAAI8B,KAKV,EAEZ1S,KAAK+N,KAAKgD,EAElB,CC5GA,IAAe4B,GAAA,IAAIvL,EAAW,CAK7B5C,GAAI,cACJ0D,MAAO,gBACPjG,KAAM,mBACNoC,OAAQ,CACPhG,EAAG,CACF+G,SAAU,CAAC,EAAG,QACdnD,KAAM,MAEP0J,EAAG,CACFvG,SAAU,CAAC,EAAG,KACdnD,KAAM,MAEP2J,EAAG,CACFxG,SAAU,CAAC,EAAG,SACdnD,KAAM,OAIRN,KAAMsK,EACNtD,SAAUzF,GAIFA,EAAI9E,KAAKwU,GAAK5S,KAAK8J,IA9BjB,IA8BqB8I,EAAQ,KAEvChK,OAAQiK,GAEAA,EAAOzU,KAAIwU,GAAK5S,KAAK8J,IAAI8I,EAlCvB,IAkC+B,OCjC1C,MAAM5G,GAAI,KACJD,GAAI,IACJrM,GAAI,KAAI,MAERyS,GAAK,SACLC,GAAK,KAAI,IACTU,GAAK,QAELC,GAAO,IAAY,IAAM,MACzBjR,IAAK,IACLkR,GAAK,sBAELC,GAAc,CACnB,CAAG,UAAY,QAAW,SAC1B,EAAG,OAAY,SAAW,UAC1B,EAAG,SAAY,MAAW,WAGrBC,GAAc,CACnB,CAAG,oBAAsB,mBAAqB,kBAC9C,CAAG,mBAAsB,mBAAqB,oBAC9C,EAAG,oBAAsB,kBAAqB,qBAEzCC,GAAc,CACnB,CAAG,GAAW,GAAW,GACzB,CAAG,OAAW,SAAW,SACzB,CAAG,QAAW,UAAW,WAGpBC,GAAc,CACnB,CAAE,EAAqB,kBAAsB,oBAC7C,CAAE,mBAAqB,mBAAsB,oBAC7C,CAAE,mBAAqB,oBAAsB,oBAG9C,IAAeC,GAAA,IAAIjM,EAAW,CAC7B5C,GAAI,SACJvC,KAAM,SACNoC,OAAQ,CACPiP,GAAI,CACHlO,SAAU,CAAC,EAAG,GACdnD,KAAM,MAEPsR,GAAI,CACHnO,SAAU,EAAE,GAAK,KAElBoO,GAAI,CACHpO,SAAU,EAAE,GAAK,MAInBzD,KAAMgR,GACN,QAAAhK,CAAUzF,GAMT,IAAMuQ,EAAIC,EAAIC,GAAOzQ,EAUjB0Q,EAHM/V,EAAiBoV,GAAa,CAJ9BjH,GAAIyH,GAAQzH,GAAI,GAAK2H,EACrB5H,GAAI2H,GAAQ3H,GAAI,GAAK0H,EAGmBE,IAGlCvV,KAAK,SAAUkT,GAI9B,QAHUa,GAAMC,IAAOd,EAAM,MAAU5R,KAC3B,EAAKoT,IAAOxB,EAAM,MAAU5R,MA/DjC,kBAkEV,KAGQmU,EAAIN,EAAIC,GAAM3V,EAAiBsV,GAAaS,GAIlD,MAAO,EADI,EAAI9R,IAAK+R,GAAO,EAAK/R,GAAI+R,GAAOb,GAC/BO,EAAIC,EAChB,EACD,MAAA5K,CAAQyK,GACP,IAAKS,EAAIP,EAAIC,GAAMH,EAOf9B,EAHQ1T,EAAiBuV,GAAa,EAHhCU,EAAKd,KAAO,EAAIlR,GAAIA,IAAKgS,EAAKd,KAGQO,EAAIC,IAGpCpV,KAAI,SAAUkT,GAK7B,OAFQ,MAFGa,GAAMb,GAAOyB,KACXD,GAAMxB,GAAOyB,GAASX,MAzFzB,iBA6Fb,KAGQ2B,EAAIC,EAAIL,GAAO9V,EAAiBqV,GAAa3B,GAG/CkC,GAAMM,GAAO/H,GAAI,GAAK2H,GAAO3H,GAEjC,MAAO,CAAEyH,GADCO,GAAOjI,GAAI,GAAK0H,GAAO1H,GAChB4H,EACjB,EAEDxL,QAAS,CAERzC,MAAS,CACRrB,OAAQ,CAAC,0BAA2B,gCAAiC,qCC9GzD4P,GAAA,IAAI7M,EAAW,CAC7B5C,GAAI,SACJvC,KAAM,SACNoC,OAAQ,CACPiP,GAAI,CACHlO,SAAU,CAAC,EAAG,GACdnD,KAAM,MAEPiS,GAAI,CACH9O,SAAU,CAAC,EAAG,GACdnD,KAAM,UAEPkS,GAAI,CACH/O,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,QAIRN,KAAM0R,GACN,QAAA1K,CAAUyL,GAET,IACIxG,GADCkG,EAAIP,EAAIC,GAAMY,EAEnB,MAAM,EAAI,KASV,OANCxG,EADG5N,KAAKE,IAAIqT,GAAM,GAAKvT,KAAKE,IAAIsT,GAAM,EAChCjN,IAGqB,IAArBvG,KAAK8N,MAAM0F,EAAID,GAAYvT,KAAKc,GAGhC,CACNgT,EACA9T,KAAK+N,KAAKwF,GAAM,EAAIC,GAAM,GAC1BxF,EAAeJ,GAEhB,EACDhF,OAAQqL,GAGA,CACNA,EAAO,GACPA,EAAO,GAAKjU,KAAKqO,IAAI4F,EAAO,GAAKjU,KAAKc,GAAK,KAC3CmT,EAAO,GAAKjU,KAAKsO,IAAI2F,EAAO,GAAKjU,KAAKc,GAAK,QCvC/B,SAAAuT,GAAU3O,EAAOoJ,IAC9BpJ,EAAOoJ,GAAUxG,EAAS,CAAC5C,EAAOoJ,IAKnC,IAAKwF,EAAKC,EAAKC,GAAOP,GAAO7S,KAAKsE,IAC7B+O,EAAKC,EAAKC,GAAOV,GAAO7S,KAAK0N,GAI9B,EAAKwF,EAAMG,EACX,EAAKF,EAAMG,EAGVpU,OAAOC,MAAMiU,IAAUlU,OAAOC,MAAMoU,IAExCH,EAAM,EACNG,EAAM,GAEErU,OAAOC,MAAMiU,GAErBA,EAAMG,EAEErU,OAAOC,MAAMoU,KACrBA,EAAMH,GAGP,IAAI,EAAKA,EAAMG,EACX,EAAK,EAAI3U,KAAK+N,KAAKwG,EAAMG,GAAO1U,KAAKsO,IAAK,EAAK,GAAMtO,KAAKc,GAAK,MAEnE,OAAOd,KAAK+N,KAAK,GAAM,EAAI,GAAM,EAAI,GAAM,EAC5C,CCtCA,MAAMoE,GAAK,SACLC,GAAK,KAAO,IACZU,GAAK,QACL8B,GAAK,KAAO,MACZC,GAAK,KAAO,GACZC,GAAM,MAAQ,KACdC,GAAM,GAAK,KAIX/D,GAAa,CAClB,CAAG,kBAAqB,mBAAqB,kBAC7C,EAAG,kBAAqB,kBAAqB,mBAC7C,CAAG,kBAAqB,kBAAqB,oBAiBxCgE,GAAa,CAClB,CAAG,GAAe,GAAmB,GACrC,CAAG,KAAO,MAAO,MAAQ,KAAO,KAAO,MACvC,CAAE,MAAQ,MAAO,MAAQ,MAAQ,IAAM,OAIlCC,GAAa,CAClB,CAAE,kBAAqB,kBAAqB,kBAC5C,CAAE,mBAAqB,mBAAqB,mBAC5C,CAAE,kBAAqB,mBAAqB,oBASvChE,GAAa,CAClB,CAAG,oBAAqB,mBAAqB,mBAC7C,CAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAU9C,IAAeiE,GAAA,IAAI9N,EAAW,CAC7B5C,GAAI,QACJvC,KAAM,QAUNoC,OAAQ,CACP5F,EAAG,CACF2G,SAAU,CAAC,EAAG,GACdnD,KAAM,KAEPkT,GAAI,CACH/P,SAAU,EAAE,GAAK,IACjBnD,KAAM,MAEPmT,GAAI,CACHhQ,SAAU,EAAE,GAAK,IACjBnD,KAAM,OAIRN,KAAMgR,GACNhK,SAAUzF,GAaX,SAAqBqO,GAGpB,IAAIqC,EAAQrC,EAAInT,KAAK,SAAUkT,GAI9B,QAHUa,GAAMC,IAAOd,EAAM,MAAUsD,KAC3B,EAAK9B,IAAOxB,EAAM,MAAUsD,MAEfC,EAC3B,IAGC,OAAOhX,EAAiBmX,GAAYpB,EACrC,CArBSyB,CAFGxX,EAAiBmT,GAAY9N,IAIxC,MAAA0F,CAAQ0M,GACP,IAAI/D,EAoBN,SAAqB+D,GACpB,IAAI1B,EAAQ/V,EAAiBoX,GAAYK,GAGrC/D,EAAMqC,EAAMxV,KAAK,SAAUkT,GAG9B,OAAO,KAFItR,KAAK8J,IAAKwH,GAAOyD,GAAO5C,GAAI,IAC1BC,GAAMU,GAAMxB,GAAOyD,MACCD,EACnC,IAEC,OAAOvD,CACR,CA/BYgE,CAAWD,GAErB,OAAOzX,EAAiBoT,GAAYM,EACpC,ICjGa,SAAAiE,GAAU9P,EAAOoJ,IAC9BpJ,EAAOoJ,GAAUxG,EAAS,CAAC5C,EAAOoJ,IAOnC,IAAM2G,EAAIC,EAAIC,GAAOT,GAAM9T,KAAKsE,IAC1BkQ,EAAIC,EAAIC,GAAOZ,GAAM9T,KAAK0N,GAMhC,OAAO,IAAM9O,KAAK+N,MAAM0H,EAAKG,IAAO,EAAK,KAAQF,EAAKG,IAAO,GAAMF,EAAKG,IAAO,EAChF,CCjBA,MAAMjN,GAAQlG,EAAOE,IACfkT,GAAc,IACdC,GAAiB,EAAID,GACrBE,GAAM,EAAIjW,KAAKc,GAEfoV,GAAQ,CACb,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAGpBC,GAAW,CAChB,CAAC,oBAAqB,mBAAoB,oBAC1C,CAAC,mBAAqB,mBAAqB,qBAC3C,EAAE,qBAAuB,mBAAqB,qBAGzCvB,GAAK,CACV,CAAC,IAAO,IAAO,KACf,CAAC,KAAQ,KAAQ,KACjB,CAAC,KAAQ,KAAQ,OAGZwB,GAAc,CACnBC,KAAM,CAAC,GAAK,KAAO,IACnBC,IAAK,CAAC,GAAK,IAAM,IACjBC,QAAS,CAAC,EAAG,IAAM,IAGdC,GAAa,CAElB7I,EAAG,CAAC,MAAO,GAAO,OAAQ,OAAQ,QAClC8I,EAAG,CAAC,GAAK,GAAK,EAAK,IAAK,IACxBC,EAAG,CAAC,EAAK,IAAO,IAAO,IAAO,MAGzBC,GAAU,IAAM3W,KAAKc,GACrB8V,GAAU5W,KAAKc,GAAK,IAEnB,SAASiC,GAAOsB,EAAQwS,GAC9B,MAAMC,EAAOzS,EAAOjG,KAAIU,IACvB,MAAMT,EAAIqD,EAAKmV,EAAK7W,KAAKE,IAAIpB,GAAK,IAAMiX,IACxC,OAAO,IAAMvU,EAASnD,EAAGS,IAAMT,EAAI,MAAM,IAE1C,OAAOyY,CACR,CAsCO,SAASC,GACfC,EACAC,EACAC,EACAC,EACAC,GAGA,MAAM5U,EAAM,CAAA,EAEZA,EAAI4U,YAAcA,EAClB5U,EAAIwU,SAAWA,EACfxU,EAAI2U,SAAWA,EACf,MAAME,EAAOL,EAAS5Y,KAAIU,GACd,IAAJA,IAIR0D,EAAI8U,GAAKL,EAETzU,EAAI+U,GAAKL,EAET,MAAMM,EAAKH,EAAK,GAGVI,EAAO5Z,EAAiBqY,GAAOmB,GAI/BjK,GADN+J,EAAWf,GAAY5T,EAAI2U,WACR,GACnB3U,EAAI1D,EAAIqY,EAAS,GACjB3U,EAAIkV,GAAKP,EAAS,GAElB,MACMQ,GADI,GAAK,EAAInV,EAAI8U,GAAK,KACZ,EAGhB9U,EAAIqU,GAAMc,EAAKnV,EAAI8U,GAAK,IAAO,EAAIK,IAAO,EAAIA,GAAM3X,KAAKqN,KAAK,EAAI7K,EAAI8U,IACtE9U,EAAIoV,OAASpV,EAAIqU,IAAM,IAEvBrU,EAAI9C,EAAI8C,EAAI+U,GAAKC,EACjBhV,EAAIoJ,EAAI,KAAO5L,KAAK+N,KAAKvL,EAAI9C,GAC7B8C,EAAIqV,IAAM,KAASrV,EAAI9C,IAAM,GAC7B8C,EAAIsV,IAAMtV,EAAIqV,IAId,MAAM/V,EAAI,EACT,EACA9B,KAAK8J,IACJ9J,KAAK6J,IAAIuD,GAAK,EAAI,EAAI,IAAMpN,KAAK4B,MAAMY,EAAI8U,GAAK,IAAM,KAAM,GAC5D,GAEF9U,EAAIuV,KAAON,EAAKrZ,KAAIU,GACZkC,EAAY,EAAGwW,EAAK1Y,EAAGgD,KAE/BU,EAAIwV,QAAUxV,EAAIuV,KAAK3Z,KAAIU,GACnB,EAAIA,IAIZ,MAAMmZ,EAAQR,EAAKrZ,KAAI,CAACU,EAAGL,IACnBK,EAAI0D,EAAIuV,KAAKtZ,KAEfyZ,EAAQnV,GAAMkV,EAAOzV,EAAIqU,IAK/B,OAJArU,EAAI2V,GAAK3V,EAAIqV,KAAO,EAAIK,EAAM,GAAKA,EAAM,GAAK,IAAOA,EAAM,IAIpD1V,CACR,CAGA,MAAM4V,GAAoBrB,GACzBlO,GACA,GAAK7I,KAAKc,GAAK,GAAK,GACpB,WACA,GAGM,SAASuX,GAAWC,EAAO9V,GAIjC,UAAmBgG,IAAZ8P,EAAMC,OAAgC/P,IAAZ8P,EAAME,GACtC,MAAM,IAAIlO,MAAM,oDAGjB,UAAmB9B,IAAZ8P,EAAMG,OAAgCjQ,IAAZ8P,EAAMjV,OAAgCmF,IAAZ8P,EAAMjN,GAChE,MAAM,IAAIf,MAAM,yDAIjB,UAAmB9B,IAAZ8P,EAAM3K,OAAgCnF,IAAZ8P,EAAM5B,GACtC,MAAM,IAAIpM,MAAM,oDAIjB,GAAgB,IAAZgO,EAAMC,GAAyB,IAAZD,EAAME,EAC5B,MAAO,CAAC,EAAK,EAAK,GAInB,IAAIE,EAAO,EAEVA,OADelQ,IAAZ8P,EAAM3K,EACFH,EAAU8K,EAAM3K,GAAKiJ,GAtHvB,SAA2BF,GACjC,IAAIiC,GAAOjC,EAAI,IAAM,KAAO,IAC5B,MAAMjY,EAAIuB,KAAKI,MAAM,IAAOuY,GAC5BA,GAAU,IACV,MAAOC,EAAIC,GAAOrC,GAAW7I,EAAEtH,MAAM5H,EAAGA,EAAI,IACrCqa,EAAIC,GAAOvC,GAAWC,EAAEpQ,MAAM5H,EAAGA,EAAI,GAE5C,OAAO+O,GACLmL,GAAMI,EAAMH,EAAKE,EAAKD,GAAO,IAAMD,EAAKG,IACxCJ,GAAMI,EAAMD,GAAM,IAAMC,GAE3B,CA8GSC,CAAiBV,EAAM5B,GAAKE,GAGpC,MAAMqC,EAAOjZ,KAAKqO,IAAIqK,GAChBQ,EAAOlZ,KAAKsO,IAAIoK,GAGtB,IAAIS,EAAQ,OACI3Q,IAAZ8P,EAAMC,EACTY,EAA+B,GAAvBzX,EAAK4W,EAAMC,EAAG,SAEF/P,IAAZ8P,EAAME,IACdW,EAAQ,IAAO3W,EAAI1D,EAAIwZ,EAAME,IAAMhW,EAAI2V,GAAK,GAAK3V,EAAIoV,SAItD,IAAInR,EAAQ,OACI+B,IAAZ8P,EAAMG,EACThS,EAAQ6R,EAAMG,EAAIU,OAEE3Q,IAAZ8P,EAAMjV,EACdoD,EAAS6R,EAAMjV,EAAIb,EAAIoV,OAAUuB,OAEb3Q,IAAZ8P,EAAMjN,IACd5E,EAAQ,KAAU6R,EAAMjN,GAAK,GAAM7I,EAAI2V,GAAK,GAAK3V,EAAI1D,GAEtD,MAAMsa,EAAI1X,EACT+E,EAAQzG,KAAKuN,IAAI,KAAOvN,KAAKuN,IAAI,IAAM/K,EAAI9C,IAAK,KAChD,GAAK,GAIA2Z,EAAK,KAAQrZ,KAAKqO,IAAIqK,EAAO,GAAK,KAGlC5a,EAAI0E,EAAI2V,GAAKzW,EAAKyX,EAAO,EAAI3W,EAAI1D,EAAI0D,EAAIoJ,GAGzC0N,EAAK,IAAM,GAAK9W,EAAIkV,GAAKlV,EAAIsV,IAAMuB,EACnCE,EAAKzb,EAAI0E,EAAIqV,IACb/L,EACL,IAAMyN,EAAK,MACX1X,EAAKuX,EAAG,GAAKE,EAAKF,GAAK,GAAKH,EAAO,IAAMC,IAMpCM,EAhMA,SAAkBC,EAAS5C,GACjC,MAAM6C,EAAW,IAAM7C,EAAM,OAASb,GACtC,OAAOyD,EAAQrb,KAAIU,IAClB,MAAM6a,EAAO3Z,KAAKE,IAAIpB,GACtB,OAAO0C,EAASkY,EAAWhY,EAAKiY,GAAQ,IAAMA,GAAO3D,IAAiBlX,EAAE,GAE1E,CA0Le8a,CACb/b,EAAiB+W,GAAI,CAAC2E,EALbzN,EAAImN,EACJnN,EAAIoN,IAIoB9a,KAAIU,GACzB,EAAJA,EAAQ,OAEhB0D,EAAIqU,IAEL,OAAOhZ,EACNsY,GACAqD,EAAMpb,KAAI,CAACU,EAAGL,IACNK,EAAI0D,EAAIwV,QAAQvZ,MAEvBL,KAAIU,GACEA,EAAI,KAEb,CAGO,SAAS+a,GAASC,EAAQtX,GAEhC,MAAMuX,EAASD,EAAO1b,KAAIU,GACd,IAAJA,IAEFkb,EAAOjX,GACZlF,EAAiBqY,GAAO6D,GAAQ3b,KAAI,CAACU,EAAGL,IAChCK,EAAI0D,EAAIuV,KAAKtZ,KAErB+D,EAAIqU,IAIC3J,EAAI8M,EAAK,KAAO,GAAKA,EAAK,GAAKA,EAAK,IAAM,GAC1ChO,GAAKgO,EAAK,GAAKA,EAAK,GAAK,EAAIA,EAAK,IAAM,EACxCtB,GAAS1Y,KAAK8N,MAAM9B,EAAGkB,GAAK+I,GAAOA,IAAOA,GAG1CoD,EAAK,KAAQrZ,KAAKqO,IAAIqK,EAAO,GAAK,KASlCjS,EAAQ/E,EANb,IAAM,GAAKc,EAAIkV,GAAKlV,EAAIsV,IACxBjW,EACCwX,EAAKrZ,KAAK+N,KAAKb,GAAK,EAAIlB,GAAK,GAC7BgO,EAAK,GAAKA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAGjB,IAAOha,KAAKuN,IAAI,KAAOvN,KAAKuN,IAAI,IAAM/K,EAAI9C,GAAI,KAK9DyZ,EAAQzX,EAFJc,EAAIqV,KAAO,EAAImC,EAAK,GAAKA,EAAK,GAAK,IAAOA,EAAK,IAElCxX,EAAI2V,GAAI,GAAM3V,EAAI1D,EAAI0D,EAAIoJ,GAG3C2M,EAAI,IAAM7W,EAAKyX,EAAO,GAGtBX,EAAK,EAAIhW,EAAI1D,EAAIqa,GAAS3W,EAAI2V,GAAK,GAAK3V,EAAIoV,OAG5Ca,EAAIhS,EAAQ0S,EAGZ9V,EAAIoV,EAAIjW,EAAIoV,OAGZjK,EAAIH,EAAUkL,EAAO/B,IAGrBD,EA3PA,SAAwB/I,GAC9B,IAAIsM,EAAKzM,EAAUG,GACfsM,GAAMzD,GAAW7I,EAAE,KACtBsM,GAAM,KAGP,MAAMxb,E/B+KA,SAAqB4J,EAAK/G,EAAO4Y,EAAK,EAAGtB,EAAKvQ,EAAIpK,QACxD,KAAOic,EAAKtB,GAAI,CACf,MAAMuB,EAAOD,EAAKtB,GAAO,EACrBvQ,EAAI8R,GAAO7Y,EACd4Y,EAAKC,EAAM,EAGXvB,EAAKuB,CAEN,CACD,OAAOD,CACR,C+B1LWE,CAAW5D,GAAW7I,EAAGsM,GAAM,GAClCrB,EAAIC,GAAOrC,GAAW7I,EAAEtH,MAAM5H,EAAGA,EAAI,IACrCqa,EAAIC,GAAOvC,GAAWC,EAAEpQ,MAAM5H,EAAGA,EAAI,GAGtC2a,GAAKa,EAAKrB,GAAME,EACtB,OAHWtC,GAAWE,EAAEjY,GAGX,IAAM2a,GAAMA,GAAKP,EAAMoB,GAAMlB,EAC3C,CA8OWsB,CAAc1M,GAOxB,MAAO,CAAC4K,EAAGA,EAAGE,EAAGA,EAAG9K,EAAGA,EAAGtC,EAJhB,GAAK3J,EAAKc,EAAI1D,EAAI2H,GAASjE,EAAI2V,GAAK,GAAI,IAIlBK,EAAGA,EAAGnV,EAAGA,EAAGqT,EAAGA,EAChD,CASA,IAAe4B,GAAA,IAAIlR,EAAW,CAC7B5C,GAAI,YACJ0D,MAAO,cACPjG,KAAM,YACNoC,OAAQ,CACPiW,EAAG,CACFlV,SAAU,CAAC,EAAG,KACdnD,KAAM,KAEPjE,EAAG,CACFoH,SAAU,CAAC,EAAG,KACdnD,KAAM,gBAEP0L,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,QAIRN,KAAM+J,EAEN,QAAA/C,CAAU0D,GACT,MAAMiM,EAAQuB,GAAQxN,EAAK+L,IAC3B,MAAO,CAACE,EAAMC,EAAGD,EAAMjV,EAAGiV,EAAM3K,EAChC,EACD/E,OAAQ0P,GACAD,GACN,CAACE,EAAGD,EAAM,GAAIjV,EAAGiV,EAAM,GAAI3K,EAAG2K,EAAM,IACpCF,MChWH,MAAMvP,GAAQlG,EAAOE,IACf6O,GAAI,IAAM,MACV3E,GAAI,MAAQ,GASlB,SAASwN,GAAWC,GAGnB,OAAQA,EAAQ,EAAMxa,KAAKuN,KAAKiN,EAAQ,IAAM,IAAK,GAAKA,EAAQzN,EACjE,CA0EA,SAAS0N,GAAOpO,EAAK7J,GAGpB,MAAM4W,EApFE,MAJSzN,EAwFCU,EAAI,IArFNqF,GAAK1R,KAAKqN,KAAK1B,IAAMoB,GAAIpB,EAAI,IAAM,KAC7B,GAJvB,IAAkBA,EAyFjB,GAAU,IAANyN,EACH,MAAO,CAAC,EAAK,EAAK,GAEnB,MAAMd,EAAQuB,GAAQxN,EAAK+L,IAC3B,MAAO,CAAC5K,EAAU8K,EAAM3K,GAAI2K,EAAMG,EAAGW,EACtC,CAGO,MAAMhB,GAAoBrB,GAChClO,GAAO,IAAM7I,KAAKc,GAAKyZ,GAAU,IACf,IAAlBA,GAAU,IACV,WACA,GAYD,IAAeG,GAAA,IAAItT,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACPsJ,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,OAEPnD,EAAG,CACFsG,SAAU,CAAC,EAAG,KACdnD,KAAM,gBAEPmX,EAAG,CACFhU,SAAU,CAAC,EAAG,KACdnD,KAAM,SAIRN,KAAM+J,EAEN/C,SAAU0D,GACFoO,GAAMpO,GAEdzD,OAAQ8R,GA5HT,SAAkBrW,EAAQ7B,GASzB,IAAKmL,EAAG7O,EAAGsa,GAAK/U,EACZgI,EAAM,GACNiO,EAAI,EAGR,GAAU,IAANlB,EACH,MAAO,CAAC,EAAK,EAAK,GAInB,IAAIzN,EAAI4O,GAAUnB,GAKjBkB,EADGlB,EAAI,EACH,mBAAsBA,GAAK,EAAI,iBAAoBA,EAAI,kBAGvD,qBAAwBA,GAAK,EAAI,mBAAsBA,EAAI,mBAWhE,IAAIuB,EAAU,EACVC,EAAOC,IAIX,KAAOF,GAPc,IAOW,CAC/BtO,EAAMgM,GAAU,CAACE,EAAG+B,EAAG7B,EAAG3Z,EAAG6O,EAAGA,GAAInL,GAIpC,MAAMsY,EAAQ9a,KAAKE,IAAImM,EAAI,GAAKV,GAChC,GAAImP,EAAQF,EAAM,CACjB,GAAIE,GAfY,MAgBf,OAAOzO,EAGRuO,EAAOE,CACP,CAODR,IAASjO,EAAI,GAAKV,GAAK2O,GAAK,EAAIjO,EAAI,IAEpCsO,GAAW,CACX,CAID,OAAOtC,GAAU,CAACE,EAAG+B,EAAG7B,EAAG3Z,EAAG6O,EAAGA,GAAInL,EACtC,CAuDSuY,CAAQL,EAAKtC,IAErBjQ,QAAS,CACRzC,MAAO,CACNlB,GAAI,QACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BCpJ7D,MAAMuS,GAAU5W,KAAKc,GAAK,IACpBka,GAAW,CAAC,EAAM,KAAO,OAO/B,SAASC,GAAc5W,GAMlBA,EAAO,GAAK,IACfA,EAASqW,GAAI/R,SAAS+R,GAAI9R,OAAOvE,KAMlC,MAAMhB,EAAIrD,KAAKkb,IAAIlb,KAAK8J,IAAI,EAAIkR,GAAS,GAAK3W,EAAO,GAAK+T,GAAkBR,OAAQ,IAAQoD,GAAS,GAC/FG,EAAO9W,EAAO,GAAKuS,GACnB1J,EAAI7J,EAAIrD,KAAKqO,IAAI8M,GACjBnP,EAAI3I,EAAIrD,KAAKsO,IAAI6M,GAEvB,MAAO,CAAC9W,EAAO,GAAI6I,EAAGlB,EACvB,CASe,SAAAoP,GAAU1V,EAAOoJ,IAC9BpJ,EAAOoJ,GAAUxG,EAAS,CAAC5C,EAAOoJ,IAEnC,IAAMuM,EAAIlM,EAAIC,GAAO6L,GAAaP,GAAItZ,KAAKsE,KACrC4V,EAAI/L,EAAIC,GAAOyL,GAAaP,GAAItZ,KAAK0N,IAI3C,OAAO9O,KAAK+N,MAAMsN,EAAKC,IAAO,GAAKnM,EAAKI,IAAO,GAAKH,EAAKI,IAAO,EACjE,CChCA,IAAe+L,GAAA,CACdlJ,YACAC,aACAzD,aACAwF,YACAmB,aACA/D,WACA2J,cCGD,MAAMI,GAAa,CAClBd,IAAO,CACNe,OAAQ,QACRC,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAE,GAEpB,YAAa,CACZH,OAAQ,QACRC,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAEC,QAAS,QAAShS,IAAK,EAAGC,IAAK,OAwBrC,SAASgS,GACvBpW,GACA+V,OACCA,EAASnY,EAASC,cAAaY,MAC/BA,EAAiBwX,aACjBA,EAAe,GAAED,IACjBA,EAAM,EAACE,gBACPA,EAAkB,CAAE,GACjB,CAAE,GAkBN,GAhBAlW,EAAQ4C,EAAS5C,GAEbqW,EAAc3Z,UAAU,IAC3B+B,EAAQ/B,UAAU,GAET+B,IACTA,EAAQuB,EAAMvB,OAUX8E,EAAQvD,EAPZvB,EAAQiD,EAAWmB,IAAIpE,GAOG,CAAEsF,QAAS,IACpC,OAAO/D,EAGR,IAAIsW,EACJ,GAAe,QAAXP,EACHO,EAAaC,GAAWvW,EAAO,CAAEvB,cAE7B,CACJ,GAAe,SAAXsX,GAAsBxS,EAAQvD,EAAOvB,GA2ExC6X,EAAa3a,GAAGqE,EAAOvB,OA3EyB,CAE5ChF,OAAOC,UAAU8c,eAAe5c,KAAKkc,GAAYC,MAClDA,SAAQC,MAAKC,eAAcC,mBAAmBJ,GAAWC,IAI5D,IAAIU,EAAKtN,EACT,GAAqB,KAAjB8M,EACH,IAAK,IAAI3d,KAAKud,GACb,GAAI,SAAWI,EAAanc,gBAAkBxB,EAAEwB,cAAe,CAC9D2c,EAAKZ,GAAcvd,GACnB,KACA,CAIH,IAAIoe,EAAUN,GAAQza,GAAGqE,EAAOvB,GAAQ,CAAEsX,OAAQ,OAAQtX,UAC1D,GAAIgY,EAAGzW,EAAO0W,GAAWV,EAAK,CAG7B,GAA4C,IAAxCvc,OAAOwI,KAAKiU,GAAiB3d,OAAc,CAC9C,IAAIoe,EAAcjV,EAAWwD,aAAagR,EAAgBC,SACtDA,EAAUtT,EAAIlH,GAAGqE,EAAO2W,EAAYlY,OAAQkY,EAAY7X,IAI5D,GAHI8X,EAAYT,KACfA,EAAU,GAEPA,GAAWD,EAAgB9R,IAC9B,OAAOzI,GAAG,CAAE8C,MAAO,UAAWE,OAAQ1B,EAAY,KAAK+C,EAAMvB,OAEzD,GAAI0X,GAAWD,EAAgB/R,IACnC,OAAOxI,GAAG,CAAE8C,MAAO,UAAWE,OAAQ,CAAC,EAAG,EAAG,IAAMqB,EAAMvB,MAE1D,CAGD,IAAIM,EAAY2C,EAAWwD,aAAa6Q,GACpCc,EAAW9X,EAAUN,MACrB8G,EAAUxG,EAAUD,GAEpBgY,EAAcnb,GAAGqE,EAAO6W,GAE5BC,EAAYnY,OAAOhC,SAAQ,CAACvD,EAAGL,KAC1B6d,EAAYxd,KACf0d,EAAYnY,OAAO5F,GAAK,EACxB,IAEF,IACIoL,GADSpF,EAAUS,OAAST,EAAUW,UACzB,GACb,EA/HR,SAAsBsW,GAGrB,MAAMe,EAAUf,EAAW1b,KAAKI,MAAMJ,KAAKC,MAAMD,KAAKE,IAAIwb,KAAnC,EAEvB,OAAO1b,KAAK8J,IAAI4S,WAAW,MAAKD,EAAQ,IAAM,KAC/C,CAyHYE,CAAYjB,GAChBkB,EAAM/S,EACNgT,EAAOtU,EAAIiU,EAAavR,GAE5B,KAAO4R,EAAOD,EAAM,GAAG,CACtB,IAAIR,EAAUzK,EAAM6K,GACpBJ,EAAUN,GAAQM,EAAS,CAAEjY,QAAOsX,OAAQ,SAC/BU,EAAGK,EAAaJ,GAEhBV,EAAM,EAClBkB,EAAMrU,EAAIiU,EAAavR,GAGvB4R,EAAOtU,EAAIiU,EAAavR,GAGzByB,EAAI8P,EAAavR,GAAU2R,EAAMC,GAAQ,EACzC,CAEDb,EAAa3a,GAAGmb,EAAarY,EAC7B,MAEA6X,EAAaI,CAEd,CAKD,GAAe,SAAXX,IAECxS,EAAQ+S,EAAY7X,EAAO,CAAEsF,QAAS,IACzC,CACD,IAAIqT,EAAS3d,OAAOwK,OAAOxF,EAAME,QAAQjG,KAAIU,GAAKA,EAAEoG,OAAS,KAE7D8W,EAAW3X,OAAS2X,EAAW3X,OAAOjG,KAAI,CAACU,EAAGL,KAC7C,IAAKoL,EAAKC,GAAOgT,EAAOre,GAUxB,YARY+J,IAARqB,IACH/K,EAAIkB,KAAK8J,IAAID,EAAK/K,SAGP0J,IAARsB,IACHhL,EAAIkB,KAAK6J,IAAI/K,EAAGgL,IAGVhL,CAAC,GAET,CACD,CAOD,OALIqF,IAAUuB,EAAMvB,QACnB6X,EAAa3a,GAAG2a,EAAYtW,EAAMvB,QAGnCuB,EAAMrB,OAAS2X,EAAW3X,OACnBqB,CACR,CAEAoW,GAAQlP,QAAU,QAKlB,MAAMmQ,GAAS,CACdC,MAAO,CAAE7Y,MAAOqN,EAAOnN,OAAQ,CAAC,EAAG,EAAG,IACtC4Y,MAAO,CAAE9Y,MAAOqN,EAAOnN,OAAQ,CAAC,EAAG,EAAG,KAahC,SAAS4X,GAAYiB,GAAQ/Y,MAACA,GAAS,CAAA,GAC7C,MAAMgZ,EAAM,IACN,EAAI,KAEVD,EAAS5U,EAAS4U,GAEb/Y,IACJA,EAAQ+Y,EAAO/Y,OAGhBA,EAAQiD,EAAWmB,IAAIpE,GACvB,MAAMiZ,EAAahW,EAAWmB,IAAI,SAElC,GAAIpE,EAAM6E,YACT,OAAO3H,GAAG6b,EAAQ/Y,GAGnB,MAAMkZ,EAAehc,GAAG6b,EAAQE,GAChC,IAAIvP,EAAIwP,EAAahZ,OAAO,GAG5B,GAAIwJ,GAAK,EAAG,CACX,MAAMhF,EAAQxH,GAAG0b,GAAOC,MAAO7Y,GAE/B,OADA0E,EAAMpC,MAAQyW,EAAOzW,MACdpF,GAAGwH,EAAO1E,EACjB,CACD,GAAI0J,GAAK,EAAG,CACX,MAAMyP,EAAQjc,GAAG0b,GAAOE,MAAO9Y,GAE/B,OADAmZ,EAAM7W,MAAQyW,EAAOzW,MACdpF,GAAGic,EAAOnZ,EACjB,CAED,GAAI8E,EAAQoU,EAAclZ,EAAO,CAACsF,QAAS,IAC1C,OAAOpI,GAAGgc,EAAclZ,GAGzB,SAASoZ,EAAMC,GACd,MAAMC,EAAYpc,GAAGmc,EAAQrZ,GACvBuZ,EAAcve,OAAOwK,OAAOxF,EAAME,QAQxC,OAPAoZ,EAAUpZ,OAASoZ,EAAUpZ,OAAOjG,KAAI,CAAC2L,EAAOmB,KAC/C,GAAI,UAAWwS,EAAYxS,GAAQ,CAClC,MAAOrB,EAAKC,GAAQ4T,EAAYxS,GAAOhG,MACvC,OnCrEG,SAAgB2E,EAAKyH,EAAKxH,GAChC,OAAO9J,KAAK8J,IAAI9J,KAAK6J,IAAIC,EAAKwH,GAAMzH,EACrC,CmCmEW8T,CAAW9T,EAAKE,EAAOD,EAC9B,CACD,OAAOC,CAAK,IAEN0T,CACP,CACD,IAAI5T,EAAM,EACNC,EAAMuT,EAAahZ,OAAO,GAC1BuZ,GAAc,EACdC,EAAUlM,EAAM0L,GAChBjB,EAAUmB,EAAKM,GAEfC,EAAIrM,EAAS2K,EAASyB,GAC1B,GAAIC,EAAIX,EACP,OAAOf,EAGR,KAAQtS,EAAMD,EAAO,GAAG,CACvB,MAAMkU,GAAUlU,EAAMC,GAAO,EAE7B,GADA+T,EAAQxZ,OAAO,GAAK0Z,EAChBH,GAAe3U,EAAQ4U,EAAS1Z,EAAO,CAACsF,QAAS,IACpDI,EAAMkU,OAKN,GAFA3B,EAAUmB,EAAKM,GACfC,EAAIrM,EAAS2K,EAASyB,GAClBC,EAAIX,EAAK,CACZ,GAAKA,EAAMW,EAAI,EACd,MAGAF,GAAc,EACd/T,EAAMkU,CAEP,MAEAjU,EAAMiU,CAGR,CACD,OAAO3B,CACR,CC1Se,SAAS/a,GAAIqE,EAAOvB,GAAO8E,QAACA,GAAW,CAAA,GACrDvD,EAAQ4C,EAAS5C,GAGjB,IAAIrB,GAFJF,EAAQiD,EAAWmB,IAAIpE,IAEJ/C,KAAKsE,GACpB7G,EAAM,CAACsF,QAAOE,SAAQoC,MAAOf,EAAMe,OAMvC,OAJIwC,IACHpK,EAAMid,GAAQjd,GAAiB,IAAZoK,OAAmBT,EAAYS,IAG5CpK,CACR,CCTe,SAASmf,GAAWtY,GAAO/F,UACzCA,EAAY2D,EAAS3D,UAASyE,OAC9BA,EAAS,UACT6E,QAAAA,GAAU,KACPgV,GACA,IACH,IAAIpf,EAIAgJ,EAAWzD,EACfA,GAHAsB,EAAQ4C,EAAS5C,IAGFvB,MAAMoD,UAAUnD,IACrBsB,EAAMvB,MAAMoD,UAAU,YACtBH,EAAW8W,eAMrB,IAAI7Z,EAASqB,EAAMrB,OAAOgC,QAS1B,GAPA4C,IAAY7E,EAAO0X,QAEf7S,IAAYkV,EAAazY,KAE5BrB,EAASyX,GAAQnK,EAAMjM,IAAoB,IAAZuD,OAAmBT,EAAYS,GAAS5E,QAGpD,WAAhBD,EAAOnF,KAAmB,CAG7B,GAFAgf,EAActe,UAAYA,GAEtByE,EAAO4Z,UAIV,MAAM,IAAI5a,UAAU,UAAUyE,6DAH9BhJ,EAAMuF,EAAO4Z,UAAU3Z,EAAQqB,EAAMe,MAAOwX,EAK7C,KACI,CAEJ,IAAIhc,EAAOmC,EAAOnC,MAAQ,QAEtBmC,EAAOqH,gBACVpH,EAASD,EAAOqH,gBAAgBpH,EAAQ1E,GAGtB,OAAdA,IACH0E,EAASA,EAAOjG,KAAIU,GACZsf,EAAqBtf,EAAG,CAACa,iBAKnC,IAAIqG,EAAO,IAAI3B,GAEf,GAAa,UAATpC,EAAkB,CAErB,IAAIiG,EAAQ9D,EAAOI,IAAMJ,EAAO6C,MAAM,IAAMvB,EAAMvB,MAAMK,GACxDwB,EAAKqY,QAAQnW,EACb,CAED,IAAIzB,EAAQf,EAAMe,MACA,OAAd9G,IACH8G,EAAQ2X,EAAqB3X,EAAO,CAAC9G,eAGtC,IAAI2e,EAAW5Y,EAAMe,OAAS,GAAKrC,EAAOma,QAAU,GAAK,GAAGna,EAAOoa,OAAS,IAAM,QAAQ/X,IAC1F5H,EAAM,GAAGoD,KAAQ+D,EAAKoF,KAAKhH,EAAOoa,OAAS,KAAO,OAAOF,IACzD,CAED,OAAOzf,CACR,CD5DAwC,GAAGuL,QAAU,QENb,IAAe6R,GAAA,IAAI5S,EAAc,CAChCrH,GAAI,iBACJ0D,MAAO,mBACPjG,KAAM,kBACN4G,MAAO,MACRqD,QAlBgB,CACf,CAAE,kBAAoB,mBAAsB,mBAC5C,CAAE,kBAAoB,kBAAsB,oBAC5C,CAAE,EAAoB,oBAAsB,oBAgB7CC,UAZkB,CACjB,CAAG,mBAAqB,kBAAoB,iBAC5C,EAAG,iBAAqB,kBAAoB,mBAC5C,CAAG,kBAAqB,iBAAoB,qBCZ7C,MAAM,GAAI,iBACJ,GAAI,iBAEV,IAAeuS,GAAA,IAAI7S,EAAc,CAChCrH,GAAI,UACJvC,KAAM,WACNN,KAAM8c,GAEN7V,OAAQ+V,GACAA,EAAIvgB,KAAI,SAAUkT,GACxB,OAAIA,EAAU,IAAJ,GACFA,EAAM,IAGPtR,KAAKuN,KAAK+D,EAAM,GAAI,GAAK,GAAG,EAAI,IAC1C,IAEC3I,SAAUgW,GACFA,EAAIvgB,KAAI,SAAUkT,GACxB,OAAIA,GAAO,GACH,GAAItR,KAAKuN,IAAI+D,EAAK,MAAS,GAAI,GAGhC,IAAMA,CAChB,MCdA,IAAesN,GAAA,IAAI/S,EAAc,CAChCrH,GAAI,YACJ0D,MAAO,sBACPjG,KAAM,YACN4G,MAAO,MACRqD,QAjBgB,CACf,CAAC,kBAAoB,mBAAqB,mBAC1C,CAAC,kBAAoB,kBAAqB,kBAC1C,CAAC,EAAoB,mBAAqB,oBAe3CC,UAZkB,CACjB,CAAE,mBAAsB,mBAAqB,oBAC7C,EAAE,kBAAsB,mBAAqB,qBAC7C,CAAE,oBAAsB,mBAAqB,sBCF9C,MAQaA,GAAY,CACxB,CAAG,oBAAsB,mBAAsB,mBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,oBAAsB,mBAAsB,qBAGhD,IAAe0S,GAAA,IAAIhT,EAAc,CAChCrH,GAAI,cACJvC,KAAM,cACN4G,MAAO,MACRqD,QAlBgB,CACf,CAAE,mBAAqB,iBAAqB,mBAC5C,CAAE,mBAAqB,iBAAqB,oBAC5C,CAAE,mBAAqB,mBAAqB,oBAgB7CC,UAACA,KCpBc2S,GAAA,CACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,MAAS,CAAC,IAAM,IAAK,EAAG,GACxBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,OAAU,CAAC,EAAG,IAAM,IAAK,IAAM,KAC/B/B,MAAS,CAAC,EAAG,EAAG,GAChBgC,eAAkB,CAAC,EAAG,IAAM,IAAK,IAAM,KACvCC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC1CC,MAAS,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACpCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,WAAc,CAAC,IAAM,IAAK,EAAG,GAC7BC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,MAAS,CAAC,EAAG,IAAM,IAAK,GAAK,KAC7BC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,QAAW,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACtCC,KAAQ,CAAC,EAAG,EAAG,GACfC,SAAY,CAAC,EAAG,EAAG,IAAM,KACzBC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC7CC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,EAAG,IAAM,IAAK,GAC5BC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,YAAe,CAAC,IAAM,IAAK,EAAG,IAAM,KACpCC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC7CC,WAAc,CAAC,EAAG,IAAM,IAAK,GAC7BC,WAAc,CAAC,GAAW,GAAK,IAAK,IACpCC,QAAW,CAAC,IAAM,IAAK,EAAG,GAC1BC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC5CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,SAAY,CAAC,EAAG,GAAK,IAAK,IAAM,KAChCC,YAAe,CAAC,EAAG,IAAM,IAAK,GAC9BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,WAAc,CAAC,GAAK,IAAK,IAAM,IAAK,GACpCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,YAAe,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC1CC,QAAW,CAAC,EAAG,EAAG,GAClBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GACrCC,KAAQ,CAAC,EAAG,IAAM,IAAK,GACvBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,MAAS,CAAC,EAAG,IAAM,IAAK,GACxBC,YAAe,CAAC,IAAM,IAAK,EAAG,GAAK,KACnCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,SAAY,CAAC,IAAM,IAAK,EAAG,IAAM,KACjCC,QAAW,CAAC,EAAG,IAAM,IAAK,IAAM,KAChCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,OAAU,CAAC,GAAK,IAAK,EAAG,IAAM,KAC9BC,MAAS,CAAC,EAAG,EAAG,IAAM,KACtBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,EAAG,IAAM,IAAK,IAAM,KACrCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,GAC5BC,qBAAwB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrDC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,cAAiB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC7CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,KAAQ,CAAC,EAAG,EAAG,GACfC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,QAAW,CAAC,EAAG,EAAG,GAClBC,OAAU,CAAC,IAAM,IAAK,EAAG,GACzBC,iBAAoB,CAAC,GAAW,IAAM,IAAK,IAAM,KACjDC,WAAc,CAAC,EAAG,EAAG,IAAM,KAC3BC,aAAgB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC5CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC9CC,gBAAmB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAChDC,kBAAqB,CAAC,EAAG,IAAM,IAAK,IAAM,KAC1CC,gBAAmB,CAAC,GAAK,IAAK,IAAM,IAAK,IACzCC,gBAAmB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC/CC,aAAgB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,IAAM,KAClCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,EAAG,IAAM,KACrBC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,GAChCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,OAAU,CAAC,EAAG,IAAM,IAAK,GACzBC,UAAa,CAAC,EAAG,GAAK,IAAK,GAC3BC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,WAAc,CAAC,EAAG,IAAM,IAAK,IAAM,KACnCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,IAAM,IAAK,EAAG,IAAM,KAC/BC,cAAiB,CAAC,GAAW,GAAU,IACvCC,IAAO,CAAC,EAAG,EAAG,GACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,YAAe,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KAC1CC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC1CC,SAAY,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACvCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,OAAU,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACrCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,IAAO,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,OAAU,CAAC,EAAG,GAAK,IAAK,GAAK,KAC7BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtChf,MAAS,CAAC,EAAG,EAAG,GAChBif,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,EAAG,EAAG,GACjBC,YAAe,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,MCxJ5C,IAAItjB,GAAexG,MAAM,GAAG+pB,KAAK,mCAC7BC,GAAqBhqB,MAAM,GAAG+pB,KAAK,oBAEvC,IAAeE,GAAA,IAAItc,EAAc,CAChCrH,GAAI,OACJvC,KAAM,OACNN,KAAMkd,GACNlW,SAAUyD,GAIFA,EAAIhO,KAAIkT,IACd,IAAI7P,EAAO6P,EAAM,GAAK,EAAI,EACtBpR,EAAMoR,EAAM7P,EAEhB,OAAIvB,EAAM,SACFuB,GAAQ,MAASvB,IAAQ,EAAI,KAAQ,MAGtC,MAAQoR,CAAG,IAGpB1I,OAAQwD,GAIAA,EAAIhO,KAAIkT,IACd,IAAI7P,EAAO6P,EAAM,GAAK,EAAI,EACtBpR,EAAMoR,EAAM7P,EAEhB,OAAIvB,GAAO,OACHoR,EAAM,MAGP7P,IAAUvB,EAAM,MAAS,QAAU,GAAI,IAGhDiI,QAAS,CACRiE,IAAO,CACN/H,OAAQK,IAET0jB,WAAc,CACbnmB,KAAM,MACNuc,QAAQ,EACRna,OAAQ6jB,GACR3J,SAAS,GAEV7Y,MAAS,CAAsB,EAC/B2iB,KAAQ,CACPhkB,OAAQK,GACR8Z,QAAQ,EACRpW,WAAW,GAEZkgB,YAAe,CACdrmB,KAAM,OACNuc,QAAQ,EACRna,OAAQ6jB,IAETK,IAAO,CACNtpB,KAAM,SACN6c,SAAS,EACTxV,KAAMtH,GAAO,2BAA2BsH,KAAKtH,GAC7C,KAAAsG,CAAOtG,GACFA,EAAIf,QAAU,IAEjBe,EAAMA,EAAIiH,QAAQ,aAAc,SAGjC,IAAIoiB,EAAO,GAKX,OAJArpB,EAAIiH,QAAQ,iBAAiBuiB,IAC5BH,EAAK3hB,KAAK+hB,SAASD,EAAW,IAAM,IAAI,IAGlC,CACN1gB,QAAS,OACTzD,OAAQgkB,EAAKhiB,MAAM,EAAG,GACtBI,MAAO4hB,EAAKhiB,MAAM,GAAG,GAEtB,EACD2X,UAAW,CAAC3Z,EAAQoC,GACnBiiB,YAAW,GACR,MACCjiB,EAAQ,GACXpC,EAAOqC,KAAKD,GAGbpC,EAASA,EAAOjG,KAAIU,GAAKkB,KAAK2oB,MAAU,IAAJ7pB,KAEpC,IAAI8pB,EAAcF,GAAYrkB,EAAOuF,OAAM9K,GAAKA,EAAI,IAAO,IAEvDypB,EAAMlkB,EAAOjG,KAAIU,GAChB8pB,GACK9pB,EAAI,IAAIO,SAAS,IAGnBP,EAAEO,SAAS,IAAIwpB,SAAS,EAAG,OAChCzd,KAAK,IAER,MAAO,IAAMmd,CAAG,GAGlBO,QAAW,CACV7pB,KAAM,SACNqH,KAAMtH,GAAO,YAAYsH,KAAKtH,GAC9B,KAAAsG,CAAOtG,GAEN,IAAIH,EAAM,CAACiJ,QAAS,OAAQzD,OAAQ,KAAMoC,MAAO,GAUjD,GARY,iBAHZzH,EAAMA,EAAIQ,gBAITX,EAAIwF,OAASya,GAASxB,MACtBze,EAAI4H,MAAQ,GAGZ5H,EAAIwF,OAASya,GAAS9f,GAGnBH,EAAIwF,OACP,OAAOxF,CAER,MCvHWkqB,GAAA,IAAIld,EAAc,CAChCrH,GAAI,KACJ0D,MAAO,aACPjG,KAAM,KACNN,KAAMid,GAENjW,SAAUwf,GAAKxf,SACfC,OAAQuf,GAAKvf,SCEd,IAAIogB,GAEJ,GAJA1lB,EAAS2lB,cAAgBd,GAIN,oBAARe,KAAuBA,IAAIC,SAErC,IAAK,IAAIhlB,IAAS,CAACmJ,EAAKoR,GAASqK,IAAK,CACrC,IAAI1kB,EAASF,EAAMoG,eAEfvL,EAAMgf,GADE,CAAC7Z,QAAOE,SAAQoC,MAAO,IAGnC,GAAIyiB,IAAIC,SAAS,QAASnqB,GAAM,CAC/BsE,EAAS2lB,cAAgB9kB,EACzB,KACA,CACD,CCnBK,SAASilB,GAAc1jB,GAE7B,OAAO6C,EAAI7C,EAAO,CAACgG,EAAS,KAC7B,CCHe,SAAS2d,GAAgBxX,EAAQC,GAC/CD,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAElB,IAAIwX,EAAKtpB,KAAK8J,IAAIsf,GAAavX,GAAS,GACpC0X,EAAKvpB,KAAK8J,IAAIsf,GAAatX,GAAS,GAMxC,OAJIyX,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,KAGTA,EAAK,MAAQC,EAAK,IAC3B,CCXA,MAMMC,GAAU,KACVC,GAAU,MAWhB,SAASC,GAAQC,GAChB,OAAIA,GAAKH,GACDG,EAEDA,GAAKH,GAAUG,IAAMF,EAC7B,CAEA,SAASG,GAAWtY,GACnB,IAAI7P,EAAO6P,EAAM,GAAK,EAAI,EACtBpR,EAAMF,KAAKE,IAAIoR,GACnB,OAAO7P,EAAOzB,KAAKuN,IAAIrN,EAAK,IAC7B,CAGe,SAAS2pB,GAAcC,EAAYC,GAIjD,IAAIC,EACAvR,EACAwR,EAGAC,EAAGva,EAAG5R,EARVgsB,EAAazhB,EAASyhB,GACtBD,EAAaxhB,EAASwhB,GAStBC,EAAa1oB,GAAG0oB,EAAY,SAK3BG,EAAGva,EAAG5R,GAAKgsB,EAAW1lB,OACvB,IAAI8lB,EAAwB,SAAfP,GAAUM,GAAgC,SAAfN,GAAUja,GAAgC,QAAfia,GAAU7rB,GAE7E+rB,EAAazoB,GAAGyoB,EAAY,SAC3BI,EAAGva,EAAG5R,GAAK+rB,EAAWzlB,OACvB,IAAI+lB,EAAuB,SAAfR,GAAUM,GAAgC,SAAfN,GAAUja,GAAgC,QAAfia,GAAU7rB,GAGxEssB,EAAOX,GAAOS,GACdG,EAAMZ,GAAOU,GAGbG,EAAMD,EAAMD,EAgChB,OA3BIrqB,KAAKE,IAAIoqB,EAAMD,GAxDF,KAyDhB5R,EAAI,EAGA8R,GAEHP,EAAIM,GAvEQ,IAuEQD,GAtEP,IAuEb5R,EA3Dc,KA2DVuR,IAIJA,EAAIM,GAzEO,IAyEQD,GA1EP,IA2EZ5R,EA9Dc,KA8DVuR,GAILC,EADGjqB,KAAKE,IAAIuY,GAxEC,GAyEN,EAECA,EAAI,EAGLA,EAxEW,KA2EXA,EA3EW,KA8EL,IAAPwR,CACR,CC7Fe,SAASO,GAAmB3Y,EAAQC,GAClDD,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAElB,IAAIwX,EAAKtpB,KAAK8J,IAAIsf,GAAavX,GAAS,GACpC0X,EAAKvpB,KAAK8J,IAAIsf,GAAatX,GAAS,GAEpCyX,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGjB,IAAImB,EAASnB,EAAKC,EAClB,OAAiB,IAAVkB,EAAc,GAAKnB,EAAKC,GAAMkB,CACtC,CCPe,SAASC,GAAe7Y,EAAQC,GAC9CD,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAElB,IAAIwX,EAAKtpB,KAAK8J,IAAIsf,GAAavX,GAAS,GACpC0X,EAAKvpB,KAAK8J,IAAIsf,GAAatX,GAAS,GAMxC,OAJIyX,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGH,IAAPC,EAbI,KAacD,EAAKC,GAAMA,CACrC,CClBe,SAASoB,GAAe9Y,EAAQC,GAC9CD,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAElB,IAAI5C,EAAK3G,EAAIsJ,EAAQ,CAAC7E,EAAK,MACvBsC,EAAK/G,EAAIuJ,EAAQ,CAAC9E,EAAK,MAE3B,OAAOhN,KAAKE,IAAIgP,EAAKI,EACtB,CCXA,MACM,GAAK,GAAK,IACVvC,GAAI,MAAQ,GAElB,IAAIlE,GAAQlG,EAAOE,IAEnB,IAAe+nB,GAAA,IAAIxjB,EAAW,CAC7B5C,GAAI,UACJvC,KAAM,UACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdnD,KAAM,aAEPiL,EAAG,CACF9H,SAAU,EAAE,IAAK,MAElB4G,EAAG,CACF5G,SAAU,EAAE,IAAK,OAMpByD,MAACA,GAEAlH,KAAM+J,EAGN,QAAA/C,CAAUzF,GAET,IAGIkK,EAHMlK,EAAI9E,KAAI,CAACkD,EAAO7C,IAAM6C,EAAQuH,GAAMpK,KAGlCL,KAAIkD,GAASA,EAlCjB,oBAkC6BtB,KAAKqN,KAAK/L,IAAUyL,GAAIzL,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAM8L,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAID,MAAAxE,CAAQ0E,GAEP,IAAIF,EAAI,GAaR,OAZAA,EAAE,IAAME,EAAI,GAAK,IAAM,IACvBF,EAAE,GAAKE,EAAI,GAAK,IAAMF,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAKE,EAAI,GAAK,IAGb,CACTF,EAAE,GAAO,GAAKpN,KAAKuN,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,GACrEO,EAAI,GAAK,EAAKtN,KAAKuN,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKP,GAC1DK,EAAE,GAAO,GAAKpN,KAAKuN,IAAIH,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAML,IAI3D3O,KAAI,CAACkD,EAAO7C,IAAM6C,EAAQuH,GAAMpK,IAC3C,EAED0J,QAAS,CACR,UAAW,CACV9D,OAAQ,CAAC,0BAA2B,gCAAiC,qCC5DxE,MAAMwmB,GAAyB,GAAnB7qB,KAAKuN,IAAI,EAAG,IAAa,GAEtB,SAASud,GAAkBjZ,EAAQC,GACjDD,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAElB,IAAIiZ,EAAQxiB,EAAIsJ,EAAQ,CAAC+Y,GAAS,MAC9BI,EAAQziB,EAAIuJ,EAAQ,CAAC8Y,GAAS,MAE9BK,EAAejrB,KAAKE,IAAIF,KAAKuN,IAAIwd,EAAOF,IAAO7qB,KAAKuN,IAAIyd,EAAOH,KAE/DK,EAAWlrB,KAAKuN,IAAI0d,EAAe,EAAIJ,IAAQ7qB,KAAKmrB,MAAQ,GAEhE,OAAQD,EAAW,IAAO,EAAMA,CACjC,qJCpBO,SAASE,GAAI1lB,GAEnB,IAAK2lB,EAAG1B,EAAG2B,GAAK/e,EAAO7G,EAAOgG,GAC1B+e,EAAQY,EAAI,GAAK1B,EAAI,EAAI2B,EAC7B,MAAO,CAAC,EAAID,EAAIZ,EAAO,EAAId,EAAIc,EAChC,CCLe,SAASjnB,GAAQ2O,EAAIC,EAAIlT,EAAI,CAAA,GACvCH,EAASG,KACZA,EAAI,CAACuc,OAAQvc,IAGd,IAAIuc,OAACA,EAASnY,EAASE,UAAW+nB,GAAQrsB,EAE1C,IAAK,IAAIlB,KAAKud,GACb,GAAI,SAAWE,EAAOjc,gBAAkBxB,EAAEwB,cACzC,OAAO+b,GAAcvd,GAAGmU,EAAIC,EAAImZ,GAIlC,MAAM,IAAInoB,UAAU,0BAA0BqY,IAC/C,CC0GO,SAASvW,GAAO2M,EAAQC,EAAQ3O,EAAU,CAAA,GAChD,GAAIqoB,GAAQ3Z,GAAS,CAEpB,IAAK/F,EAAG3I,GAAW,CAAC0O,EAAQC,GAE5B,OAAO5M,MAAS4G,EAAE2f,UAAUC,OAAQ,IAAI5f,EAAE2f,UAAUtoB,WAAYA,GAChE,CAED,IAAIgB,MAACA,EAAKwnB,YAAEA,EAAWC,YAAEA,EAAWC,cAAEA,GAAiB1oB,EAEvD0O,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAGlBD,EAASF,EAAME,GACfC,EAASH,EAAMG,GAEf,IAAI2Z,EAAY,CAACC,OAAQ,CAAC7Z,EAAQC,GAAS3O,WAoB3C,GAjBCgB,EADGA,EACKiD,EAAWmB,IAAIpE,GAGfiD,EAAWa,SAAS3E,EAASwoB,qBAAuBja,EAAO1N,MAGpEwnB,EAAcA,EAAcvkB,EAAWmB,IAAIojB,GAAexnB,EAE1D0N,EAASxQ,GAAGwQ,EAAQ1N,GACpB2N,EAASzQ,GAAGyQ,EAAQ3N,GAGpB0N,EAASiK,GAAQjK,GACjBC,EAASgK,GAAQhK,GAIb3N,EAAME,OAAOsJ,GAA6B,UAAxBxJ,EAAME,OAAOsJ,EAAE1O,KAAkB,CACtD,IAAI8sB,EAAM5oB,EAAQyK,IAAMzK,EAAQyK,KAAO,UAEnCA,EAAM,CAACzJ,EAAO,MACb,EAAI,GAAM,CAACoE,EAAIsJ,EAAQjE,GAAMrF,EAAIuJ,EAAQlE,IAI1CrN,MAAM,KAAQA,MAAM,GACvB,EAAK,EAEGA,MAAM,KAAQA,MAAM,KAC5B,EAAK,IAEL,EAAI,GzC3KA,SAAiBwrB,EAAKC,GAC5B,GAAY,QAARD,EACH,OAAOC,EAGR,IAAK7c,EAAII,GAAMyc,EAAO5tB,IAAIoP,GAEtBye,EAAY1c,EAAKJ,EA+BrB,MA7BY,eAAR4c,EACCE,EAAY,IACf1c,GAAM,KAGS,eAARwc,EACJE,EAAY,IACf9c,GAAM,KAGS,WAAR4c,GACH,IAAME,GAAaA,EAAY,MAC/BA,EAAY,EACf9c,GAAM,IAGNI,GAAM,KAIQ,YAARwc,IACJE,EAAY,IACf9c,GAAM,IAEE8c,GAAa,MACrB1c,GAAM,MAID,CAACJ,EAAII,EACb,CyCoIa2c,CAAcH,EAAK,CAAC,EAAI,IACnCrf,EAAImF,EAAQjE,EAAK,GACjBlB,EAAIoF,EAAQlE,EAAK,EACjB,CAQD,OANIie,IAEHha,EAAOxN,OAASwN,EAAOxN,OAAOjG,KAAIU,GAAKA,EAAI+S,EAAOpL,QAClDqL,EAAOzN,OAASyN,EAAOzN,OAAOjG,KAAIU,GAAKA,EAAIgT,EAAOrL,SAG5CtH,OAAOyI,QAAOtJ,IACpBA,EAAIstB,EAAcA,EAAYttB,GAAKA,EACnC,IAAI+F,EAASwN,EAAOxN,OAAOjG,KAAI,CAAC6C,EAAOxC,IAE/BuC,EAAYC,EADT6Q,EAAOzN,OAAO5F,GACOH,KAG5BmI,EAAQzF,EAAY6Q,EAAOpL,MAAOqL,EAAOrL,MAAOnI,GAChDO,EAAM,CAACsF,QAAOE,SAAQoC,SAW1B,OATIolB,IAEHhtB,EAAIwF,OAASxF,EAAIwF,OAAOjG,KAAIU,GAAKA,EAAI2H,KAGlCklB,IAAgBxnB,IACnBtF,EAAMwC,GAAGxC,EAAK8sB,IAGR9sB,CAAG,GACR,CACF4sB,aAEF,CAEO,SAASD,GAASla,GACxB,MAAqB,aAAdrS,EAAKqS,MAAyBA,EAAIma,SAC1C,CAEAnoB,EAASwoB,mBAAqB,MCpN9B,IAAeK,GAAA,IAAI/kB,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACPsJ,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,OAEPoJ,EAAG,CACFnG,MAAO,CAAC,EAAG,KACXjD,KAAM,cAEPgL,EAAG,CACF/H,MAAO,CAAC,EAAG,KACXjD,KAAM,cAIRN,KAAMwmB,GAGNxf,SAAUyD,IACT,IAAItC,EAAM9J,KAAK8J,OAAOsC,GAClBvC,EAAM7J,KAAK6J,OAAOuC,IACjBN,EAAGC,EAAGC,GAAKI,GACXuB,EAAGtC,EAAG4B,GAAK,CAAC1G,IAAK,GAAIsD,EAAMC,GAAO,GACnChI,EAAIgI,EAAMD,EAEd,GAAU,IAAN/H,EAAS,CAGZ,OAFAuJ,EAAW,IAAN4B,GAAiB,IAANA,EAAW,GAAKnD,EAAMmD,GAAKjN,KAAK6J,IAAIoD,EAAG,EAAIA,GAEnDnD,GACP,KAAKgC,EAAG6B,GAAK5B,EAAIC,GAAKlK,GAAKiK,EAAIC,EAAI,EAAI,GAAI,MAC3C,KAAKD,EAAG4B,GAAK3B,EAAIF,GAAKhK,EAAI,EAAG,MAC7B,KAAKkK,EAAG2B,GAAK7B,EAAIC,GAAKjK,EAAI,EAG3B6L,GAAQ,EACR,CAcD,OATItC,EAAI,IACPsC,GAAK,IACLtC,EAAIrL,KAAKE,IAAImL,IAGVsC,GAAK,MACRA,GAAK,KAGC,CAACA,EAAO,IAAJtC,EAAa,IAAJ4B,EAAQ,EAI7BrE,OAAQwjB,IACP,IAAKze,EAAGtC,EAAG4B,GAAKmf,EAUhB,SAAShf,EAAG1N,GACX,IAAI2sB,GAAK3sB,EAAIiO,EAAI,IAAM,GACnBT,EAAI7B,EAAIrL,KAAK6J,IAAIoD,EAAG,EAAIA,GAC5B,OAAOA,EAAIC,EAAIlN,KAAK8J,KAAK,EAAG9J,KAAK6J,IAAIwiB,EAAI,EAAG,EAAIA,EAAG,GACnD,CAED,OAfA1e,GAAQ,IAEJA,EAAI,IACPA,GAAK,KAGNtC,GAAK,IACL4B,GAAK,IAQE,CAACG,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAG,EAG1BjF,QAAS,CACRikB,IAAO,CACN/nB,OAAQ,CAAC,qBAAsB,eAAgB,iBAEhDioB,KAAQ,CACPjoB,OAAQ,CAAC,qBAAsB,eAAgB,gBAC/Cma,QAAQ,EACRpW,WAAW,MC/ECmkB,GAAA,IAAInlB,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACPsJ,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,OAEPoJ,EAAG,CACFnG,MAAO,CAAC,EAAG,KACXjD,KAAM,cAEP2Q,EAAG,CACF1N,MAAO,CAAC,EAAG,KACXjD,KAAM,UAIRN,KAAMwqB,GAEN,QAAAxjB,CAAUyjB,GACT,IAAKze,EAAGtC,EAAG4B,GAAKmf,EAChB/gB,GAAK,IACL4B,GAAK,IAEL,IAAI2F,EAAI3F,EAAI5B,EAAIrL,KAAK6J,IAAIoD,EAAG,EAAIA,GAEhC,MAAO,CACNU,EACM,IAANiF,EAAU,EAAI,KAAO,EAAI3F,EAAI2F,GAC7B,IAAMA,EAEP,EAED,MAAAhK,CAAQ4jB,GACP,IAAK7e,EAAGtC,EAAGuH,GAAK4Z,EAEhBnhB,GAAK,IACLuH,GAAK,IAEL,IAAI3F,EAAI2F,GAAK,EAAIvH,EAAI,GAErB,MAAO,CACNsC,EACO,IAANV,GAAiB,IAANA,EAAW,GAAM2F,EAAI3F,GAAKjN,KAAK6J,IAAIoD,EAAG,EAAIA,GAAM,IACxD,IAAJA,EAED,EAED9E,QAAS,CACRzC,MAAO,CACNlB,GAAI,QACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BCrD9CooB,GAAA,IAAIrlB,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACPsJ,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,OAEPyqB,EAAG,CACFxnB,MAAO,CAAC,EAAG,KACXjD,KAAM,aAEP+J,EAAG,CACF9G,MAAO,CAAC,EAAG,KACXjD,KAAM,cAIRN,KAAM4qB,GACN,QAAA5jB,CAAU6jB,GACT,IAAK7e,EAAGtC,EAAGuH,GAAK4Z,EAEhB,MAAO,CAAC7e,EAAGiF,GAAK,IAAMvH,GAAK,IAAK,IAAMuH,EACtC,EACD,MAAAhK,CAAQ6jB,GACP,IAAK9e,EAAG+e,EAAG1gB,GAAKygB,EAGhBC,GAAK,IACL1gB,GAAK,IAGL,IAAI2gB,EAAMD,EAAI1gB,EACd,GAAI2gB,GAAO,EAAG,CAEb,MAAO,CAAChf,EAAG,EAAU,KADV+e,EAAIC,GAEf,CAED,IAAI/Z,EAAK,EAAI5G,EAEb,MAAO,CAAC2B,EAAO,KADA,IAANiF,EAAW,EAAI,EAAI8Z,EAAI9Z,GACR,IAAJA,EACpB,EAEDzK,QAAS,CACRskB,IAAO,CACNpoB,OAAQ,CAAC,qBAAsB,0BAA2B,+BClC7D,IAAeuoB,GAAA,IAAI/gB,EAAc,CAChCrH,GAAI,gBACJ0D,MAAO,mBACPjG,KAAM,kCACN4G,MAAO,MACRqD,QAjBgB,CACf,CAAE,kBAAsB,kBAAsB,mBAC9C,CAAE,mBAAsB,kBAAsB,oBAC9C,CAAE,mBAAsB,mBAAsB,oBAe/CC,UAZkB,CACjB,CAAG,oBAAwB,mBAAuB,oBAClD,EAAG,kBAAwB,mBAAuB,oBAClD,CAAG,qBAAwB,mBAAuB,uBCdpC0gB,GAAA,IAAIhhB,EAAc,CAChCrH,GAAI,SACJ0D,MAAO,UACPjG,KAAM,2BACNN,KAAMirB,GACNhkB,OAAQ+V,GAAOA,EAAIvgB,KAAIkT,GAAOtR,KAAKuN,IAAIvN,KAAKE,IAAIoR,GAAM,IAAM,KAAOtR,KAAKyB,KAAK6P,KAC7E3I,SAAUgW,GAAOA,EAAIvgB,KAAIkT,GAAOtR,KAAKuN,IAAIvN,KAAKE,IAAIoR,GAAM,IAAM,KAAOtR,KAAKyB,KAAK6P,OCUhF,IAAewb,GAAA,IAAIjhB,EAAc,CAChCrH,GAAI,kBACJ0D,MAAO,wBACPjG,KAAM,kBACN4G,MAAO,MACPlH,KAAMkL,EACPX,QAlBgB,CACf,CAAE,kBAAsB,mBAAsB,mBAC9C,CAAE,kBAAsB,iBAAsB,mBAC9C,CAAE,EAAsB,EAAsB,oBAgB/CC,UAbkB,CACjB,CAAG,oBAAsB,oBAAsB,oBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,EAAsB,EAAsB,uBCVhD,IAAe4gB,GAAA,IAAIlhB,EAAc,CAChCrH,GAAI,WACJ0D,MAAO,eACPjG,KAAM,WACNN,KAAMmrB,GACNlkB,OAAQ+V,GAEAA,EAAIvgB,KAAIwU,GAAKA,EATV,OASoBA,EAAI,GAAKA,GAAK,MAE7CjK,SAAUgW,GACFA,EAAIvgB,KAAIwU,GAAKA,GAbX,WAaqBA,IAAM,EAAI,KAAO,GAAKA,MCZvCoa,GAAA,IAAI5lB,EAAW,CAC7B5C,GAAI,QACJvC,KAAM,QACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,GACdnD,KAAM,aAEPnD,EAAG,CACFsG,SAAU,CAAC,EAAG,IACdnD,KAAM,UAEP0L,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,QAGR4G,MAAO,MAEPlH,KAAMyP,EACN,QAAAzI,CAAU6I,GAET,IACI7D,GADCE,EAAGX,EAAGlB,GAAKwF,EAEhB,MAAM,EAAI,KASV,OANC7D,EADG3N,KAAKE,IAAIgN,GAAK,GAAKlN,KAAKE,IAAI8L,GAAK,EAChCzF,IAGmB,IAAnBvG,KAAK8N,MAAM9B,EAAGkB,GAAWlN,KAAKc,GAG5B,CACN+M,EACA7N,KAAK+N,KAAKb,GAAK,EAAIlB,GAAK,GACxBgC,EAAeL,GAEhB,EAED,MAAA/E,CAAQokB,GACP,IACI9f,EAAGlB,GADF6B,EAAG4K,EAAG9K,GAAKqf,EAahB,OATIzsB,MAAMoN,IACTT,EAAI,EACJlB,EAAI,IAGJkB,EAAIuL,EAAIzY,KAAKqO,IAAIV,EAAI3N,KAAKc,GAAK,KAC/BkL,EAAIyM,EAAIzY,KAAKsO,IAAIX,EAAI3N,KAAKc,GAAK,MAGzB,CAAE+M,EAAGX,EAAGlB,EACf,EAED7D,QAAS,CACR6kB,MAAS,CACR3oB,OAAQ,CAAC,0BAA2B,+BAAgC,0BC1DvE,IAAIwE,GAAQlG,EAAOE,IAEnB,MACMkK,GAAI,MAAQ,IACXkgB,GAAeC,IAAiB9B,GAAG,CAACjnB,MAAOuH,EAASrH,OAAQwE,KAEnE,IAAeskB,GAAA,IAAI/lB,EAAW,CAC7B5C,GAAI,MACJvC,KAAM,MACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdnD,KAAM,aAGPmrB,EAAG,CACFhoB,SAAU,EAAE,IAAK,MAElBwN,EAAG,CACFxN,SAAU,EAAE,IAAK,OAInByD,MAAOA,GACPlH,KAAM+J,EAIN,QAAA/C,CAAUzF,GACT,IAAImJ,EAAM,CAAC5L,EAASyC,EAAI,IAAKzC,EAASyC,EAAI,IAAKzC,EAASyC,EAAI,KACxDyI,EAAIU,EAAI,IAEPghB,EAAIC,GAAMlC,GAAG,CAACjnB,MAAOuH,EAASrH,OAAQgI,IAG3C,IAAK/L,OAAOitB,SAASF,KAAQ/sB,OAAOitB,SAASD,GAC5C,MAAO,CAAC,EAAG,EAAG,GAGf,IAAIzf,EAAIlC,GArCA,oBAqCSoB,GAAIpB,EAAI,IAAM3L,KAAKqN,KAAK1B,GAAK,GAC9C,MAAO,CACNkC,EACA,GAAKA,GAAKwf,EAAKJ,IACf,GAAKpf,GAAKyf,EAAKJ,IAEhB,EAID,MAAAtkB,CAAQukB,GACP,IAAKtf,EAAGuf,EAAGxa,GAAKua,EAGhB,GAAU,IAANtf,GAAWhO,EAAOgO,GACrB,MAAO,CAAC,EAAG,EAAG,GAGfuf,EAAI3sB,EAAS2sB,GACbxa,EAAInS,EAASmS,GAEb,IAAIya,EAAMD,GAAK,GAAKvf,GAAMof,GACtBK,EAAM1a,GAAK,GAAK/E,GAAMqf,GAEtBvhB,EAAIkC,GAAK,EAAIA,EAAId,GAAI/M,KAAKuN,KAAKM,EAAI,IAAM,IAAK,GAElD,MAAO,CACNlC,GAAM,EAAI0hB,GAAO,EAAIC,IACrB3hB,EACAA,IAAM,GAAK,EAAI0hB,EAAK,GAAKC,IAAO,EAAIA,IAErC,EAEDnlB,QAAS,CACRzC,MAAO,CACNlB,GAAI,QACJH,OAAQ,CAAC,0BAA2B,gCAAiC,qCC7EzDmpB,GAAA,IAAIpmB,EAAW,CAC7B5C,GAAI,QACJvC,KAAM,QACNoC,OAAQ,CACP4I,EAAG,CACF7H,SAAU,CAAC,EAAG,KACdnD,KAAM,aAEPnD,EAAG,CACFsG,SAAU,CAAC,EAAG,KACdnD,KAAM,UAEP0L,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,QAIRN,KAAMwrB,GACN,QAAAxkB,CAAUwkB,GAET,IACIvf,GADCC,EAAGuf,EAAGxa,GAAKua,EAWhB,OANCvf,EADG5N,KAAKE,IAAIktB,GAFH,KAEaptB,KAAKE,IAAI0S,GAFtB,IAGHrM,IAGmB,IAAnBvG,KAAK8N,MAAM8E,EAAGwa,GAAWptB,KAAKc,GAG9B,CACN+M,EACA7N,KAAK+N,KAAKqf,GAAK,EAAIxa,GAAK,GACxB5E,EAAeJ,GAEhB,EACD,MAAAhF,CAAQqF,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGN5N,MAAM6N,KACTA,EAAM,GAEA,CACNF,EACAC,EAASnO,KAAKqO,IAAID,EAAMpO,KAAKc,GAAK,KAClCqN,EAASnO,KAAKsO,IAAIF,EAAMpO,KAAKc,GAAK,KAEnC,EAEDqH,QAAS,CACRzC,MAAO,CACNlB,GAAI,UACJH,OAAQ,CAAC,0BAA2B,0BAA2B,0BClClE,MAGMopB,GAAOthB,GAAU,GAAG,GACpBuhB,GAAOvhB,GAAU,GAAG,GACpBwhB,GAAOxhB,GAAU,GAAG,GACpByhB,GAAOzhB,GAAU,GAAG,GACpB0hB,GAAO1hB,GAAU,GAAG,GACpB2hB,GAAO3hB,GAAU,GAAG,GACpB4hB,GAAO5hB,GAAU,GAAG,GACpB6hB,GAAO7hB,GAAU,GAAG,GACpB8hB,GAAO9hB,GAAU,GAAG,GAE1B,SAAS+hB,GAAyBC,EAAOC,EAAW3gB,GACnD,MAAM3L,EAAIssB,GAAapuB,KAAKsO,IAAIb,GAAS0gB,EAAQnuB,KAAKqO,IAAIZ,IAC1D,OAAO3L,EAAI,EAAI+Y,IAAW/Y,CAC3B,CAEO,SAASusB,GAAwBphB,GACvC,MAAMqhB,EAAOtuB,KAAKuN,IAAIN,EAAI,GAAI,GAAK,QAC7BshB,EAAOD,EApBJ,oBAoBeA,EAAOrhB,EAnBtB,kBAoBHuhB,EAAMD,GAAQ,OAASd,GAAO,MAAQE,IACtCc,EAAMF,GAAQ,OAASZ,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMH,GAAQ,OAASZ,GAAO,OAASD,IACvCiB,EAAMJ,GAAQ,OAASX,GAAO,MAAQE,IACtCc,EAAML,GAAQ,OAAST,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMN,GAAQ,OAAST,GAAO,OAASD,IACvCiB,EAAMP,GAAQ,OAASR,GAAO,MAAQE,IACtCc,EAAMR,GAAQ,OAASN,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMT,GAAQ,OAASN,GAAO,OAASD,IAE7C,MAAO,CACNiB,IAAKT,EAAME,EACXQ,IAAKT,EAAMxhB,EAAIyhB,EACfS,IAAKX,GAAOE,EAAM,QAClBU,KAAMX,EAAM,QAAUxhB,GAAKyhB,EAAM,QACjCW,IAAKV,EAAME,EACXS,IAAKV,EAAM3hB,EAAI4hB,EACfU,IAAKZ,GAAOE,EAAM,QAClBW,KAAMZ,EAAM,QAAU3hB,GAAK4hB,EAAM,QACjCY,IAAKX,EAAME,EACXU,IAAKX,EAAM9hB,EAAI+hB,EACfW,IAAKb,GAAOE,EAAM,QAClBY,KAAMb,EAAM,QAAU9hB,GAAK+hB,EAAM,QAEnC,CAEA,SAASa,GAAoBC,EAAOniB,GACnC,MAAMoiB,EAASpiB,EAAI,IAAM3N,KAAKc,GAAK,EAC7BkvB,EAAK9B,GAAwB4B,EAAMb,IAAKa,EAAMZ,IAAKa,GACnDE,EAAK/B,GAAwB4B,EAAMX,IAAKW,EAAMV,IAAKW,GACnDG,EAAKhC,GAAwB4B,EAAMT,IAAKS,EAAMR,IAAKS,GACnDI,EAAKjC,GAAwB4B,EAAMP,IAAKO,EAAMN,IAAKO,GACnDK,EAAKlC,GAAwB4B,EAAML,IAAKK,EAAMJ,IAAKK,GACnD3gB,EAAK8e,GAAwB4B,EAAMH,IAAKG,EAAMF,IAAKG,GAEzD,OAAO/vB,KAAK6J,IAAImmB,EAAIC,EAAIC,EAAIC,EAAIC,EAAIhhB,EACrC,CAEA,IAAeihB,GAAA,IAAIjpB,EAAW,CAC7B5C,GAAI,QACJvC,KAAM,QACNoC,OAAQ,CACPsJ,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,OAEPoJ,EAAG,CACFnG,MAAO,CAAC,EAAG,KACXjD,KAAM,cAEPgL,EAAG,CACF/H,MAAO,CAAC,EAAG,KACXjD,KAAM,cAIRN,KAAM6rB,GACN1kB,WAAYqf,GAGZ,QAAAxf,CAAU+E,GACT,IACIrC,GADC4B,EAAGnO,EAAG6O,GAAK,CAAClN,EAASiN,EAAI,IAAKjN,EAASiN,EAAI,IAAKjN,EAASiN,EAAI,KAGlE,GAAIT,EAAI,WACP5B,EAAI,EACJ4B,EAAI,SAEA,GAAIA,EAAI,KACZ5B,EAAI,EACJ4B,EAAI,MAEA,CAGJ5B,EAAIvM,EADM+wB,GADExB,GAAuBphB,GACCU,GACtB,GACd,CAED,MAAO,CAACA,EAAGtC,EAAG4B,EACd,EAGD,MAAArE,CAAQwjB,GACP,IACIttB,GADC6O,EAAGtC,EAAG4B,GAAK,CAACxM,EAAS2rB,EAAI,IAAK3rB,EAAS2rB,EAAI,IAAK3rB,EAAS2rB,EAAI,KAGlE,GAAInf,EAAI,WACPA,EAAI,IACJnO,EAAI,OAEA,GAAImO,EAAI,KACZA,EAAI,EACJnO,EAAI,MAEA,CAGJA,EADU+wB,GADExB,GAAuBphB,GACCU,GAC1B,IAAMtC,CAChB,CAED,MAAO,CAAC4B,EAAGnO,EAAG6O,EACd,EAEDxF,QAAS,CACRzC,MAAO,CACNlB,GAAI,UACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BCnH7D,SAASisB,GAAoBnC,EAAOC,GACnC,OAAOpuB,KAAKE,IAAIkuB,GAAapuB,KAAK+N,KAAK/N,KAAKuN,IAAI4gB,EAAO,GAAK,EAC7D,CAEA,SAASoC,GAAoBT,GAC5B,IAAIE,EAAKM,GAAmBR,EAAMb,IAAKa,EAAMZ,KACzCe,EAAKK,GAAmBR,EAAMX,IAAKW,EAAMV,KACzCc,EAAKI,GAAmBR,EAAMT,IAAKS,EAAMR,KACzCa,EAAKG,GAAmBR,EAAMP,IAAKO,EAAMN,KACzCY,EAAKE,GAAmBR,EAAML,IAAKK,EAAMJ,KACzCtgB,EAAKkhB,GAAmBR,EAAMH,IAAKG,EAAMF,KAE7C,OAAO5vB,KAAK6J,IAAImmB,EAAIC,EAAIC,EAAIC,EAAIC,EAAIhhB,EACrC,CAvBajD,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GAiB1B,IAAeqkB,GAAA,IAAIppB,EAAW,CAC7B5C,GAAI,QACJvC,KAAM,QACNoC,OAAQ,CACPsJ,EAAG,CACFvI,SAAU,CAAC,EAAG,KACdnG,KAAM,QACNgD,KAAM,OAEPoJ,EAAG,CACFnG,MAAO,CAAC,EAAG,KACXjD,KAAM,cAEPgL,EAAG,CACF/H,MAAO,CAAC,EAAG,KACXjD,KAAM,cAIRN,KAAM6rB,GACN1kB,WAAY,OAGZ,QAAAH,CAAU+E,GACT,IACIrC,GADC4B,EAAGnO,EAAG6O,GAAK,CAAClN,EAASiN,EAAI,IAAKjN,EAASiN,EAAI,IAAKjN,EAASiN,EAAI,KAGlE,GAAIT,EAAI,WACP5B,EAAI,EACJ4B,EAAI,SAEA,GAAIA,EAAI,KACZ5B,EAAI,EACJ4B,EAAI,MAEA,CAGJ5B,EAAIvM,EADMyxB,GADElC,GAAuBphB,IAErB,GACd,CACD,MAAO,CAACU,EAAGtC,EAAG4B,EACd,EAGD,MAAArE,CAAQwjB,GACP,IACIttB,GADC6O,EAAGtC,EAAG4B,GAAK,CAACxM,EAAS2rB,EAAI,IAAK3rB,EAAS2rB,EAAI,IAAK3rB,EAAS2rB,EAAI,KAGlE,GAAInf,EAAI,WACPA,EAAI,IACJnO,EAAI,OAEA,GAAImO,EAAI,KACZA,EAAI,EACJnO,EAAI,MAEA,CAGJA,EADUyxB,GADElC,GAAuBphB,IAEzB,IAAM5B,CAChB,CAED,MAAO,CAAC4B,EAAGnO,EAAG6O,EACd,EAEDxF,QAAS,CACRzC,MAAO,CACNlB,GAAI,UACJH,OAAQ,CAAC,qBAAsB,0BAA2B,+BC3H7D,MACM3E,GAAI,KAAI,MAGR+wB,GAAO,GAAW,KAClBte,GAAK,SACLC,GAAK,KAAI,IACTU,GAAK,QAEX,IAAe4d,GAAA,IAAI7kB,EAAc,CAChCrH,GAAI,YACJ0D,MAAO,aACPjG,KAAM,cACNN,KAAM8c,GACN7V,OAAQ+V,GAGAA,EAAIvgB,KAAI,SAAUkT,GAExB,OAAY,KADFtR,KAAK8J,IAAMwH,GAAOmf,GAAQte,GAAK,IAAMC,GAAMU,GAAMxB,GAAOmf,MAhBxD,kBAFF,GAoBX,IAEC9nB,SAAUgW,GAGFA,EAAIvgB,KAAI,SAAUkT,GACxB,IAAIjT,EAAI2B,KAAK8J,IA1BL,IA0BSwH,EAAW,IAAO,GAInC,QAHWa,GAAMC,GAAM/T,GAAKqB,KACf,EAAKoT,GAAMzU,GAAKqB,MAzBtB,QA4BV,MC7BA,MAAMwN,GAAI,UACJlB,GAAI,UACJlN,GAAI,UAEJ6xB,GAAQ,OAEd,IAAeC,GAAA,IAAI/kB,EAAc,CAChCrH,GAAI,aACJ0D,MAAO,cACPjG,KAAM,eACNiH,SAAU,QAEVvH,KAAM8c,GACN7V,OAAQ+V,GAGAA,EAAIvgB,KAAI,SAAUkT,GAKxB,OAAIA,GAAO,GACFA,GAAO,EAAK,EAAIqf,IAEhB3wB,KAAK4B,KAAK0P,EAAMxS,IAAKoO,IAAKlB,IAAK,GAAM2kB,EACjD,IAEChoB,SAAUgW,GAIFA,EAAIvgB,KAAI,SAAUkT,GAMxB,OAJAA,GAAOqf,KAII,EAAI,GACP3wB,KAAK+N,KAAK,EAAIuD,GAEfpE,GAAIlN,KAAKkb,IAAI,GAAK5J,EAAMtF,IAAKlN,EACvC,MC1CO,MAAM+xB,GAAO,CAAA,EAcb,SAASC,IAAWtsB,GAACA,EAAEusB,SAAEA,EAAQC,WAAEA,IAEzCH,GAAKrsB,GAAMpC,UAAU,EACtB,CAEO,SAASW,GAAOC,EAAIC,EAAIuB,EAAK,YAKnC,IAAIiX,EAASoV,GAAKrsB,IAEb,EAAI,EAAI,GAAM3G,EAAiB4d,EAAOsV,SAAU/tB,IAChD,EAAI,EAAI,GAAMnF,EAAiB4d,EAAOsV,SAAU9tB,GAUjDguB,EAAgBpzB,EAPR,CACX,CAAC,EAAK,EAAK,EAAU,GACrB,CAAC,EAAU,EAAK,EAAK,GACrB,CAAC,EAAU,EAAU,EAAK,IAIiB4d,EAAOsV,UAGnD,OAFclzB,EAAiB4d,EAAOuV,WAAYC,EAGnD,CAvCAvuB,EAAMV,IAAI,8BAA8BQ,IACnCA,EAAIW,QAAQsY,SACfjZ,EAAIa,EAAIN,GAAMP,EAAIQ,GAAIR,EAAIS,GAAIT,EAAIW,QAAQsY,QAC1C,IAGF/Y,EAAMV,IAAI,4BAA4BQ,IAChCA,EAAIa,IACRb,EAAIa,EAAIN,GAAMP,EAAIQ,GAAIR,EAAIS,GAAIT,EAAIW,QAAQsY,QAC1C,IAgCFqV,GAAU,CACTtsB,GAAI,YACJusB,SAAU,CACT,CAAG,OAAY,OAAY,QAC3B,EAAG,MAAY,QAAY,OAC3B,CAAG,EAAY,EAAY,SAE5BC,WAAY,CACX,CAAE,oBAAqB,mBAAsB,oBAC7C,CAAE,kBAAqB,mBAAsB,sBAC7C,CAAE,EAAqB,EAAsB,uBAI/CF,GAAU,CACTtsB,GAAI,WAGJusB,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,OAAY,MAAY,SAG5BC,WAAY,CACX,CAAG,mBAAqB,mBAAqB,oBAC7C,CAAG,kBAAqB,kBAAqB,qBAC7C,EAAG,mBAAqB,mBAAqB,oBAI/CF,GAAU,CACTtsB,GAAI,QAEJusB,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,KAAY,MAAY,QAE5BC,WAAY,CACX,CAAG,oBAAuB,mBAAqB,oBAC/C,CAAG,kBAAuB,kBAAqB,oBAC/C,EAAG,qBAAuB,mBAAqB,uBAIjDF,GAAU,CACTtsB,GAAI,QACJusB,SAAU,CACT,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAG1BC,WAAY,CACX,CAAG,mBAAsB,mBAAqB,oBAC9C,CAAG,kBAAsB,mBAAqB,qBAC9C,EAAG,oBAAsB,mBAAqB,uBAIhD7xB,OAAOyI,OAAOjF,EAAQ,CAIrB7E,EAAK,CAAC,OAAS,EAAS,QAGxB2a,EAAK,CAAC,OAAS,EAAU,SAKzByY,IAAK,CAAC,OAAS,EAAS,QACxBC,IAAK,CAAC,OAAS,EAAS,SAGxBrT,EAAK,CAAC,EAAS,EAAS,GAGxBsT,GAAK,CAAC,OAAS,EAAS,QACxBC,GAAK,CAAC,OAAS,EAAS,SACxBC,IAAK,CAAC,QAAS,EAAS,SCzHzB3uB,EAAO4uB,KAAO,CAAC,OAAU,OAAS,EAAS,OAAgC,QAc3E,IAAeC,GAAA,IAAI3lB,EAAc,CAChCrH,GAAI,SACJ0D,MAAO,WACPjG,KAAM,SAKNoC,OAAQ,CACPyH,EAAG,CACF5G,MAAO,CAAC,EAAG,OACXjD,KAAM,OAEP8J,EAAG,CACF7G,MAAO,CAAC,EAAG,OACXjD,KAAM,SAEP+J,EAAG,CACF9G,MAAO,CAAC,EAAG,OACXjD,KAAM,SAIRiH,SAAU,QAEVL,MAAOlG,EAAO4uB,KAEdrlB,QAtCe,CACf,CAAG,kBAAsB,mBAAsB,mBAC/C,CAAG,mBAAsB,kBAAsB,oBAC/C,EAAG,oBAAsB,oBAAsB,qBAoC/CC,UAlCiB,CACjB,CAAG,oBAAuB,iBAAsB,oBAChD,EAAG,kBAAuB,mBAAsB,qBAChD,CAAG,qBAAuB,oBAAsB,sBCfjD,MAAM,GAAI,IAAM,GAIVslB,IAAoB,UAGpBC,IAAe1xB,KAAK2xB,KAAK,OAAS,MAAQ,MAEhD,IAAeC,GAAA,IAAI/lB,EAAc,CAChCrH,GAAI,SACJ0D,MAAO,WACPjG,KAAM,SASNoC,OAAQ,CACPyH,EAAG,CACF5G,MAAO,CAACusB,GAAkBC,IAC1BzvB,KAAM,OAEP8J,EAAG,CACF7G,MAAO,CAACusB,GAAkBC,IAC1BzvB,KAAM,SAEP+J,EAAG,CACF9G,MAAO,CAACusB,GAAkBC,IAC1BzvB,KAAM,SAGRiH,SAAU,QAEVvH,KAAM6vB,GAEN5oB,OAAQ+V,GAGAA,EAAIvgB,KAAI,SAAUkT,GACxB,OAAIA,IAHO,kBAIiC,GAAnC,IAAa,MAANA,EAAe,MAAQ,IAE9BA,EAAMogB,GACP,IAAa,MAANpgB,EAAe,MAGtB,KAEX,IAIC3I,SAAUgW,GACFA,EAAIvgB,KAAI,SAAUkT,GACxB,OAAIA,GAAO,GACFtR,KAAK2xB,KAAK,IAAK,MAAQ,MAEvBrgB,EAAM,IACLtR,KAAK2xB,KAAK,GAAU,GAANrgB,GAAa,MAAQ,OAGnCtR,KAAK2xB,KAAKrgB,GAAO,MAAQ,KAEtC,srBClEe,SAAmBwY,EAAYC,EAAY7qB,EAAI,CAAA,GACzDH,EAASG,KACZA,EAAI,CAAC2yB,UAAW3yB,IAGjB,IAAI2yB,UAACA,KAActG,GAAQrsB,EAE3B,IAAK2yB,EAAW,CACf,IAAIC,EAAa3yB,OAAOwI,KAAKoqB,IAAoB3zB,KAAI8O,GAAKA,EAAEjH,QAAQ,YAAa,MAAKmF,KAAK,MAC3F,MAAM,IAAIhI,UAAU,0EAA0E0uB,IAC9F,CAEDhI,EAAaxhB,EAASwhB,GACtBC,EAAazhB,EAASyhB,GAEtB,IAAK,IAAI7c,KAAK6kB,GACb,GAAI,WAAaF,EAAUryB,gBAAkB0N,EAAE1N,cAC9C,OAAOuyB,GAAmB7kB,GAAG4c,EAAYC,EAAYwB,GAIvD,MAAM,IAAInoB,UAAU,+BAA+ByuB,IACpD,8KClBO,SAAiBnsB,EAAOssB,EAAS,KAGvC,OAAOtlB,EAAIhH,EADK,CADJ0B,EAAWmB,IAAI,QAAS,OACZ,MACK0E,GAAKA,GAAK,EAAI+kB,IAC5C,+N9B4Be,SAAkBtsB,GAAOvB,MAACA,EAAQb,EAAS2lB,iBAAkB9lB,GAAW,IACtF,IAAItE,EAAMmf,GAAUtY,EAAOvC,GAE3B,GAAmB,oBAAR+lB,KAAuBA,IAAIC,SAAS,QAAStqB,KAASyE,EAAS2lB,cACzEpqB,EAAM,IAAI2G,OAAO3G,GACjBA,EAAI6G,MAAQA,MAER,CAEJ,IAAIusB,EAAgBvsB,EAKpB,IAFcA,EAAMrB,OAAO6tB,KAAKryB,IAAWA,EAAO6F,EAAMe,WAIjDuiB,KAAiBE,IAAIC,SAAS,QAAS,wBAE5C8I,EAAgBtgB,EAAMjM,GACtBusB,EAAc5tB,OAAS4tB,EAAc5tB,OAAOjG,IAAIqC,GAChDwxB,EAAcxrB,MAAQhG,EAASwxB,EAAcxrB,OAE7C5H,EAAMmf,GAAUiU,EAAe9uB,GAE3B+lB,IAAIC,SAAS,QAAStqB,IAIzB,OAFAA,EAAM,IAAI2G,OAAO3G,GACjBA,EAAI6G,MAAQusB,EACLpzB,EAOVozB,EAAgB5wB,GAAG4wB,EAAe9tB,GAClCtF,EAAM,IAAI2G,OAAOwY,GAAUiU,EAAe9uB,IAC1CtE,EAAI6G,MAAQusB,CACZ,CAED,OAAOpzB,CACR,oC+BhFe,SAAiBgT,EAAQC,GAIvC,OAHAD,EAASvJ,EAASuJ,GAClBC,EAASxJ,EAASwJ,GAEXD,EAAO1N,QAAU2N,EAAO3N,OACrB0N,EAAOpL,QAAUqL,EAAOrL,OACxBoL,EAAOxN,OAAOuF,OAAM,CAAC9K,EAAGL,IAAMK,IAAMgT,EAAOzN,OAAO5F,IAC7D,iJDNO,SAAkBiH,EAAOssB,EAAS,KAGxC,OAAOtlB,EAAIhH,EADK,CADJ0B,EAAWmB,IAAI,QAAS,OACZ,MACK0E,GAAKA,GAAK,EAAI+kB,IAC5C,cnBmBO,SAAc7f,EAAIC,EAAI9T,EAAI,GAAIY,EAAI,IAQxC,OAPCiT,EAAIC,GAAM,CAAC9J,EAAS6J,GAAK7J,EAAS8J,IAEnB,WAAZnT,EAAKX,MACPA,EAAGY,GAAK,CAAC,GAAIZ,IAGP4G,GAAMiN,EAAIC,EAAIlT,EACf4M,CAAExN,EACV,mJVvBO,SAAuBoH,EAAOpE,GAEpCoL,EAAIhH,EAAO,CAACgG,EAAS,KAAMpK,EAC5B,gBU6BO,SAAgB6Q,EAAIC,EAAIjP,EAAU,CAAA,GACxC,IAAIgvB,EAEA3G,GAAQrZ,MAEVggB,EAAYhvB,GAAW,CAACgP,EAAIC,IAC5BD,EAAIC,GAAM+f,EAAW1G,UAAUC,QAGjC,IAAI0G,UACHA,EAASzW,aAAEA,EAAY0W,MACvBA,EAAQ,EAACC,SAAEA,EAAW,OACnBC,GACApvB,EAECgvB,KACHhgB,EAAIC,GAAM,CAAC9J,EAAS6J,GAAK7J,EAAS8J,IACnC+f,EAAajtB,GAAMiN,EAAIC,EAAImgB,IAG5B,IAAIC,EAAahvB,GAAO2O,EAAIC,GACxBqgB,EAAcL,EAAY,EAAIpyB,KAAK8J,IAAIuoB,EAAOryB,KAAK0yB,KAAKF,EAAaJ,GAAa,GAAKC,EACvFxzB,EAAM,GAMV,QAJiB2J,IAAb8pB,IACHG,EAAczyB,KAAK6J,IAAI4oB,EAAaH,IAGjB,IAAhBG,EACH5zB,EAAM,CAAC,CAACP,EAAG,GAAIoH,MAAOysB,EAAW,UAE7B,CACJ,IAAIQ,EAAO,GAAKF,EAAc,GAC9B5zB,EAAMX,MAAMkD,KAAK,CAACnD,OAAQw0B,IAAc,CAACj0B,EAAGC,KAC3C,IAAIH,EAAIG,EAAIk0B,EACZ,MAAO,CAACr0B,IAAGoH,MAAOysB,EAAW7zB,GAAG,GAEjC,CAED,GAAI8zB,EAAY,EAAG,CAElB,IAAIQ,EAAW/zB,EAAIoT,QAAO,CAACC,EAAK2gB,EAAKp0B,KACpC,GAAU,IAANA,EACH,OAAO,EAGR,IAAI,EAAK+E,GAAOqvB,EAAIntB,MAAO7G,EAAIJ,EAAI,GAAGiH,MAAOiW,GAC7C,OAAO3b,KAAK8J,IAAIoI,EAAK,EAAG,GACtB,GAEH,KAAO0gB,EAAWR,GAAW,CAG5BQ,EAAW,EAEX,IAAK,IAAIn0B,EAAI,EAAIA,EAAII,EAAIZ,QAAYY,EAAIZ,OAASq0B,EAAW7zB,IAAK,CACjE,IAAIq0B,EAAOj0B,EAAIJ,EAAI,GACfo0B,EAAMh0B,EAAIJ,GAEVH,GAAKu0B,EAAIv0B,EAAIw0B,EAAKx0B,GAAK,EACvBoH,EAAQysB,EAAW7zB,GACvBs0B,EAAW5yB,KAAK8J,IAAI8oB,EAAUpvB,GAAOkC,EAAOotB,EAAKptB,OAAQlC,GAAOkC,EAAOmtB,EAAIntB,QAC3E7G,EAAIk0B,OAAOt0B,EAAG,EAAG,CAACH,IAAGoH,MAAOysB,EAAW7zB,KACvCG,GACA,CACD,CACD,CAID,OAFAI,EAAMA,EAAIT,KAAI8O,GAAKA,EAAExH,QAEd7G,CACR,kFFxGO,SAAa6G,GAEnB,IAAK2lB,EAAG1B,EAAG2B,GAAK/e,EAAO7G,EAAOgG,GACzBihB,EAAMtB,EAAI1B,EAAI2B,EACnB,MAAO,CAACD,EAAIsB,EAAKhD,EAAIgD,EACtB"}