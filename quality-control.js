const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');

// <PERSON>te kontrol konfigürasyonu
const QC_CONFIG = {
  MIN_FILE_SIZE: 50000, // 50KB minimum dosya boyutu
  MAX_FILE_SIZE: 10000000, // 10MB maksimum dosya boyutu
  REQUIRED_RESOLUTION: { width: 3000, height: 4000 },
  MIN_RARITY_SCORE: 0,
  MAX_RARITY_SCORE: 100,
  REQUIRED_ATTRIBUTES: [
    'Palette', 'Spiral Type', 'Effect', 'Ring Count', 
    'Stroke Weight', 'Wobble Intensity', 'Gradient Intensity', 'Rarity Score'
  ],
  DUPLICATE_THRESHOLD: 0.95 // Benzerlik eşiği
};

// Test sonuçları
class QualityReport {
  constructor() {
    this.totalFiles = 0;
    this.passedFiles = 0;
    this.failedFiles = 0;
    this.issues = [];
    this.duplicates = [];
    this.statistics = {};
  }
  
  addIssue(file, type, message) {
    this.issues.push({
      file: file,
      type: type,
      message: message,
      timestamp: new Date().toISOString()
    });
  }
  
  addDuplicate(file1, file2, similarity) {
    this.duplicates.push({
      file1: file1,
      file2: file2,
      similarity: similarity
    });
  }
  
  generateReport() {
    const successRate = ((this.passedFiles / this.totalFiles) * 100).toFixed(1);
    
    return {
      summary: {
        totalFiles: this.totalFiles,
        passedFiles: this.passedFiles,
        failedFiles: this.failedFiles,
        successRate: `${successRate}%`,
        totalIssues: this.issues.length,
        duplicatesFound: this.duplicates.length
      },
      issues: this.issues,
      duplicates: this.duplicates,
      statistics: this.statistics,
      generatedAt: new Date().toISOString()
    };
  }
}

// Dosya boyutu kontrolü
function checkFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const size = stats.size;
    
    if (size < QC_CONFIG.MIN_FILE_SIZE) {
      return { passed: false, message: `Dosya çok küçük: ${size} bytes (min: ${QC_CONFIG.MIN_FILE_SIZE})` };
    }
    
    if (size > QC_CONFIG.MAX_FILE_SIZE) {
      return { passed: false, message: `Dosya çok büyük: ${size} bytes (max: ${QC_CONFIG.MAX_FILE_SIZE})` };
    }
    
    return { passed: true, size: size };
  } catch (error) {
    return { passed: false, message: `Dosya okunamadı: ${error.message}` };
  }
}

// Görsel çözünürlük kontrolü
async function checkImageResolution(imagePath) {
  try {
    const image = await loadImage(imagePath);
    const { width, height } = image;
    
    if (width !== QC_CONFIG.REQUIRED_RESOLUTION.width || height !== QC_CONFIG.REQUIRED_RESOLUTION.height) {
      return { 
        passed: false, 
        message: `Yanlış çözünürlük: ${width}x${height} (beklenen: ${QC_CONFIG.REQUIRED_RESOLUTION.width}x${QC_CONFIG.REQUIRED_RESOLUTION.height})` 
      };
    }
    
    return { passed: true, resolution: { width, height } };
  } catch (error) {
    return { passed: false, message: `Görsel yüklenemedi: ${error.message}` };
  }
}

// Metadata doğrulama
function validateMetadata(metadataPath) {
  try {
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
    const issues = [];
    
    // Gerekli alanları kontrol et
    if (!metadata.name) issues.push('name alanı eksik');
    if (!metadata.description) issues.push('description alanı eksik');
    if (!metadata.image) issues.push('image alanı eksik');
    if (!metadata.attributes || !Array.isArray(metadata.attributes)) {
      issues.push('attributes alanı eksik veya geçersiz');
    } else {
      // Gerekli attribute'ları kontrol et
      const attributeTypes = metadata.attributes.map(attr => attr.trait_type);
      QC_CONFIG.REQUIRED_ATTRIBUTES.forEach(required => {
        if (!attributeTypes.includes(required)) {
          issues.push(`Gerekli attribute eksik: ${required}`);
        }
      });
      
      // Rarity score kontrolü
      const rarityAttr = metadata.attributes.find(attr => attr.trait_type === 'Rarity Score');
      if (rarityAttr) {
        const score = rarityAttr.value;
        if (score < QC_CONFIG.MIN_RARITY_SCORE || score > QC_CONFIG.MAX_RARITY_SCORE) {
          issues.push(`Geçersiz rarity score: ${score} (${QC_CONFIG.MIN_RARITY_SCORE}-${QC_CONFIG.MAX_RARITY_SCORE} arası olmalı)`);
        }
      }
    }
    
    if (issues.length > 0) {
      return { passed: false, message: issues.join(', ') };
    }
    
    return { passed: true, metadata: metadata };
  } catch (error) {
    return { passed: false, message: `Metadata okunamadı: ${error.message}` };
  }
}

// Görsel benzerlik kontrolü (basit histogram karşılaştırması)
async function calculateImageSimilarity(imagePath1, imagePath2) {
  try {
    const image1 = await loadImage(imagePath1);
    const image2 = await loadImage(imagePath2);
    
    // Küçük boyutlarda histogram oluştur (performans için)
    const canvas1 = createCanvas(100, 100);
    const ctx1 = canvas1.getContext('2d');
    ctx1.drawImage(image1, 0, 0, 100, 100);
    
    const canvas2 = createCanvas(100, 100);
    const ctx2 = canvas2.getContext('2d');
    ctx2.drawImage(image2, 0, 0, 100, 100);
    
    const imageData1 = ctx1.getImageData(0, 0, 100, 100);
    const imageData2 = ctx2.getImageData(0, 0, 100, 100);
    
    // Basit piksel karşılaştırması
    let totalDiff = 0;
    const totalPixels = imageData1.data.length / 4;
    
    for (let i = 0; i < imageData1.data.length; i += 4) {
      const r1 = imageData1.data[i];
      const g1 = imageData1.data[i + 1];
      const b1 = imageData1.data[i + 2];
      
      const r2 = imageData2.data[i];
      const g2 = imageData2.data[i + 1];
      const b2 = imageData2.data[i + 2];
      
      const diff = Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
      totalDiff += diff;
    }
    
    const avgDiff = totalDiff / (totalPixels * 3 * 255);
    const similarity = 1 - avgDiff;
    
    return similarity;
  } catch (error) {
    console.error(`Benzerlik hesaplama hatası: ${error.message}`);
    return 0;
  }
}

// Ana kalite kontrol fonksiyonu
async function runQualityControl(outputDir) {
  console.log('🔍 Kalite Kontrol Sistemi Başlatılıyor...\n');
  
  const report = new QualityReport();
  
  if (!fs.existsSync(outputDir)) {
    console.error(`❌ Çıktı klasörü bulunamadı: ${outputDir}`);
    return;
  }
  
  // Tüm dosyaları listele
  const files = fs.readdirSync(outputDir);
  const imageFiles = files.filter(file => file.endsWith('.png'));
  const jsonFiles = files.filter(file => file.endsWith('.json') && !file.includes('rarity-report'));
  
  report.totalFiles = imageFiles.length;
  
  console.log(`📊 Toplam ${imageFiles.length} NFT bulundu`);
  console.log(`📄 ${jsonFiles.length} metadata dosyası bulundu\n`);
  
  // Her NFT için kontroller
  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    const baseName = imageFile.replace('.png', '');
    const jsonFile = baseName + '.json';
    
    console.log(`🔍 Kontrol ediliyor: ${baseName} (${i + 1}/${imageFiles.length})`);
    
    let passed = true;
    const imagePath = path.join(outputDir, imageFile);
    const jsonPath = path.join(outputDir, jsonFile);
    
    // 1. Dosya boyutu kontrolü
    const sizeCheck = checkFileSize(imagePath);
    if (!sizeCheck.passed) {
      report.addIssue(imageFile, 'FILE_SIZE', sizeCheck.message);
      passed = false;
    }
    
    // 2. Görsel çözünürlük kontrolü
    const resolutionCheck = await checkImageResolution(imagePath);
    if (!resolutionCheck.passed) {
      report.addIssue(imageFile, 'RESOLUTION', resolutionCheck.message);
      passed = false;
    }
    
    // 3. Metadata kontrolü
    if (fs.existsSync(jsonPath)) {
      const metadataCheck = validateMetadata(jsonPath);
      if (!metadataCheck.passed) {
        report.addIssue(jsonFile, 'METADATA', metadataCheck.message);
        passed = false;
      }
    } else {
      report.addIssue(jsonFile, 'MISSING_FILE', 'Metadata dosyası bulunamadı');
      passed = false;
    }
    
    if (passed) {
      report.passedFiles++;
    } else {
      report.failedFiles++;
    }
  }
  
  // 4. Duplikasyon kontrolü (sadece geçen dosyalar için)
  console.log('\n🔍 Duplikasyon kontrolü yapılıyor...');
  const passedImages = imageFiles.filter(file => {
    const hasIssues = report.issues.some(issue => issue.file === file);
    return !hasIssues;
  });
  
  for (let i = 0; i < passedImages.length; i++) {
    for (let j = i + 1; j < passedImages.length; j++) {
      const similarity = await calculateImageSimilarity(
        path.join(outputDir, passedImages[i]),
        path.join(outputDir, passedImages[j])
      );
      
      if (similarity > QC_CONFIG.DUPLICATE_THRESHOLD) {
        report.addDuplicate(passedImages[i], passedImages[j], similarity);
      }
    }
    
    if (i % 10 === 0) {
      console.log(`   İlerleme: ${i}/${passedImages.length} dosya kontrol edildi`);
    }
  }
  
  // 5. İstatistikler
  const metadataStats = {};
  jsonFiles.forEach(file => {
    try {
      const metadata = JSON.parse(fs.readFileSync(path.join(outputDir, file), 'utf8'));
      metadata.attributes.forEach(attr => {
        if (!metadataStats[attr.trait_type]) {
          metadataStats[attr.trait_type] = {};
        }
        if (!metadataStats[attr.trait_type][attr.value]) {
          metadataStats[attr.trait_type][attr.value] = 0;
        }
        metadataStats[attr.trait_type][attr.value]++;
      });
    } catch (error) {
      // Hatalı metadata dosyaları zaten raporlandı
    }
  });
  
  report.statistics = metadataStats;
  
  // Rapor oluştur
  const finalReport = report.generateReport();
  
  // Raporu kaydet
  const reportPath = path.join(outputDir, 'quality-control-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(finalReport, null, 2));
  
  // Konsol çıktısı
  console.log('\n' + '='.repeat(60));
  console.log('📋 KALİTE KONTROL RAPORU');
  console.log('='.repeat(60));
  console.log(`📊 Toplam Dosya: ${finalReport.summary.totalFiles}`);
  console.log(`✅ Geçen Dosya: ${finalReport.summary.passedFiles}`);
  console.log(`❌ Başarısız Dosya: ${finalReport.summary.failedFiles}`);
  console.log(`📈 Başarı Oranı: ${finalReport.summary.successRate}`);
  console.log(`⚠️  Toplam Sorun: ${finalReport.summary.totalIssues}`);
  console.log(`🔄 Duplikasyon: ${finalReport.summary.duplicatesFound}`);
  
  if (finalReport.issues.length > 0) {
    console.log('\n❌ BULUNAN SORUNLAR:');
    finalReport.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.file}: ${issue.message}`);
    });
  }
  
  if (finalReport.duplicates.length > 0) {
    console.log('\n🔄 DUPLIKASYONLAR:');
    finalReport.duplicates.forEach((dup, index) => {
      console.log(`${index + 1}. ${dup.file1} ↔ ${dup.file2} (${(dup.similarity * 100).toFixed(1)}% benzer)`);
    });
  }
  
  console.log(`\n📄 Detaylı rapor: ${reportPath}`);
  console.log('='.repeat(60));
  
  return finalReport;
}

// CLI kullanımı
if (require.main === module) {
  const outputDir = process.argv[2] || path.join(__dirname, 'output');
  runQualityControl(outputDir).catch(console.error);
}

module.exports = { runQualityControl, QC_CONFIG };
