import { BezierSegment } from "./BezierSegment";
import { Point } from "./types";
export declare class BezierPath {
    segments: BezierSegment[];
    private _totalLength;
    samples: {
        dist: number;
        pt: Point;
        tan: Point;
        segIdx: number;
        t: number;
    }[];
    constructor(segments: BezierSegment[]);
    segmentStartEnds: {
        start: number;
        end: number;
    }[];
    private _jumps;
    jumps(): number[];
    getTotalLength(): number;
    private findClosestSampleIdx;
    getPointAtLength(length: number, approximate?: boolean): Point;
    getAngleAtLength(length: number, approximate?: boolean): number;
    getTangentAtLength(length: number, approximate?: boolean): Point;
}
