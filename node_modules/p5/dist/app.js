import { p as p5 } from './main-rEhlsQtb.js';
import shape from './shape/index.js';
import accessibility from './accessibility/index.js';
import color from './color/index.js';
import friendlyErrors from './core/friendly_errors/index.js';
import data from './data/index.js';
import dom from './dom/index.js';
import events from './events/index.js';
import image from './image/index.js';
import io from './io/index.js';
import math from './math/index.js';
import utilities from './utilities/index.js';
import webgl from './webgl/index.js';
import type from './type/index.js';
import { waitForDocumentReady, waitingForTranslator, _globalInit } from './core/init.js';
import './constants-C2DVjshm.js';
import './core/transform.js';
import './core/structure.js';
import './core/environment.js';
import './math/p5.Vector.js';
import './rendering-CpHn8PfG.js';
import './creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import './color/color_spaces/hsb.js';
import './dom/p5.Element.js';
import './dom/p5.File.js';
import './io/p5.XML.js';
import './p5.Renderer-DO9wIL55.js';
import './image/filters.js';
import './shape/custom_shapes.js';
import './core/States.js';
import './io/utilities.js';
import 'file-saver';
import './dom/p5.MediaElement.js';
import './shape/2d_primitives.js';
import './core/helpers.js';
import './shape/attributes.js';
import './shape/curves.js';
import './shape/vertex.js';
import './color/setting.js';
import 'omggif';
import './io/csv.js';
import 'gifenc';
import './image/pixels.js';
import './webgl/GeometryBuilder.js';
import './math/p5.Matrix.js';
import './math/Matrices/Matrix.js';
import './math/Matrices/MatrixInterface.js';
import './webgl/p5.Geometry.js';
import './webgl/p5.DataArray.js';
import './webgl/p5.Quat.js';
import './webgl/p5.RenderBuffer.js';
import './webgl/ShapeBuilder.js';
import 'libtess';
import './webgl/GeometryBufferCache.js';
import './image/const.js';
import './math/trigonometry.js';
import './image/filterRenderer2D.js';
import './accessibility/describe.js';
import './accessibility/gridOutput.js';
import './accessibility/textOutput.js';
import './accessibility/outputs.js';
import './accessibility/color_namer.js';
import './color/color_conversion.js';
import './core/friendly_errors/fes_core.js';
import './core/internationalization.js';
import 'i18next';
import 'i18next-browser-languagedetector';
import './core/friendly_errors/browser_errors.js';
import './core/friendly_errors/stacktrace.js';
import './core/friendly_errors/param_validator.js';
import 'zod/v4';
import './core/friendly_errors/sketch_verifier.js';
import 'acorn';
import 'acorn-walk';
import './core/friendly_errors/file_errors.js';
import './data/local_storage.js';
import './dom/dom.js';
import './events/acceleration.js';
import './events/keyboard.js';
import './events/pointer.js';
import './io/p5.Table.js';
import './io/p5.TableRow.js';
import './math/calculation.js';
import './math/noise.js';
import './math/random.js';
import './math/math.js';
import './utilities/conversion.js';
import './utilities/utility_functions.js';
import './utilities/time_date.js';
import './webgl/interaction.js';
import './webgl/loading.js';
import './webgl/text.js';
import './type/p5.Font.js';
import './type/textCore.js';
import '@japont/unicode-range';
import './type/unicodeRanges.js';
import './type/lib/Typr.js';
import 'pako';
import '@davepagurek/bezier-path';
import './webgl/ShaderGenerator.js';
import 'escodegen';

// core
shape(p5);
accessibility(p5);
color(p5);
friendlyErrors(p5);
data(p5);
dom(p5);
events(p5);
image(p5);
io(p5);
math(p5);
utilities(p5);
webgl(p5);
type(p5);
Promise.all([waitForDocumentReady(), waitingForTranslator]).then(_globalInit);

export { p5 as default };
