const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');

// Comprehensive Artistic Quality Assessment System
class ArtisticQualityAssessment {
  constructor() {
    this.artisticStandards = this.defineArtisticStandards();
    this.assessmentCriteria = this.defineAssessmentCriteria();
  }

  // Define comprehensive artistic standards
  defineArtisticStandards() {
    return {
      composition: {
        ruleOfThirds: { weight: 0.25, threshold: 0.6 },
        goldenRatio: { weight: 0.25, threshold: 0.5 },
        balance: { weight: 0.25, threshold: 0.7 },
        focusPoint: { weight: 0.25, threshold: 0.6 }
      },
      colorHarmony: {
        complementary: { weight: 0.3, threshold: 0.6 },
        analogous: { weight: 0.3, threshold: 0.7 },
        triadic: { weight: 0.2, threshold: 0.5 },
        temperature: { weight: 0.2, threshold: 0.6 }
      },
      visualFlow: {
        eyeMovement: { weight: 0.4, threshold: 0.6 },
        rhythm: { weight: 0.3, threshold: 0.5 },
        continuity: { weight: 0.3, threshold: 0.7 }
      },
      mathematicalBeauty: {
        fi<PERSON><PERSON><PERSON>: { weight: 0.3, threshold: 0.6 },
        symmetry: { weight: 0.3, threshold: 0.5 },
        proportion: { weight: 0.4, threshold: 0.7 }
      },
      emotionalImpact: {
        contrast: { weight: 0.3, threshold: 0.6 },
        mood: { weight: 0.4, threshold: 0.5 },
        energy: { weight: 0.3, threshold: 0.6 }
      }
    };
  }

  // Define assessment criteria
  defineAssessmentCriteria() {
    return {
      excellent: { min: 0.85, description: 'Exceptional artistic quality' },
      good: { min: 0.70, description: 'High artistic quality' },
      acceptable: { min: 0.55, description: 'Acceptable artistic quality' },
      needsImprovement: { min: 0.40, description: 'Needs artistic improvement' },
      poor: { min: 0.0, description: 'Poor artistic quality' }
    };
  }

  // Main artistic assessment function
  async assessArtisticQuality(imagePath) {
    console.log(`🎨 Artistic quality assessment: ${path.basename(imagePath)}`);

    const image = await loadImage(imagePath);
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0);
    
    const imageData = ctx.getImageData(0, 0, image.width, image.height);
    
    const assessment = {
      composition: await this.assessComposition(imageData),
      colorHarmony: await this.assessColorHarmony(imageData),
      visualFlow: await this.assessVisualFlow(imageData),
      mathematicalBeauty: await this.assessMathematicalBeauty(imageData),
      emotionalImpact: await this.assessEmotionalImpact(imageData)
    };

    const overallScore = this.calculateOverallArtisticScore(assessment);
    const qualityLevel = this.determineArtisticLevel(overallScore);
    
    return {
      overallScore: overallScore,
      qualityLevel: qualityLevel,
      detailedAssessment: assessment,
      recommendations: this.generateArtisticRecommendations(assessment),
      timestamp: new Date().toISOString()
    };
  }

  // Assess composition using multiple principles
  async assessComposition(imageData) {
    const ruleOfThirdsScore = this.assessRuleOfThirds(imageData);
    const goldenRatioScore = this.assessGoldenRatio(imageData);
    const balanceScore = this.assessBalance(imageData);
    const focusPointScore = this.assessFocusPoint(imageData);

    const weights = this.artisticStandards.composition;
    const compositionScore = (
      ruleOfThirdsScore * weights.ruleOfThirds.weight +
      goldenRatioScore * weights.goldenRatio.weight +
      balanceScore * weights.balance.weight +
      focusPointScore * weights.focusPoint.weight
    );

    return {
      ruleOfThirds: ruleOfThirdsScore,
      goldenRatio: goldenRatioScore,
      balance: balanceScore,
      focusPoint: focusPointScore,
      overallScore: compositionScore,
      analysis: this.analyzeCompositionStrengths(ruleOfThirdsScore, goldenRatioScore, balanceScore, focusPointScore)
    };
  }

  // Rule of thirds assessment
  assessRuleOfThirds(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;

    // Define thirds lines
    const verticalLines = [width / 3, (2 * width) / 3];
    const horizontalLines = [height / 3, (2 * height) / 3];
    
    // Calculate interest points near intersection lines
    let interestScore = 0;
    const tolerance = Math.min(width, height) * 0.05; // 5% tolerance

    for (const vLine of verticalLines) {
      for (const hLine of horizontalLines) {
        const regionInterest = this.calculateRegionInterest(
          imageData, 
          Math.max(0, vLine - tolerance), 
          Math.min(width, vLine + tolerance),
          Math.max(0, hLine - tolerance), 
          Math.min(height, hLine + tolerance)
        );
        interestScore += regionInterest;
      }
    }

    return Math.min(interestScore / 4, 1); // Normalize to 0-1
  }

  // Golden ratio assessment
  assessGoldenRatio(imageData) {
    const phi = (1 + Math.sqrt(5)) / 2; // Golden ratio
    const width = imageData.width;
    const height = imageData.height;

    // Check if dimensions follow golden ratio
    const aspectRatio = width / height;
    const goldenRatioDeviation = Math.abs(aspectRatio - phi) / phi;
    const aspectScore = Math.max(0, 1 - goldenRatioDeviation);

    // Check golden spiral positioning
    const spiralScore = this.assessGoldenSpiral(imageData);

    return (aspectScore + spiralScore) / 2;
  }

  // Assess golden spiral composition
  assessGoldenSpiral(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const centerX = width / 2;
    const centerY = height / 2;

    // Generate golden spiral points
    const spiralPoints = this.generateGoldenSpiralPoints(centerX, centerY, Math.min(width, height) / 4);
    
    let spiralInterest = 0;
    for (const point of spiralPoints) {
      if (point.x >= 0 && point.x < width && point.y >= 0 && point.y < height) {
        const interest = this.getPixelInterest(imageData, Math.floor(point.x), Math.floor(point.y));
        spiralInterest += interest;
      }
    }

    return spiralInterest / spiralPoints.length;
  }

  // Generate golden spiral points
  generateGoldenSpiralPoints(centerX, centerY, radius) {
    const points = [];
    const phi = (1 + Math.sqrt(5)) / 2;
    
    for (let i = 0; i < 100; i++) {
      const angle = i * 0.1;
      const r = radius * Math.pow(phi, angle / (Math.PI / 2));
      const x = centerX + r * Math.cos(angle);
      const y = centerY + r * Math.sin(angle);
      points.push({ x, y });
    }
    
    return points;
  }

  // Assess visual balance
  assessBalance(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    
    // Divide image into quadrants and calculate visual weight
    const quadrants = [
      { x: 0, y: 0, w: width/2, h: height/2 },
      { x: width/2, y: 0, w: width/2, h: height/2 },
      { x: 0, y: height/2, w: width/2, h: height/2 },
      { x: width/2, y: height/2, w: width/2, h: height/2 }
    ];

    const weights = quadrants.map(quad => 
      this.calculateVisualWeight(imageData, quad.x, quad.y, quad.w, quad.h)
    );

    // Calculate balance scores
    const horizontalBalance = 1 - Math.abs((weights[0] + weights[2]) - (weights[1] + weights[3])) / 2;
    const verticalBalance = 1 - Math.abs((weights[0] + weights[1]) - (weights[2] + weights[3])) / 2;
    const diagonalBalance = 1 - Math.abs((weights[0] + weights[3]) - (weights[1] + weights[2])) / 2;

    return (horizontalBalance + verticalBalance + diagonalBalance) / 3;
  }

  // Calculate visual weight of a region
  calculateVisualWeight(imageData, startX, startY, width, height) {
    const pixels = imageData.data;
    const imageWidth = imageData.width;
    
    let totalWeight = 0;
    let pixelCount = 0;

    for (let y = startY; y < startY + height && y < imageData.height; y++) {
      for (let x = startX; x < startX + width && x < imageWidth; x++) {
        const idx = (y * imageWidth + x) * 4;
        const r = pixels[idx];
        const g = pixels[idx + 1];
        const b = pixels[idx + 2];
        
        // Calculate visual weight based on brightness and saturation
        const brightness = (r + g + b) / (3 * 255);
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const saturation = max === 0 ? 0 : (max - min) / max;
        
        // Darker and more saturated colors have more visual weight
        const weight = (1 - brightness) * 0.7 + saturation * 0.3;
        totalWeight += weight;
        pixelCount++;
      }
    }

    return pixelCount > 0 ? totalWeight / pixelCount : 0;
  }

  // Assess focus point
  assessFocusPoint(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    
    // Find the most interesting region (highest contrast/detail)
    const regionSize = Math.min(width, height) / 10;
    let maxInterest = 0;
    let focusX = width / 2;
    let focusY = height / 2;

    for (let y = regionSize; y < height - regionSize; y += regionSize / 2) {
      for (let x = regionSize; x < width - regionSize; x += regionSize / 2) {
        const interest = this.calculateRegionInterest(
          imageData, x - regionSize/2, x + regionSize/2, y - regionSize/2, y + regionSize/2
        );
        
        if (interest > maxInterest) {
          maxInterest = interest;
          focusX = x;
          focusY = y;
        }
      }
    }

    // Score based on focus point position (center is not always best)
    const centerDistance = Math.sqrt(Math.pow(focusX - width/2, 2) + Math.pow(focusY - height/2, 2));
    const maxDistance = Math.sqrt(Math.pow(width/2, 2) + Math.pow(height/2, 2));
    const positionScore = 1 - (centerDistance / maxDistance) * 0.5; // Slight preference for off-center

    return Math.min(maxInterest * positionScore, 1);
  }

  // Calculate region interest (contrast/detail)
  calculateRegionInterest(imageData, startX, endX, startY, endY) {
    const pixels = imageData.data;
    const width = imageData.width;
    
    let totalContrast = 0;
    let comparisons = 0;

    for (let y = Math.max(0, startY); y < Math.min(imageData.height - 1, endY); y++) {
      for (let x = Math.max(0, startX); x < Math.min(width - 1, endX); x++) {
        const currentIdx = (y * width + x) * 4;
        const rightIdx = (y * width + x + 1) * 4;
        const downIdx = ((y + 1) * width + x) * 4;
        
        // Calculate contrast with neighboring pixels
        const currentBrightness = (pixels[currentIdx] + pixels[currentIdx + 1] + pixels[currentIdx + 2]) / 3;
        const rightBrightness = (pixels[rightIdx] + pixels[rightIdx + 1] + pixels[rightIdx + 2]) / 3;
        const downBrightness = (pixels[downIdx] + pixels[downIdx + 1] + pixels[downIdx + 2]) / 3;
        
        totalContrast += Math.abs(currentBrightness - rightBrightness) / 255;
        totalContrast += Math.abs(currentBrightness - downBrightness) / 255;
        comparisons += 2;
      }
    }

    return comparisons > 0 ? totalContrast / comparisons : 0;
  }

  // Get pixel interest level
  getPixelInterest(imageData, x, y) {
    const idx = (y * imageData.width + x) * 4;
    const pixels = imageData.data;
    
    const r = pixels[idx];
    const g = pixels[idx + 1];
    const b = pixels[idx + 2];
    
    // Interest based on saturation and contrast
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const saturation = max === 0 ? 0 : (max - min) / max;
    const brightness = (r + g + b) / (3 * 255);
    
    return saturation * 0.7 + (1 - Math.abs(brightness - 0.5) * 2) * 0.3;
  }

  // Assess color harmony
  async assessColorHarmony(imageData) {
    const colorPalette = this.extractColorPalette(imageData);
    
    const complementaryScore = this.assessComplementaryColors(colorPalette);
    const analogousScore = this.assessAnalogousColors(colorPalette);
    const triadicScore = this.assessTriadicColors(colorPalette);
    const temperatureScore = this.assessColorTemperature(colorPalette);

    const weights = this.artisticStandards.colorHarmony;
    const harmonyScore = (
      complementaryScore * weights.complementary.weight +
      analogousScore * weights.analogous.weight +
      triadicScore * weights.triadic.weight +
      temperatureScore * weights.temperature.weight
    );

    return {
      complementary: complementaryScore,
      analogous: analogousScore,
      triadic: triadicScore,
      temperature: temperatureScore,
      overallScore: harmonyScore,
      palette: colorPalette
    };
  }

  // Extract dominant color palette
  extractColorPalette(imageData) {
    const pixels = imageData.data;
    const colorCounts = new Map();
    
    // Sample every 10th pixel for performance
    for (let i = 0; i < pixels.length; i += 40) { // 10 pixels * 4 channels
      const r = Math.floor(pixels[i] / 32) * 32; // Quantize to reduce color space
      const g = Math.floor(pixels[i + 1] / 32) * 32;
      const b = Math.floor(pixels[i + 2] / 32) * 32;
      
      const colorKey = `${r},${g},${b}`;
      colorCounts.set(colorKey, (colorCounts.get(colorKey) || 0) + 1);
    }
    
    // Get top 10 colors
    const sortedColors = Array.from(colorCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([color, count]) => {
        const [r, g, b] = color.split(',').map(Number);
        return { r, g, b, count, hue: this.rgbToHue(r, g, b) };
      });
    
    return sortedColors;
  }

  // Convert RGB to hue
  rgbToHue(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;
    
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const diff = max - min;
    
    if (diff === 0) return 0;
    
    let hue;
    switch (max) {
      case r: hue = (g - b) / diff + (g < b ? 6 : 0); break;
      case g: hue = (b - r) / diff + 2; break;
      case b: hue = (r - g) / diff + 4; break;
    }
    
    return (hue / 6) * 360;
  }

  // Assess complementary color harmony
  assessComplementaryColors(palette) {
    let complementaryScore = 0;
    let pairs = 0;
    
    for (let i = 0; i < palette.length; i++) {
      for (let j = i + 1; j < palette.length; j++) {
        const hue1 = palette[i].hue;
        const hue2 = palette[j].hue;
        const hueDiff = Math.abs(hue1 - hue2);
        const complementaryDiff = Math.min(hueDiff, 360 - hueDiff);
        
        // Complementary colors are ~180 degrees apart
        if (Math.abs(complementaryDiff - 180) < 30) {
          const weight = (palette[i].count + palette[j].count) / 2;
          complementaryScore += weight * (1 - Math.abs(complementaryDiff - 180) / 30);
          pairs++;
        }
      }
    }
    
    return pairs > 0 ? complementaryScore / pairs / 1000 : 0; // Normalize
  }

  // Assess analogous color harmony
  assessAnalogousColors(palette) {
    let analogousScore = 0;
    let groups = 0;
    
    for (let i = 0; i < palette.length; i++) {
      for (let j = i + 1; j < palette.length; j++) {
        const hue1 = palette[i].hue;
        const hue2 = palette[j].hue;
        const hueDiff = Math.min(Math.abs(hue1 - hue2), 360 - Math.abs(hue1 - hue2));
        
        // Analogous colors are within 30 degrees
        if (hueDiff <= 30) {
          const weight = (palette[i].count + palette[j].count) / 2;
          analogousScore += weight * (1 - hueDiff / 30);
          groups++;
        }
      }
    }
    
    return groups > 0 ? analogousScore / groups / 1000 : 0; // Normalize
  }

  // Assess triadic color harmony
  assessTriadicColors(palette) {
    let triadicScore = 0;
    let triads = 0;
    
    for (let i = 0; i < palette.length; i++) {
      for (let j = i + 1; j < palette.length; j++) {
        for (let k = j + 1; k < palette.length; k++) {
          const hues = [palette[i].hue, palette[j].hue, palette[k].hue].sort((a, b) => a - b);
          
          // Check if colors form a triad (120 degrees apart)
          const diff1 = hues[1] - hues[0];
          const diff2 = hues[2] - hues[1];
          const diff3 = 360 - (hues[2] - hues[0]);
          
          const triadicDeviation = Math.abs(diff1 - 120) + Math.abs(diff2 - 120) + Math.abs(diff3 - 120);
          
          if (triadicDeviation < 60) { // Allow some tolerance
            const weight = (palette[i].count + palette[j].count + palette[k].count) / 3;
            triadicScore += weight * (1 - triadicDeviation / 60);
            triads++;
          }
        }
      }
    }
    
    return triads > 0 ? triadicScore / triads / 1000 : 0; // Normalize
  }

  // Assess color temperature harmony
  assessColorTemperature(palette) {
    let warmColors = 0;
    let coolColors = 0;
    let totalWeight = 0;
    
    palette.forEach(color => {
      const hue = color.hue;
      const weight = color.count;
      totalWeight += weight;
      
      // Warm colors: red, orange, yellow (0-60, 300-360)
      // Cool colors: blue, green, purple (120-240)
      if ((hue >= 0 && hue <= 60) || (hue >= 300 && hue <= 360)) {
        warmColors += weight;
      } else if (hue >= 120 && hue <= 240) {
        coolColors += weight;
      }
    });
    
    if (totalWeight === 0) return 0;
    
    const warmRatio = warmColors / totalWeight;
    const coolRatio = coolColors / totalWeight;
    
    // Score based on temperature consistency or intentional contrast
    const temperatureBalance = Math.abs(warmRatio - coolRatio);
    const temperatureConsistency = Math.max(warmRatio, coolRatio);
    
    // Prefer either consistent temperature or balanced contrast
    return Math.max(temperatureConsistency, 1 - temperatureBalance);
  }

  // Assess visual flow
  async assessVisualFlow(imageData) {
    const eyeMovementScore = this.assessEyeMovement(imageData);
    const rhythmScore = this.assessRhythm(imageData);
    const continuityScore = this.assessContinuity(imageData);

    const weights = this.artisticStandards.visualFlow;
    const flowScore = (
      eyeMovementScore * weights.eyeMovement.weight +
      rhythmScore * weights.rhythm.weight +
      continuityScore * weights.continuity.weight
    );

    return {
      eyeMovement: eyeMovementScore,
      rhythm: rhythmScore,
      continuity: continuityScore,
      overallScore: flowScore
    };
  }

  // Assess eye movement patterns
  assessEyeMovement(imageData) {
    // Simplified eye movement assessment based on contrast paths
    const width = imageData.width;
    const height = imageData.height;
    const contrastMap = this.generateContrastMap(imageData);
    
    // Find paths of high contrast that guide the eye
    let pathScore = 0;
    const numPaths = 10;
    
    for (let i = 0; i < numPaths; i++) {
      const startX = Math.floor(Math.random() * width);
      const startY = Math.floor(Math.random() * height);
      
      const pathLength = this.traceContrastPath(contrastMap, startX, startY, width, height);
      pathScore += pathLength / Math.min(width, height);
    }
    
    return Math.min(pathScore / numPaths, 1);
  }

  // Generate contrast map
  generateContrastMap(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    const contrastMap = new Array(width * height);
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = y * width + x;
        const pixelIdx = idx * 4;
        
        const current = (pixels[pixelIdx] + pixels[pixelIdx + 1] + pixels[pixelIdx + 2]) / 3;
        
        // Calculate contrast with neighbors
        let maxContrast = 0;
        for (let dy = -1; dy <= 1; dy++) {
          for (let dx = -1; dx <= 1; dx++) {
            if (dx === 0 && dy === 0) continue;
            
            const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
            const neighbor = (pixels[neighborIdx] + pixels[neighborIdx + 1] + pixels[neighborIdx + 2]) / 3;
            const contrast = Math.abs(current - neighbor) / 255;
            maxContrast = Math.max(maxContrast, contrast);
          }
        }
        
        contrastMap[idx] = maxContrast;
      }
    }
    
    return contrastMap;
  }

  // Trace contrast path for eye movement
  traceContrastPath(contrastMap, startX, startY, width, height) {
    let currentX = startX;
    let currentY = startY;
    let pathLength = 0;
    const visited = new Set();
    const maxSteps = 50;
    
    for (let step = 0; step < maxSteps; step++) {
      const key = `${currentX},${currentY}`;
      if (visited.has(key)) break;
      visited.add(key);
      
      // Find direction with highest contrast
      let bestX = currentX;
      let bestY = currentY;
      let bestContrast = 0;
      
      for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
          const newX = currentX + dx;
          const newY = currentY + dy;
          
          if (newX >= 0 && newX < width && newY >= 0 && newY < height) {
            const contrast = contrastMap[newY * width + newX];
            if (contrast > bestContrast) {
              bestContrast = contrast;
              bestX = newX;
              bestY = newY;
            }
          }
        }
      }
      
      if (bestX === currentX && bestY === currentY) break; // No improvement
      
      pathLength += Math.sqrt(Math.pow(bestX - currentX, 2) + Math.pow(bestY - currentY, 2));
      currentX = bestX;
      currentY = bestY;
    }
    
    return pathLength;
  }

  // Assess rhythm in the composition
  assessRhythm(imageData) {
    // Analyze repetitive patterns and spacing
    const width = imageData.width;
    const height = imageData.height;
    
    // Check for rhythmic patterns in both directions
    const horizontalRhythm = this.analyzeRhythmDirection(imageData, 'horizontal');
    const verticalRhythm = this.analyzeRhythmDirection(imageData, 'vertical');
    
    return (horizontalRhythm + verticalRhythm) / 2;
  }

  // Analyze rhythm in specific direction
  analyzeRhythmDirection(imageData, direction) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    
    const isHorizontal = direction === 'horizontal';
    const primaryDim = isHorizontal ? width : height;
    const secondaryDim = isHorizontal ? height : width;
    
    let rhythmScore = 0;
    const sampleLines = 10;
    
    for (let line = 0; line < sampleLines; line++) {
      const linePos = Math.floor((line / sampleLines) * secondaryDim);
      const intensities = [];
      
      for (let pos = 0; pos < primaryDim; pos++) {
        const x = isHorizontal ? pos : linePos;
        const y = isHorizontal ? linePos : pos;
        const idx = (y * width + x) * 4;
        const intensity = (pixels[idx] + pixels[idx + 1] + pixels[idx + 2]) / (3 * 255);
        intensities.push(intensity);
      }
      
      // Analyze periodicity using autocorrelation
      const periodicity = this.calculatePeriodicity(intensities);
      rhythmScore += periodicity;
    }
    
    return rhythmScore / sampleLines;
  }

  // Calculate periodicity using simplified autocorrelation
  calculatePeriodicity(signal) {
    const length = signal.length;
    let maxCorrelation = 0;
    
    // Check for periods between 5% and 25% of signal length
    const minPeriod = Math.floor(length * 0.05);
    const maxPeriod = Math.floor(length * 0.25);
    
    for (let period = minPeriod; period <= maxPeriod; period++) {
      let correlation = 0;
      let count = 0;
      
      for (let i = 0; i < length - period; i++) {
        correlation += signal[i] * signal[i + period];
        count++;
      }
      
      if (count > 0) {
        correlation /= count;
        maxCorrelation = Math.max(maxCorrelation, correlation);
      }
    }
    
    return maxCorrelation;
  }

  // Assess continuity
  assessContinuity(imageData) {
    // Measure how well elements connect and flow together
    const edgeMap = this.generateEdgeMap(imageData);
    const continuityScore = this.analyzeContinuity(edgeMap, imageData.width, imageData.height);
    
    return continuityScore;
  }

  // Generate edge map using Sobel operator
  generateEdgeMap(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    const edgeMap = new Array(width * height);
    
    // Sobel kernels
    const sobelX = [[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]];
    const sobelY = [[-1, -2, -1], [0, 0, 0], [1, 2, 1]];
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let gx = 0, gy = 0;
        
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const pixelIdx = ((y + ky) * width + (x + kx)) * 4;
            const intensity = (pixels[pixelIdx] + pixels[pixelIdx + 1] + pixels[pixelIdx + 2]) / 3;
            
            gx += intensity * sobelX[ky + 1][kx + 1];
            gy += intensity * sobelY[ky + 1][kx + 1];
          }
        }
        
        const magnitude = Math.sqrt(gx * gx + gy * gy) / 255;
        edgeMap[y * width + x] = magnitude;
      }
    }
    
    return edgeMap;
  }

  // Analyze continuity from edge map
  analyzeContinuity(edgeMap, width, height) {
    // Find connected edge components
    const visited = new Array(width * height).fill(false);
    const components = [];
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = y * width + x;
        if (!visited[idx] && edgeMap[idx] > 0.1) { // Edge threshold
          const component = this.floodFillEdges(edgeMap, visited, x, y, width, height);
          if (component.length > 10) { // Minimum component size
            components.push(component);
          }
        }
      }
    }
    
    // Score based on component sizes and connectivity
    let continuityScore = 0;
    const totalPixels = width * height;
    
    components.forEach(component => {
      const size = component.length;
      const connectivity = this.calculateComponentConnectivity(component);
      continuityScore += (size / totalPixels) * connectivity;
    });
    
    return Math.min(continuityScore * 10, 1); // Normalize
  }

  // Flood fill to find connected edge components
  floodFillEdges(edgeMap, visited, startX, startY, width, height) {
    const component = [];
    const stack = [{ x: startX, y: startY }];
    
    while (stack.length > 0) {
      const { x, y } = stack.pop();
      const idx = y * width + x;
      
      if (x < 0 || x >= width || y < 0 || y >= height || visited[idx] || edgeMap[idx] <= 0.1) {
        continue;
      }
      
      visited[idx] = true;
      component.push({ x, y });
      
      // Add neighbors
      for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
          if (dx !== 0 || dy !== 0) {
            stack.push({ x: x + dx, y: y + dy });
          }
        }
      }
    }
    
    return component;
  }

  // Calculate connectivity of edge component
  calculateComponentConnectivity(component) {
    if (component.length < 2) return 0;
    
    // Calculate average distance between consecutive points
    let totalDistance = 0;
    for (let i = 1; i < component.length; i++) {
      const dx = component[i].x - component[i-1].x;
      const dy = component[i].y - component[i-1].y;
      totalDistance += Math.sqrt(dx * dx + dy * dy);
    }
    
    const avgDistance = totalDistance / (component.length - 1);
    return Math.max(0, 1 - avgDistance / 10); // Normalize, prefer shorter distances
  }

  // Assess mathematical beauty
  async assessMathematicalBeauty(imageData) {
    const fibonacciScore = this.assessFibonacciProportions(imageData);
    const symmetryScore = this.assessSymmetry(imageData);
    const proportionScore = this.assessProportions(imageData);

    const weights = this.artisticStandards.mathematicalBeauty;
    const beautyScore = (
      fibonacciScore * weights.fibonacci.weight +
      symmetryScore * weights.symmetry.weight +
      proportionScore * weights.proportion.weight
    );

    return {
      fibonacci: fibonacciScore,
      symmetry: symmetryScore,
      proportion: proportionScore,
      overallScore: beautyScore
    };
  }

  // Assess Fibonacci proportions
  assessFibonacciProportions(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    
    // Check if dimensions follow Fibonacci ratios
    const aspectRatio = width / height;
    const fibonacciRatios = [1/1, 2/1, 3/2, 5/3, 8/5, 13/8, 21/13];
    
    let bestMatch = 0;
    fibonacciRatios.forEach(ratio => {
      const deviation = Math.abs(aspectRatio - ratio) / ratio;
      const match = Math.max(0, 1 - deviation);
      bestMatch = Math.max(bestMatch, match);
    });
    
    return bestMatch;
  }

  // Assess symmetry
  assessSymmetry(imageData) {
    const horizontalSymmetry = this.calculateHorizontalSymmetry(imageData);
    const verticalSymmetry = this.calculateVerticalSymmetry(imageData);
    const radialSymmetry = this.calculateRadialSymmetry(imageData);
    
    return Math.max(horizontalSymmetry, verticalSymmetry, radialSymmetry);
  }

  // Calculate horizontal symmetry
  calculateHorizontalSymmetry(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    
    let symmetryScore = 0;
    let comparisons = 0;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width / 2; x++) {
        const leftIdx = (y * width + x) * 4;
        const rightIdx = (y * width + (width - 1 - x)) * 4;
        
        const leftColor = [pixels[leftIdx], pixels[leftIdx + 1], pixels[leftIdx + 2]];
        const rightColor = [pixels[rightIdx], pixels[rightIdx + 1], pixels[rightIdx + 2]];
        
        const colorDiff = Math.sqrt(
          Math.pow(leftColor[0] - rightColor[0], 2) +
          Math.pow(leftColor[1] - rightColor[1], 2) +
          Math.pow(leftColor[2] - rightColor[2], 2)
        ) / (255 * Math.sqrt(3));
        
        symmetryScore += 1 - colorDiff;
        comparisons++;
      }
    }
    
    return comparisons > 0 ? symmetryScore / comparisons : 0;
  }

  // Calculate vertical symmetry
  calculateVerticalSymmetry(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    
    let symmetryScore = 0;
    let comparisons = 0;
    
    for (let y = 0; y < height / 2; y++) {
      for (let x = 0; x < width; x++) {
        const topIdx = (y * width + x) * 4;
        const bottomIdx = ((height - 1 - y) * width + x) * 4;
        
        const topColor = [pixels[topIdx], pixels[topIdx + 1], pixels[topIdx + 2]];
        const bottomColor = [pixels[bottomIdx], pixels[bottomIdx + 1], pixels[bottomIdx + 2]];
        
        const colorDiff = Math.sqrt(
          Math.pow(topColor[0] - bottomColor[0], 2) +
          Math.pow(topColor[1] - bottomColor[1], 2) +
          Math.pow(topColor[2] - bottomColor[2], 2)
        ) / (255 * Math.sqrt(3));
        
        symmetryScore += 1 - colorDiff;
        comparisons++;
      }
    }
    
    return comparisons > 0 ? symmetryScore / comparisons : 0;
  }

  // Calculate radial symmetry
  calculateRadialSymmetry(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    const centerX = width / 2;
    const centerY = height / 2;
    const maxRadius = Math.min(centerX, centerY);
    
    let symmetryScore = 0;
    let comparisons = 0;
    
    // Check radial symmetry at different angles
    const angleSteps = 16;
    const radiusSteps = 20;
    
    for (let r = 10; r < maxRadius; r += maxRadius / radiusSteps) {
      for (let a = 0; a < angleSteps; a++) {
        const angle1 = (a / angleSteps) * 2 * Math.PI;
        const angle2 = angle1 + Math.PI; // Opposite point
        
        const x1 = Math.floor(centerX + r * Math.cos(angle1));
        const y1 = Math.floor(centerY + r * Math.sin(angle1));
        const x2 = Math.floor(centerX + r * Math.cos(angle2));
        const y2 = Math.floor(centerY + r * Math.sin(angle2));
        
        if (x1 >= 0 && x1 < width && y1 >= 0 && y1 < height &&
            x2 >= 0 && x2 < width && y2 >= 0 && y2 < height) {
          
          const idx1 = (y1 * width + x1) * 4;
          const idx2 = (y2 * width + x2) * 4;
          
          const color1 = [pixels[idx1], pixels[idx1 + 1], pixels[idx1 + 2]];
          const color2 = [pixels[idx2], pixels[idx2 + 1], pixels[idx2 + 2]];
          
          const colorDiff = Math.sqrt(
            Math.pow(color1[0] - color2[0], 2) +
            Math.pow(color1[1] - color2[1], 2) +
            Math.pow(color1[2] - color2[2], 2)
          ) / (255 * Math.sqrt(3));
          
          symmetryScore += 1 - colorDiff;
          comparisons++;
        }
      }
    }
    
    return comparisons > 0 ? symmetryScore / comparisons : 0;
  }

  // Assess proportions
  assessProportions(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    
    // Check various proportion relationships
    const aspectRatio = width / height;
    const goldenRatio = (1 + Math.sqrt(5)) / 2;
    
    // Score based on proximity to pleasing proportions
    const proportions = [1, 4/3, 3/2, goldenRatio, 16/9, 2];
    let bestScore = 0;
    
    proportions.forEach(proportion => {
      const deviation = Math.abs(aspectRatio - proportion) / proportion;
      const score = Math.max(0, 1 - deviation);
      bestScore = Math.max(bestScore, score);
    });
    
    return bestScore;
  }

  // Assess emotional impact
  async assessEmotionalImpact(imageData) {
    const contrastScore = this.assessEmotionalContrast(imageData);
    const moodScore = this.assessMood(imageData);
    const energyScore = this.assessEnergy(imageData);

    const weights = this.artisticStandards.emotionalImpact;
    const impactScore = (
      contrastScore * weights.contrast.weight +
      moodScore * weights.mood.weight +
      energyScore * weights.energy.weight
    );

    return {
      contrast: contrastScore,
      mood: moodScore,
      energy: energyScore,
      overallScore: impactScore
    };
  }

  // Assess emotional contrast
  assessEmotionalContrast(imageData) {
    const pixels = imageData.data;
    let totalContrast = 0;
    let comparisons = 0;
    
    // Sample pixels for contrast calculation
    for (let i = 0; i < pixels.length; i += 400) { // Sample every 100th pixel
      if (i + 400 < pixels.length) {
        const r1 = pixels[i], g1 = pixels[i + 1], b1 = pixels[i + 2];
        const r2 = pixels[i + 400], g2 = pixels[i + 401], b2 = pixels[i + 402];
        
        const brightness1 = (r1 + g1 + b1) / (3 * 255);
        const brightness2 = (r2 + g2 + b2) / (3 * 255);
        
        totalContrast += Math.abs(brightness1 - brightness2);
        comparisons++;
      }
    }
    
    return comparisons > 0 ? totalContrast / comparisons : 0;
  }

  // Assess mood based on color temperature and brightness
  assessMood(imageData) {
    const pixels = imageData.data;
    let warmth = 0;
    let brightness = 0;
    let saturation = 0;
    let pixelCount = 0;
    
    for (let i = 0; i < pixels.length; i += 16) { // Sample every 4th pixel
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      
      // Calculate warmth (red/orange vs blue)
      warmth += (r - b) / 255;
      
      // Calculate brightness
      brightness += (r + g + b) / (3 * 255);
      
      // Calculate saturation
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      saturation += max === 0 ? 0 : (max - min) / max;
      
      pixelCount++;
    }
    
    if (pixelCount === 0) return 0;
    
    const avgWarmth = warmth / pixelCount;
    const avgBrightness = brightness / pixelCount;
    const avgSaturation = saturation / pixelCount;
    
    // Combine factors for mood score
    // Positive mood: warm, bright, saturated
    const moodScore = (
      (avgWarmth + 1) / 2 * 0.4 +  // Normalize warmth to 0-1
      avgBrightness * 0.3 +
      avgSaturation * 0.3
    );
    
    return Math.min(moodScore, 1);
  }

  // Assess energy based on color intensity and variation
  assessEnergy(imageData) {
    const pixels = imageData.data;
    let totalIntensity = 0;
    let intensityVariation = 0;
    let pixelCount = 0;
    const intensities = [];
    
    for (let i = 0; i < pixels.length; i += 16) { // Sample every 4th pixel
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      
      const intensity = Math.sqrt(r * r + g * g + b * b) / (255 * Math.sqrt(3));
      intensities.push(intensity);
      totalIntensity += intensity;
      pixelCount++;
    }
    
    if (pixelCount === 0) return 0;
    
    const avgIntensity = totalIntensity / pixelCount;
    
    // Calculate variation
    intensities.forEach(intensity => {
      intensityVariation += Math.pow(intensity - avgIntensity, 2);
    });
    intensityVariation = Math.sqrt(intensityVariation / pixelCount);
    
    // Energy is combination of intensity and variation
    return Math.min(avgIntensity * 0.6 + intensityVariation * 0.4, 1);
  }

  // Calculate overall artistic score
  calculateOverallArtisticScore(assessment) {
    const weights = {
      composition: 0.25,
      colorHarmony: 0.20,
      visualFlow: 0.20,
      mathematicalBeauty: 0.20,
      emotionalImpact: 0.15
    };

    return (
      assessment.composition.overallScore * weights.composition +
      assessment.colorHarmony.overallScore * weights.colorHarmony +
      assessment.visualFlow.overallScore * weights.visualFlow +
      assessment.mathematicalBeauty.overallScore * weights.mathematicalBeauty +
      assessment.emotionalImpact.overallScore * weights.emotionalImpact
    );
  }

  // Determine artistic quality level
  determineArtisticLevel(score) {
    const criteria = this.assessmentCriteria;
    
    if (score >= criteria.excellent.min) return 'EXCELLENT';
    if (score >= criteria.good.min) return 'GOOD';
    if (score >= criteria.acceptable.min) return 'ACCEPTABLE';
    if (score >= criteria.needsImprovement.min) return 'NEEDS_IMPROVEMENT';
    return 'POOR';
  }

  // Analyze composition strengths
  analyzeCompositionStrengths(ruleOfThirds, goldenRatio, balance, focusPoint) {
    const strengths = [];
    const weaknesses = [];
    
    if (ruleOfThirds > 0.7) strengths.push('Strong rule of thirds composition');
    else if (ruleOfThirds < 0.4) weaknesses.push('Poor rule of thirds alignment');
    
    if (goldenRatio > 0.7) strengths.push('Excellent golden ratio proportions');
    else if (goldenRatio < 0.4) weaknesses.push('Poor golden ratio proportions');
    
    if (balance > 0.7) strengths.push('Well-balanced composition');
    else if (balance < 0.4) weaknesses.push('Unbalanced composition');
    
    if (focusPoint > 0.7) strengths.push('Clear focal point');
    else if (focusPoint < 0.4) weaknesses.push('Weak or unclear focal point');
    
    return { strengths, weaknesses };
  }

  // Generate artistic recommendations
  generateArtisticRecommendations(assessment) {
    const recommendations = [];
    
    // Composition recommendations
    if (assessment.composition.overallScore < 0.6) {
      recommendations.push('Improve composition by applying rule of thirds or golden ratio');
      if (assessment.composition.balance < 0.5) {
        recommendations.push('Rebalance visual elements for better composition');
      }
    }
    
    // Color harmony recommendations
    if (assessment.colorHarmony.overallScore < 0.6) {
      recommendations.push('Enhance color harmony using complementary or analogous color schemes');
    }
    
    // Visual flow recommendations
    if (assessment.visualFlow.overallScore < 0.6) {
      recommendations.push('Improve visual flow and eye movement paths');
    }
    
    // Mathematical beauty recommendations
    if (assessment.mathematicalBeauty.overallScore < 0.6) {
      recommendations.push('Consider mathematical proportions like golden ratio or Fibonacci sequences');
    }
    
    // Emotional impact recommendations
    if (assessment.emotionalImpact.overallScore < 0.6) {
      recommendations.push('Enhance emotional impact through contrast, color temperature, or energy');
    }
    
    return recommendations;
  }

  // Batch assess multiple images
  async batchAssessArtisticQuality(imageDirectory) {
    console.log(`🎨 Batch artistic quality assessment: ${imageDirectory}`);
    
    if (!fs.existsSync(imageDirectory)) {
      throw new Error(`Directory not found: ${imageDirectory}`);
    }
    
    const files = fs.readdirSync(imageDirectory);
    const imageFiles = files.filter(file => 
      file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpg')
    );
    
    const results = [];
    
    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i];
      const imagePath = path.join(imageDirectory, imageFile);
      
      console.log(`🔍 Assessing ${imageFile} (${i + 1}/${imageFiles.length})`);
      
      try {
        const assessment = await this.assessArtisticQuality(imagePath);
        results.push({
          filename: imageFile,
          ...assessment
        });
        
        console.log(`✅ ${imageFile}: ${assessment.qualityLevel} (${assessment.overallScore.toFixed(3)})`);
        
      } catch (error) {
        console.error(`❌ Error assessing ${imageFile}: ${error.message}`);
        results.push({
          filename: imageFile,
          error: error.message
        });
      }
    }
    
    // Generate batch report
    const batchReport = this.generateBatchReport(results);
    
    // Save batch report
    const reportPath = path.join(imageDirectory, 'artistic-quality-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      totalImages: imageFiles.length,
      results: results,
      summary: batchReport
    }, null, 2));
    
    this.printBatchReport(batchReport, imageFiles.length);
    
    return results;
  }

  // Generate batch report summary
  generateBatchReport(results) {
    const validResults = results.filter(r => !r.error);
    
    if (validResults.length === 0) {
      return { error: 'No valid results to analyze' };
    }
    
    const scores = validResults.map(r => r.overallScore);
    const levels = validResults.map(r => r.qualityLevel);
    
    const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    const maxScore = Math.max(...scores);
    const minScore = Math.min(...scores);
    
    const levelCounts = {};
    levels.forEach(level => {
      levelCounts[level] = (levelCounts[level] || 0) + 1;
    });
    
    return {
      averageScore: avgScore,
      maxScore: maxScore,
      minScore: minScore,
      qualityDistribution: levelCounts,
      passRate: (validResults.filter(r => r.overallScore >= 0.55).length / validResults.length) * 100
    };
  }

  // Print batch report
  printBatchReport(report, totalImages) {
    console.log('\n🎨 ARTISTIC QUALITY BATCH REPORT');
    console.log('================================');
    console.log(`📁 Total Images: ${totalImages}`);
    console.log(`📊 Average Score: ${report.averageScore?.toFixed(3) || 'N/A'}`);
    console.log(`🏆 Best Score: ${report.maxScore?.toFixed(3) || 'N/A'}`);
    console.log(`📉 Worst Score: ${report.minScore?.toFixed(3) || 'N/A'}`);
    console.log(`✅ Pass Rate: ${report.passRate?.toFixed(1) || 'N/A'}%`);
    
    if (report.qualityDistribution) {
      console.log('\n📊 Quality Distribution:');
      Object.entries(report.qualityDistribution).forEach(([level, count]) => {
        console.log(`  ${level}: ${count}`);
      });
    }
  }
}

// CLI usage
async function runArtisticAssessment() {
  const imagePath = process.argv[2];
  const isBatch = process.argv.includes('--batch');
  
  if (!imagePath) {
    console.log('Usage: node artistic-quality-assessment.js <image-path-or-directory> [--batch]');
    process.exit(1);
  }
  
  try {
    const assessor = new ArtisticQualityAssessment();
    
    if (isBatch) {
      await assessor.batchAssessArtisticQuality(imagePath);
    } else {
      const result = await assessor.assessArtisticQuality(imagePath);
      console.log('\n🎨 ARTISTIC QUALITY ASSESSMENT');
      console.log('==============================');
      console.log(`📊 Overall Score: ${result.overallScore.toFixed(3)}`);
      console.log(`🏆 Quality Level: ${result.qualityLevel}`);
      
      if (result.recommendations.length > 0) {
        console.log('\n💡 Recommendations:');
        result.recommendations.forEach(rec => console.log(`  • ${rec}`));
      }
    }
    
  } catch (error) {
    console.error('❌ Artistic assessment failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  runArtisticAssessment();
}

module.exports = { ArtisticQualityAssessment };
