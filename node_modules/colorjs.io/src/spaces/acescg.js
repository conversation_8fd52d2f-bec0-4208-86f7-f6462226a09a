import RGBColorSpace from "../rgbspace.js";
import {WHITES} from "../adapt.js";
import "../CATs.js"; // because of the funky whitepoint

// The ACES whitepoint
// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates
// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py
// Similar to D60
WHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];

// convert an array of linear-light ACEScc values to CIE XYZ
const toXYZ_M = [
	[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],
	[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],
	[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],
];
const fromXYZ_M = [
	[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],
	[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],
	[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],
];

export default new RGBColorSpace({
	id: "acescg",
	cssId: "--acescg",
	name: "ACEScg",

	// ACEScg – A scene-referred, linear-light encoding of ACES Data
	// https://docs.acescentral.com/specifications/acescg/
	// uses the AP1 primaries, see section 4.3.1 Color primaries
	coords: {
		r: {
			range: [0, 65504],
			name: "Red",
		},
		g: {
			range: [0, 65504],
			name: "Green",
		},
		b: {
			range: [0, 65504],
			name: "Blue",
		},
	},

	referred: "scene",

	white: WHITES.ACES,

	toXYZ_M,
	fromXYZ_M,
});

// export default Color;
