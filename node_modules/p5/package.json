{"name": "p5", "repository": "processing/p5.js", "scripts": {"build": "grunt build", "dev": "grunt yui browserify:dev connect:yui watch:quick", "docs": "grunt yui", "docs:dev": "grunt yui:dev", "test": "grunt", "lint": "grunt lint", "lint:source": "grunt lint:source", "lint:samples": "grunt lint:samples", "lint:fix": "grunt lint-fix", "contributors:add": "all-contributors add", "contributors:generate": "all-contributors generate", "generate": "all-contributors generate", "release": "grunt release-p5", "prepublishOnly": "grunt prerelease"}, "lint-staged": {"ignore": ["test/js/**/*.js"], "Gruntfile.js": "eslint", "docs/preprocessor.js": "eslint", "utils/**/*.js": "eslint", "tasks/**/*.js": "eslint", "test/**/*.js": "eslint", "src/**/*.js": ["eslint", "node --require @babel/register ./utils/sample-linter.js"]}, "version": "1.7.0", "devDependencies": {"@babel/core": "^7.7.7", "@babel/preset-env": "^7.10.2", "@babel/register": "^7.7.7", "all-contributors-cli": "^6.19.0", "babel-plugin-i18next-extract": "^0.5.0", "babel-plugin-istanbul": "^5.2.0", "babelify": "^10.0.0", "brfs-babel": "^2.0.0", "browserify": "^16.5.0", "chai": "^3.5.0", "connect-modrewrite": "^0.10.1", "core-js": "^3.6.5", "derequire": "^2.0.0", "es6-promise": "^4.2.8", "eslint": "^8.23.1", "fetch-jsonp": "^1.1.3", "file-saver": "^1.3.8", "gifenc": "^1.0.3", "grunt": "^1.6.1", "grunt-cli": "^1.4.3", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-compress": "^2.0.0", "grunt-contrib-connect": "^3.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-watch": "^1.1.0", "grunt-contrib-yuidoc": "1.0.0", "grunt-eslint": "^24.0.0", "grunt-minjson": "^0.4.0", "grunt-mocha-test": "^0.13.3", "grunt-newer": "^1.3.0", "grunt-simple-nyc": "^3.0.1", "html-entities": "^1.3.1", "husky": "^4.2.3", "i18next": "^19.0.2", "i18next-browser-languagedetector": "^4.0.1", "libtess": "^1.2.2", "lint-staged": "^4.3.0", "marked": "^4.0.10", "mocha": "^10.2.0", "np": "^7.6.2", "omggif": "^1.0.10", "open": "^7.0.3", "opentype.js": "^0.9.0", "pretty-fast": "^0.2.7", "promise-map-series": "^0.2.3", "puppeteer": "^18.2.1", "regenerator-runtime": "^0.13.3", "simple-git": "^3.16.1", "whatwg-fetch": "^2.0.4"}, "license": "LGPL-2.1", "main": "./lib/p5.min.js", "files": ["license.txt", "lib/p5.min.js", "lib/p5.js", "lib/addons/p5.sound.js", "lib/addons/p5.sound.min.js", "translations/**"], "description": "[![npm version](https://badge.fury.io/js/p5.svg)](https://www.npmjs.com/package/p5)", "bugs": {"url": "https://github.com/processing/p5.js/issues"}, "homepage": "https://github.com/processing/p5.js#readme", "directories": {"doc": "docs", "test": "test"}, "babel": {"presets": [["@babel/preset-env", {"useBuiltIns": "usage", "corejs": 3}]], "plugins": [["i18next-extract", {"locales": ["en", "es"], "outputPath": "translations/{{locale}}/{{ns}}.json", "tFunctionNames": ["translator"]}]], "env": {"test": {"plugins": [["istanbul", {"include": ["src/**/*.js"]}]]}}}, "browserslist": ["last 2 versions", "not dead"], "author": "", "husky": {"hooks": {"pre-commit": "lint-staged"}}}