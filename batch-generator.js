const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const { analyzeRarity } = require('./rarity-analyzer');

// Konfigürasyon
const CONFIG = {
  OUTPUT_DIR: path.join(__dirname, 'output'),
  BATCH_SIZE: 100, // Her batch'te kaç NFT
  MAX_WORKERS: 4,  // Paralel işlem sayısı
  WIDTH: 3000,
  HEIGHT: 4000,
  TOTAL_SUPPLY: 10000, // Toplam koleksiyon boyutu
  RESUME_FROM_EXISTING: true // Mevcut NFT'leri atla
};

// Renk paletleri ve ağırlıkları
const PALETTES = [
  {name:'Muted Pastel', bg:'#f7f2ea', hues:[18,28,35,42,50], weight:0.25},
  {name:'Nocturne',     bg:'#111217', hues:[12,20,28,36,48], weight:0.2},
  {name:'<PERSON><PERSON>',   bg:'#f3f6f7', hues:[165,185,15,28,40], weight:0.2},
  {name:'<PERSON><PERSON>',   bg:'#f6efe2', hues:[16,22,26,30,34], weight:0.1},
  {name:'Cosmic Purple', bg:'#0a0a0f', hues:[260,280,300,320,340], weight:0.15},
  {name:'Forest Dream', bg:'#f8faf6', hues:[90,110,130,150,170], weight:0.1},
];

const SPIRAL_TYPES = [
  {name: 'Classic', weight: 0.4},
  {name: 'Fibonacci', weight: 0.25},
  {name: 'Archimedean', weight: 0.2},
  {name: 'Logarithmic', weight: 0.15}
];

const EFFECTS = [
  {name: 'None', weight: 0.3},
  {name: 'Particles', weight: 0.25},
  {name: 'Geometric', weight: 0.25},
  {name: 'Texture', weight: 0.2}
];

// Progress tracking
class ProgressTracker {
  constructor(total) {
    this.total = total;
    this.completed = 0;
    this.startTime = Date.now();
    this.lastUpdate = Date.now();
  }
  
  update(completed) {
    this.completed = completed;
    const now = Date.now();
    
    if (now - this.lastUpdate > 5000) { // Her 5 saniyede bir güncelle
      this.printProgress();
      this.lastUpdate = now;
    }
  }
  
  printProgress() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const rate = this.completed / elapsed;
    const remaining = (this.total - this.completed) / rate;
    const percentage = ((this.completed / this.total) * 100).toFixed(1);
    
    console.log(`📊 İlerleme: ${this.completed}/${this.total} (${percentage}%) | ` +
                `Hız: ${rate.toFixed(1)} NFT/s | ` +
                `Kalan süre: ${Math.round(remaining)}s`);
  }
  
  finish() {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const avgRate = this.completed / elapsed;
    console.log(`✅ Tamamlandı! ${this.completed} NFT ${elapsed.toFixed(1)}s'de üretildi (Ort: ${avgRate.toFixed(1)} NFT/s)`);
  }
}

// Rastgele sayı üretici sınıfı
class SeededRandom {
  constructor(seed) {
    this.seed = seed;
  }
  
  random() {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }
  
  randomRange(min, max) {
    return min + this.random() * (max - min);
  }
  
  randomInt(min, max) {
    return Math.floor(this.randomRange(min, max));
  }
  
  noise(x, y = 0, z = 0) {
    let n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
    return (n - Math.floor(n));
  }
}

function weightedPick(list, rnd) {
  let sum = list.reduce((a,b)=>a+b.weight,0);
  let r = rnd()*sum, acc = 0;
  for (let item of list) {
    acc += item.weight;
    if (r <= acc) return item;
  }
}

function ensureDir(dir) {
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

// Mevcut NFT'leri kontrol et
function getExistingNFTs() {
  if (!fs.existsSync(CONFIG.OUTPUT_DIR)) return new Set();
  
  const files = fs.readdirSync(CONFIG.OUTPUT_DIR);
  const existing = new Set();
  
  files.forEach(file => {
    if (file.endsWith('.json')) {
      const seed = file.replace('spiral_', '').replace('.json', '');
      existing.add(parseInt(seed));
    }
  });
  
  return existing;
}

// Benzersiz seed üret
function generateUniqueSeed(existingSeeds) {
  let seed;
  do {
    seed = Math.floor(Math.random() * 1e9);
  } while (existingSeeds.has(seed));
  
  existingSeeds.add(seed);
  return seed;
}

// Ana batch generation fonksiyonu
async function generateBatch(startIndex, endIndex, existingSeeds) {
  const results = [];
  
  for (let i = startIndex; i < endIndex; i++) {
    try {
      const seed = generateUniqueSeed(existingSeeds);
      const result = await generateSingleNFT(seed);
      results.push(result);
    } catch (error) {
      console.error(`❌ NFT ${i} üretilirken hata:`, error.message);
    }
  }
  
  return results;
}

// Tek NFT üretimi
async function generateSingleNFT(seed) {
  const rng = new SeededRandom(seed);
  
  // Traits oluştur
  const pal = weightedPick(PALETTES, () => rng.random());
  const spiralType = weightedPick(SPIRAL_TYPES, () => rng.random());
  const effect = weightedPick(EFFECTS, () => rng.random());
  
  const traits = {
    seed: seed,
    palette: pal.name,
    spiralType: spiralType.name,
    effect: effect.name,
    rings: rng.random() < 0.3 ? rng.randomInt(480,520) : rng.randomInt(360,380),
    steps: rng.randomInt(70,110),
    wobble: rng.random() < 0.2 ? rng.randomRange(0.14,0.16) : rng.randomRange(0.08,0.12),
    flowFreqR: rng.randomRange(0.015,0.028),
    flowFreqI: rng.randomRange(0.015,0.028),
    swirlOffset: rng.randomRange(0.42,0.62),
    strokeW: rng.random() < 0.3 ? rng.randomRange(2.0,2.6) : rng.randomRange(1.4,1.8),
    alpha: rng.randomRange(0.28,0.52),
    gradientIntensity: rng.randomRange(0.1, 0.8),
    particleCount: effect.name === 'Particles' ? rng.randomInt(50, 200) : 0,
    geometricDensity: effect.name === 'Geometric' ? rng.randomRange(0.1, 0.4) : 0,
    textureScale: effect.name === 'Texture' ? rng.randomRange(0.005, 0.02) : 0,
    spiralTightness: spiralType.name === 'Fibonacci' ? rng.randomRange(0.1, 0.3) : 
                    spiralType.name === 'Logarithmic' ? rng.randomRange(0.05, 0.15) : 
                    rng.randomRange(0.8, 1.2),
    centerOffset: {x: rng.randomRange(-0.1, 0.1), y: rng.randomRange(-0.1, 0.1)}
  };
  
  // Canvas oluştur ve çiz
  const canvas = createCanvas(CONFIG.WIDTH, CONFIG.HEIGHT);
  const ctx = canvas.getContext('2d');
  
  // Çizim işlemleri (spiral.js'den alınan fonksiyonlar)
  await drawArt(canvas, ctx, traits, pal, rng);
  
  // Dosyaları kaydet
  try {
    const imgBuffer = canvas.toBuffer('image/png');
    const imgPath = path.join(CONFIG.OUTPUT_DIR, `spiral_${seed}.png`);
    const jsonPath = path.join(CONFIG.OUTPUT_DIR, `spiral_${seed}.json`);

    fs.writeFileSync(imgPath, imgBuffer);

    // Metadata oluştur
    const rarityScore = calculateRarity(traits);
    const metadata = createMetadata(seed, traits, rarityScore);
    fs.writeFileSync(jsonPath, JSON.stringify(metadata, null, 2));
  } catch (error) {
    console.error(`❌ Dosya kaydetme hatası (seed: ${seed}):`, error.message);
    throw error;
  }
  
  return { seed, traits, rarityScore };
}

// Çizim fonksiyonu (spiral.js'den uyarlandı)
async function drawArt(canvas, ctx, traits, pal, rng) {
  // Arka plan
  ctx.fillStyle = pal.bg;
  ctx.fillRect(0, 0, CONFIG.WIDTH, CONFIG.HEIGHT);
  
  // Merkez noktası
  const centerX = CONFIG.WIDTH * (0.5 + traits.centerOffset.x);
  const centerY = CONFIG.HEIGHT * (traits.swirlOffset + traits.centerOffset.y);
  
  ctx.save();
  ctx.translate(centerX, centerY);
  
  // Ana spiral çizimi
  drawSpiral(ctx, traits, pal, rng);
  
  // Efektler
  if (traits.effect === 'Particles') {
    drawParticles(ctx, traits, pal, rng);
  } else if (traits.effect === 'Geometric') {
    drawGeometricElements(ctx, traits, pal, rng);
  } else if (traits.effect === 'Texture') {
    drawTextureBackground(ctx, traits, rng);
  }
  
  ctx.restore();
}

// Spiral çizim fonksiyonu (basitleştirilmiş)
function drawSpiral(ctx, traits, pal, rng) {
  ctx.lineWidth = traits.strokeW;
  
  for (let r = 6; r < traits.rings; r++) {
    let base = getSpiralRadius(r, traits.spiralType, traits);
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10 + rng.randomRange(-6, 6);
    
    let saturation = 60 + traits.gradientIntensity * 30;
    let lightness = 55 + Math.sin(r * 0.1) * traits.gradientIntensity * 20;
    
    ctx.strokeStyle = `hsla(${hue}, ${saturation}%, ${lightness}%, ${traits.alpha})`;
    ctx.beginPath();
    
    let firstPoint = true;
    for (let i = 0; i < traits.steps; i++) {
      let t = i / traits.steps * Math.PI * 2;
      let coords = getSpiralCoords(r, i, t, base, traits, rng);
      
      if (firstPoint) {
        ctx.moveTo(coords.x, coords.y);
        firstPoint = false;
      } else {
        ctx.lineTo(coords.x, coords.y);
      }
    }
    ctx.closePath();
    ctx.stroke();
  }
}

function getSpiralRadius(r, spiralType, traits) {
  switch(spiralType) {
    case 'Fibonacci':
      return r * r * 0.1 * traits.spiralTightness;
    case 'Archimedean':
      return r * 2.4 * traits.spiralTightness;
    case 'Logarithmic':
      return Math.exp(r * 0.1) * traits.spiralTightness;
    default:
      return r * 2.4;
  }
}

function getSpiralCoords(r, i, t, base, traits, rng) {
  let n = rng.noise(r * traits.flowFreqR, i * traits.flowFreqI);
  let rr = base * (1 + traits.wobble * (n * 2 - 1));
  
  if (traits.spiralType === 'Fibonacci') {
    let angle = t + r * 0.618034;
    return { x: rr * Math.cos(angle), y: rr * Math.sin(angle) };
  } else if (traits.spiralType === 'Logarithmic') {
    let angle = t * (1 + r * 0.1);
    return { x: rr * Math.cos(angle), y: rr * Math.sin(angle) };
  } else {
    return { x: rr * Math.cos(t), y: rr * Math.sin(t) };
  }
}

function drawParticles(ctx, traits, pal, rng) {
  for (let i = 0; i < traits.particleCount; i++) {
    let angle = rng.random() * Math.PI * 2;
    let radius = rng.randomRange(50, traits.rings * 2);
    let x = radius * Math.cos(angle);
    let y = radius * Math.sin(angle);
    
    let size = rng.randomRange(1, 4);
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
    let alpha = rng.randomRange(0.1, 0.4);
    
    ctx.fillStyle = `hsla(${hue}, 70%, 60%, ${alpha})`;
    ctx.beginPath();
    ctx.arc(x, y, size/2, 0, Math.PI * 2);
    ctx.fill();
  }
}

function drawGeometricElements(ctx, traits, pal, rng) {
  ctx.lineWidth = 1;
  let elementCount = traits.geometricDensity * 100;
  
  for (let i = 0; i < elementCount; i++) {
    let angle = (i / elementCount) * Math.PI * 2;
    let radius = rng.randomRange(100, traits.rings * 1.5);
    let x = radius * Math.cos(angle);
    let y = radius * Math.sin(angle);
    
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
    ctx.strokeStyle = `hsla(${hue}, 50%, 50%, 0.3)`;
    
    let shapes = ['triangle', 'square', 'hexagon'];
    let shapeType = shapes[Math.floor(rng.random() * shapes.length)];
    drawGeometricShape(ctx, x, y, shapeType, rng.randomRange(5, 15), rng);
  }
}

function drawGeometricShape(ctx, x, y, type, size, rng) {
  ctx.save();
  ctx.translate(x, y);
  ctx.rotate(rng.random() * Math.PI * 2);
  
  ctx.beginPath();
  switch(type) {
    case 'triangle':
      ctx.moveTo(-size/2, size/2);
      ctx.lineTo(size/2, size/2);
      ctx.lineTo(0, -size/2);
      ctx.closePath();
      break;
    case 'square':
      ctx.rect(-size/2, -size/2, size, size);
      break;
    case 'hexagon':
      for (let i = 0; i < 6; i++) {
        let angle = (i / 6) * Math.PI * 2;
        let px = size/2 * Math.cos(angle);
        let py = size/2 * Math.sin(angle);
        if (i === 0) ctx.moveTo(px, py);
        else ctx.lineTo(px, py);
      }
      ctx.closePath();
      break;
  }
  ctx.stroke();
  ctx.restore();
}

function drawTextureBackground(ctx, traits, rng) {
  const imageData = ctx.getImageData(0, 0, CONFIG.WIDTH, CONFIG.HEIGHT);
  const data = imageData.data;
  
  for (let x = 0; x < CONFIG.WIDTH; x += 4) {
    for (let y = 0; y < CONFIG.HEIGHT; y += 4) {
      let noiseVal = rng.noise(x * traits.textureScale, y * traits.textureScale);
      let brightness = noiseVal * 30;
      
      let index = (x + y * CONFIG.WIDTH) * 4;
      if (index < data.length - 3) {
        data[index] += brightness;
        data[index + 1] += brightness;
        data[index + 2] += brightness;
      }
    }
  }
  
  ctx.putImageData(imageData, 0, 0);
}

function calculateRarity(traits) {
  let score = 0;
  
  if (traits.spiralType === 'Logarithmic') score += 40;
  else if (traits.spiralType === 'Fibonacci') score += 30;
  else if (traits.spiralType === 'Archimedean') score += 20;
  else score += 10;
  
  if (traits.effect === 'Texture') score += 35;
  else if (traits.effect === 'Geometric') score += 25;
  else if (traits.effect === 'Particles') score += 20;
  else score += 5;
  
  if (traits.palette === 'Sepia Silk') score += 30;
  else if (traits.palette === 'Cosmic Purple') score += 25;
  else if (traits.palette === 'Forest Dream') score += 20;
  else score += 10;
  
  if (traits.rings > 500) score += 15;
  if (traits.wobble > 0.14) score += 10;
  if (traits.gradientIntensity > 0.7) score += 10;
  if (traits.strokeW > 2.0) score += 8;
  
  return Math.min(score, 100);
}

function createMetadata(seed, traits, rarityScore) {
  return {
    name: `Spiral #${seed}`,
    description: "Advanced generative spiral art with dynamic effects and multiple spiral algorithms",
    image: `spiral_${seed}.png`,
    external_url: `https://your-website.com/spiral/${seed}`,
    attributes: [
      {trait_type: "Palette", value: traits.palette},
      {trait_type: "Spiral Type", value: traits.spiralType},
      {trait_type: "Effect", value: traits.effect},
      {trait_type: "Ring Count", value: traits.rings},
      {trait_type: "Stroke Weight", value: parseFloat(traits.strokeW.toFixed(2))},
      {trait_type: "Wobble Intensity", value: parseFloat(traits.wobble.toFixed(3))},
      {trait_type: "Gradient Intensity", value: parseFloat(traits.gradientIntensity.toFixed(2))},
      {trait_type: "Rarity Score", value: rarityScore, display_type: "number"}
    ],
    properties: {
      seed: seed,
      algorithm: "Advanced Spiral Generator v2.0",
      resolution: `${CONFIG.WIDTH}x${CONFIG.HEIGHT}`,
      created: new Date().toISOString()
    }
  };
}

// Ana batch generation fonksiyonu
async function runBatchGeneration(totalSupply = CONFIG.TOTAL_SUPPLY) {
  console.log(`🚀 Batch NFT Generation Başlatılıyor...`);
  console.log(`📊 Hedef: ${totalSupply} NFT`);
  console.log(`⚙️  Batch boyutu: ${CONFIG.BATCH_SIZE}`);
  console.log(`🔧 Paralel işlem: ${CONFIG.MAX_WORKERS}`);
  
  ensureDir(CONFIG.OUTPUT_DIR);
  
  // Mevcut NFT'leri kontrol et
  const existingSeeds = getExistingNFTs();
  const existingCount = existingSeeds.size;
  
  if (existingCount > 0 && CONFIG.RESUME_FROM_EXISTING) {
    console.log(`📁 ${existingCount} mevcut NFT bulundu, devam ediliyor...`);
  }
  
  const remaining = totalSupply - existingCount;
  if (remaining <= 0) {
    console.log(`✅ Koleksiyon zaten tamamlanmış! (${existingCount}/${totalSupply})`);
    return;
  }
  
  console.log(`🎯 ${remaining} NFT daha üretilecek...\n`);
  
  const tracker = new ProgressTracker(remaining);
  let completed = 0;
  
  // Batch'leri paralel olarak işle
  for (let i = 0; i < remaining; i += CONFIG.BATCH_SIZE) {
    const batchEnd = Math.min(i + CONFIG.BATCH_SIZE, remaining);
    const batchSize = batchEnd - i;
    
    console.log(`📦 Batch ${Math.floor(i/CONFIG.BATCH_SIZE) + 1} başlatılıyor (${batchSize} NFT)...`);
    
    try {
      const results = await generateBatch(i, batchEnd, existingSeeds);
      completed += results.length;
      tracker.update(completed);
      
      console.log(`✅ Batch tamamlandı: ${results.length} NFT üretildi`);
    } catch (error) {
      console.error(`❌ Batch hatası:`, error.message);
    }
  }
  
  tracker.finish();
  
  // Final analiz
  console.log(`\n🔍 Final rarity analizi çalıştırılıyor...`);
  try {
    analyzeRarity();
  } catch (error) {
    console.error(`❌ Rarity analizi hatası:`, error.message);
  }
  
  console.log(`\n🎉 Batch generation tamamlandı!`);
  console.log(`📁 Çıktılar: ${CONFIG.OUTPUT_DIR}`);
}

// CLI kullanımı
if (require.main === module) {
  const args = process.argv.slice(2);
  const totalSupply = args[0] ? parseInt(args[0]) : CONFIG.TOTAL_SUPPLY;
  
  runBatchGeneration(totalSupply).catch(console.error);
}

module.exports = { runBatchGeneration, CONFIG };
