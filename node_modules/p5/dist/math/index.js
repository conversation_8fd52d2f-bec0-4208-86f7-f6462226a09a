import calculation from './calculation.js';
import noise from './noise.js';
import random from './random.js';
import trigonometry from './trigonometry.js';
import math$1 from './math.js';
import vector from './p5.Vector.js';
import '../constants-C2DVjshm.js';

function math(p5){
  p5.registerAddon(calculation);
  p5.registerAddon(noise);
  p5.registerAddon(random);
  p5.registerAddon(trigonometry);
  p5.registerAddon(math$1);
  p5.registerAddon(vector);
}

export { math as default };
