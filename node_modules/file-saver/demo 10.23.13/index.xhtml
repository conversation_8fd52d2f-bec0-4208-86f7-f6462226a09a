﻿<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US-x-Hixie">
<head>
    <meta charset="utf-8"/>
    <title>FileSaver.js demo</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.rawgit.com/eligrey/FileSaver.js/702cd2e820b680f88a0f299e33085c196806fc52/demo/demo.css"/>
</head>
<body>
    <h1><a href="https://github.com/eligrey/FileSaver.js">FileSaver.js</a> demo</h1>
    <p>
        The following examples demonstrate how it is possible to generate and save any type of data right in the browser using the <code>saveAs()</code> FileSaver interface, without contacting any servers.
    </p>
    <section id="image-demo">
        <h2>Saving an image</h2>
        <canvas class="input" id="canvas" width="500" height="300"/>
        <form id="canvas-options">
            <label>Filename: <input type="text" class="filename" id="canvas-filename" placeholder="doodle"/>.png</label>
            <input type="submit" value="Save"/>
            <input type="button" id="canvas-clear" value="Clear"/>
        </form>
    </section>
    <section id="text-demo">
        <h2>Saving text</h2>
        <textarea class="input" id="text" placeholder="Once upon a time..."/>
        <form id="text-options">
            <label>Filename: <input type="text" class="filename" id="text-filename" placeholder="a plain document"/>.txt</label>
            <input type="submit" value="Save"/>
        </form>
    </section>
    <section id="html-demo">
        <h2>Saving rich text</h2>
        <div class="input" id="html" contenteditable="">
            <h3>Some example rich text</h3>
            <ul>
                <li><del>Plain</del> <ins>Boring</ins> text.</li>
                <li><em>Emphasized text!</em></li>
                <li><strong>Strong text!</strong></li>
                <li>
                    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="70" height="70">
                        <circle cx="35" cy="35" r="35" fill="red"/>
                        <text x="10" y="40">image</text>
                    </svg>
                </li>
                <li><a href="https://github.com/eligrey/FileSaver.js">A link.</a></li>
            </ul>
        </div>
        <form id="html-options">
            <label>Filename: <input type="text" class="filename" id="html-filename" placeholder="a rich document"/>.xhtml</label>
            <input type="submit" value="Save"/>
        </form>
    </section>
    <script async="" src="https://cdn.rawgit.com/eligrey/Blob.js/0cef2746414269b16834878a8abc52eb9d53e6bd/Blob.js"/>
    <script async="" src="https://cdn.rawgit.com/eligrey/canvas-toBlob.js/f1a01896135ab378aa5c0118eadd81da55e698d8/canvas-toBlob.js"/>
    <script async="" src="https://cdn.rawgit.com/eligrey/FileSaver.js/e9d941381475b5df8b7d7691013401e171014e89/FileSaver.min.js"/>
    <script async="" src="https://cdn.rawgit.com/eligrey/FileSaver.js/597b6cd0207ce408a6d34890b5b2826b13450714/demo/demo.js"/>
</body>
</html>
