{"name": "spiral-nft", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"generate": "node spiral.js", "analyze": "node rarity-analyzer.js", "full-generation": "npm run generate && npm run analyze", "batch": "node batch-generator.js", "batch-small": "node batch-generator.js 100", "batch-medium": "node batch-generator.js 1000", "batch-large": "node batch-generator.js 10000", "advanced": "node advanced-nft-generator.js", "quality-check": "node quality-control.js output", "quality-check-advanced": "node quality-control.js advanced-output", "full-pipeline": "npm run advanced && npm run quality-check-advanced", "ps5": "node ps5-production-system.js", "ps5-small": "PS5_NFT_COUNT=50 node ps5-production-system.js", "ps5-medium": "PS5_NFT_COUNT=200 node ps5-production-system.js", "ps5-large": "PS5_NFT_COUNT=1000 node ps5-production-system.js", "ps5-ultra": "PS5_NFT_COUNT=5000 node ps5-production-system.js", "ps5-quality-check": "node quality-control.js ps5-output", "ps5-full-pipeline": "npm run ps5 && npm run ps5-quality-check", "ps5-preview": "node ps5-realtime-preview.js", "ps5-preview-animate": "node ps5-realtime-preview.js --animate", "ps5-preview-batch": "node ps5-realtime-preview.js --regenerate 10", "ps5-batch-optimize": "node ps5-batch-optimizer.js", "quality-advanced": "node advanced-quality-control.js", "quality-advanced-output": "node advanced-quality-control.js output", "quality-advanced-ps5": "node advanced-quality-control.js ps5-output", "artistic-assess": "node artistic-quality-assessment.js", "artistic-batch": "node artistic-quality-assessment.js output --batch", "artistic-batch-ps5": "node artistic-quality-assessment.js ps5-output --batch", "full-quality-check": "npm run quality-advanced-output && npm run artistic-batch", "full-quality-check-ps5": "npm run quality-advanced-ps5 && npm run artistic-batch-ps5", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"canvas": "^3.1.2", "fs": "^0.0.1-security", "gifenc": "^1.0.3", "p5": "^1.7.0", "path": "^0.12.7"}}