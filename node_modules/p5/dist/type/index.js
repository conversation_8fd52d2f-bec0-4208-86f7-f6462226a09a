import textCore from './textCore.js';
import font from './p5.Font.js';
import '../p5.Renderer-DO9wIL55.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../constants-C2DVjshm.js';
import '../image/filters.js';
import '../math/p5.Vector.js';
import '../shape/custom_shapes.js';
import '../core/States.js';
import '../io/utilities.js';
import 'file-saver';
import '@japont/unicode-range';
import './unicodeRanges.js';
import './lib/Typr.js';
import 'pako';
import '@davepagurek/bezier-path';

function type(p5){
  p5.registerAddon(textCore);
  p5.registerAddon(font);
}

export { type as default };
