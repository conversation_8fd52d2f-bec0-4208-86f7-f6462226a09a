import { p as p5 } from '../main-rEhlsQtb.js';
import '../constants-C2DVjshm.js';
import './transform.js';
import './structure.js';
import './environment.js';
import '../math/p5.Vector.js';
import '../rendering-CpHn8PfG.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../dom/p5.Element.js';
import '../dom/p5.File.js';
import '../io/p5.XML.js';
import '../p5.Renderer-DO9wIL55.js';
import '../image/filters.js';
import '../shape/custom_shapes.js';
import './States.js';
import '../io/utilities.js';
import 'file-saver';
import '../dom/p5.MediaElement.js';
import '../shape/2d_primitives.js';
import './helpers.js';
import '../shape/attributes.js';
import '../shape/curves.js';
import '../shape/vertex.js';
import '../color/setting.js';
import 'omggif';
import '../io/csv.js';
import 'gifenc';
import '../image/pixels.js';
import '../webgl/GeometryBuilder.js';
import '../math/p5.Matrix.js';
import '../math/Matrices/Matrix.js';
import '../math/Matrices/MatrixInterface.js';
import '../webgl/p5.Geometry.js';
import '../webgl/p5.DataArray.js';
import '../webgl/p5.Quat.js';
import '../webgl/p5.RenderBuffer.js';
import '../webgl/ShapeBuilder.js';
import 'libtess';
import '../webgl/GeometryBufferCache.js';
import '../image/const.js';
import '../math/trigonometry.js';
import '../image/filterRenderer2D.js';

/**
 * @for p5
 * @requires core
 * These are functions that are part of the Processing API but are not part of
 * the p5.js API. In some cases they have a new name, in others, they are
 * removed completely. Not all unsupported Processing functions are listed here
 * but we try to include ones that a user coming from Processing might likely
 * call.
 */


p5.prototype.pushStyle = function() {
  throw new Error('pushStyle() not used, see push()');
};

p5.prototype.popStyle = function() {
  throw new Error('popStyle() not used, see pop()');
};

p5.prototype.popMatrix = function() {
  throw new Error('popMatrix() not used, see pop()');
};

p5.prototype.pushMatrix = function() {
  throw new Error('pushMatrix() not used, see push()');
};

export { p5 as default };
