html {
	background-color: #DDD;
}
body {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
	width: 900px;
	margin: 0 auto;
	font-family: Verdana, Helvetica, Arial, sans-serif;
	-webkit-box-shadow: 0 0 10px 2px rgba(0, 0, 0, .5);
	-moz-box-shadow: 0 0 10px 2px rgba(0, 0, 0, .5);
	box-shadow: 0 0 10px 2px rgba(0, 0, 0, .5);
	padding: 7px 25px 70px;
	background-color: #FFF;
}
h1, h2, h3, h4, h5, h6 {
	font-family: Georgia, "Times New Roman", serif;
}
h2, form {
	text-align: center;
}
form {
	margin-top: 5px;
}
.input {
	width: 500px;
	height: 300px;
	margin: 0 auto;
	display: block;
}
section {
	margin-top: 40px;
}
#canvas {
	cursor: crosshair;
}
#canvas, #html {
	border: 1px solid #000;
}
.filename {
	text-align: right;
}
#html {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
	overflow: auto;
	padding: 1em;
}
