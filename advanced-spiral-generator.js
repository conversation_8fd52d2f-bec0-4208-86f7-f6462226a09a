const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Gelişmiş spiral türleri ve komb<PERSON>yonları
const ADVANCED_SPIRAL_TYPES = [
  {name: 'Classic', weight: 0.2},
  {name: '<PERSON><PERSON><PERSON><PERSON>', weight: 0.15},
  {name: 'Archimedean', weight: 0.15},
  {name: 'Logarithmic', weight: 0.1},
  {name: 'Hyperbolic', weight: 0.1},
  {name: 'Fermat', weight: 0.1},
  {name: 'Dual Spiral', weight: 0.08},
  {name: 'Triple Spiral', weight: 0.05},
  {name: 'Nested Spirals', weight: 0.04},
  {name: 'Interference Pattern', weight: 0.03}
];

const ADVANCED_EFFECTS = [
  {name: 'None', weight: 0.15},
  {name: 'Particles', weight: 0.15},
  {name: 'Geometric', weight: 0.15},
  {name: 'Texture', weight: 0.15},
  {name: 'Gradient Flow', weight: 0.1},
  {name: 'Wave Distortion', weight: 0.1},
  {name: 'Fractal Overlay', weight: 0.08},
  {name: 'Light Rays', weight: 0.07},
  {name: 'Particle Storm', weight: 0.05}
];

const ENHANCED_PALETTES = [
  {name:'Muted Pastel', bg:'#f7f2ea', hues:[18,28,35,42,50], weight:0.15},
  {name:'Nocturne',     bg:'#111217', hues:[12,20,28,36,48], weight:0.15},
  {name:'Beachglass',   bg:'#f3f6f7', hues:[165,185,15,28,40], weight:0.15},
  {name:'Sepia Silk',   bg:'#f6efe2', hues:[16,22,26,30,34], weight:0.08},
  {name:'Cosmic Purple', bg:'#0a0a0f', hues:[260,280,300,320,340], weight:0.12},
  {name:'Forest Dream', bg:'#f8faf6', hues:[90,110,130,150,170], weight:0.08},
  {name:'Sunset Blaze', bg:'#1a0f0a', hues:[0,15,30,45,60], weight:0.1},
  {name:'Ocean Deep', bg:'#0a1a2e', hues:[180,200,220,240,260], weight:0.1},
  {name:'Aurora', bg:'#0f1419', hues:[120,140,160,280,300], weight:0.07}
];

// Rastgele sayı üretici sınıfı (geliştirilmiş)
class AdvancedSeededRandom {
  constructor(seed) {
    this.seed = seed;
    this.originalSeed = seed;
  }
  
  random() {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }
  
  randomRange(min, max) {
    return min + this.random() * (max - min);
  }
  
  randomInt(min, max) {
    return Math.floor(this.randomRange(min, max));
  }
  
  // Gelişmiş noise fonksiyonu
  noise(x, y = 0, z = 0) {
    let n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
    return (n - Math.floor(n));
  }
  
  // Perlin noise benzeri
  perlinNoise(x, y) {
    const xi = Math.floor(x);
    const yi = Math.floor(y);
    const xf = x - xi;
    const yf = y - yi;
    
    const a = this.noise(xi, yi);
    const b = this.noise(xi + 1, yi);
    const c = this.noise(xi, yi + 1);
    const d = this.noise(xi + 1, yi + 1);
    
    const i1 = this.interpolate(a, b, xf);
    const i2 = this.interpolate(c, d, xf);
    
    return this.interpolate(i1, i2, yf);
  }
  
  interpolate(a, b, t) {
    return a * (1 - t) + b * t;
  }
  
  // Simplex noise benzeri
  simplexNoise(x, y) {
    const F2 = 0.5 * (Math.sqrt(3.0) - 1.0);
    const G2 = (3.0 - Math.sqrt(3.0)) / 6.0;
    
    const s = (x + y) * F2;
    const i = Math.floor(x + s);
    const j = Math.floor(y + s);
    
    const t = (i + j) * G2;
    const X0 = i - t;
    const Y0 = j - t;
    const x0 = x - X0;
    const y0 = y - Y0;
    
    return this.noise(x0 * 100, y0 * 100) * 0.5 + 0.5;
  }
}

function weightedPick(list, rnd) {
  let sum = list.reduce((a,b)=>a+b.weight,0);
  let r = rnd()*sum, acc = 0;
  for (let item of list) {
    acc += item.weight;
    if (r <= acc) return item;
  }
}

// Gelişmiş spiral koordinat hesaplayıcıları
class SpiralCalculators {
  static fibonacci(r, t, tightness, rng) {
    const phi = 1.618034; // Golden ratio
    const angle = t + r * phi * tightness;
    const radius = Math.sqrt(r) * 8 * tightness;
    return {
      x: radius * Math.cos(angle),
      y: radius * Math.sin(angle)
    };
  }
  
  static archimedean(r, t, tightness, rng) {
    const a = 2.4 * tightness;
    const radius = a * r;
    return {
      x: radius * Math.cos(t),
      y: radius * Math.sin(t)
    };
  }
  
  static logarithmic(r, t, tightness, rng) {
    const a = 0.1 * tightness;
    const radius = Math.exp(a * r);
    return {
      x: radius * Math.cos(t),
      y: radius * Math.sin(t)
    };
  }
  
  static hyperbolic(r, t, tightness, rng) {
    const a = 50 * tightness;
    const radius = a / (r + 1);
    return {
      x: radius * Math.cos(t),
      y: radius * Math.sin(t)
    };
  }
  
  static fermat(r, t, tightness, rng) {
    const angle = t + Math.sqrt(r) * tightness * 2;
    const radius = Math.sqrt(r) * 6 * tightness;
    return {
      x: radius * Math.cos(angle),
      y: radius * Math.sin(angle)
    };
  }
  
  static dualSpiral(r, t, tightness, rng) {
    const spiral1 = this.fibonacci(r, t, tightness, rng);
    const spiral2 = this.logarithmic(r, t + Math.PI, tightness * 0.7, rng);
    
    const blend = 0.5 + 0.3 * Math.sin(r * 0.1);
    return {
      x: spiral1.x * blend + spiral2.x * (1 - blend),
      y: spiral1.y * blend + spiral2.y * (1 - blend)
    };
  }
  
  static tripleSpiral(r, t, tightness, rng) {
    const offset1 = 0;
    const offset2 = Math.PI * 2 / 3;
    const offset3 = Math.PI * 4 / 3;
    
    const spiral1 = this.fibonacci(r, t + offset1, tightness, rng);
    const spiral2 = this.fibonacci(r, t + offset2, tightness * 0.8, rng);
    const spiral3 = this.fibonacci(r, t + offset3, tightness * 0.6, rng);
    
    const weight = Math.sin(r * 0.05) * 0.3 + 0.7;
    return {
      x: (spiral1.x + spiral2.x * 0.7 + spiral3.x * 0.5) * weight / 2.2,
      y: (spiral1.y + spiral2.y * 0.7 + spiral3.y * 0.5) * weight / 2.2
    };
  }
  
  static nestedSpirals(r, t, tightness, rng) {
    const outerSpiral = this.logarithmic(r, t, tightness, rng);
    const innerSpiral = this.fibonacci(r * 0.3, t * 3, tightness * 2, rng);
    
    const blend = 0.7 + 0.3 * rng.perlinNoise(r * 0.01, t * 0.01);
    return {
      x: outerSpiral.x * blend + innerSpiral.x * (1 - blend),
      y: outerSpiral.y * blend + innerSpiral.y * (1 - blend)
    };
  }
  
  static interferencePattern(r, t, tightness, rng) {
    const wave1 = this.archimedean(r, t, tightness, rng);
    const wave2 = this.archimedean(r, t + Math.PI * 0.5, tightness * 0.8, rng);
    
    const interference = Math.sin(r * 0.2) * Math.cos(t * 3);
    const amplitude = 1 + interference * 0.3;
    
    return {
      x: (wave1.x + wave2.x * 0.6) * amplitude / 1.6,
      y: (wave1.y + wave2.y * 0.6) * amplitude / 1.6
    };
  }
}

// Gelişmiş efekt sınıfları
class AdvancedEffects {
  static gradientFlow(ctx, traits, pal, rng, centerX, centerY) {
    const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, traits.rings * 3);
    
    pal.hues.forEach((hue, index) => {
      const stop = index / (pal.hues.length - 1);
      const alpha = 0.1 + traits.gradientIntensity * 0.3;
      gradient.addColorStop(stop, `hsla(${hue * 10}, 70%, 60%, ${alpha})`);
    });
    
    ctx.fillStyle = gradient;
    ctx.fillRect(-centerX, -centerY, centerX * 2, centerY * 2);
  }
  
  static waveDistortion(ctx, traits, pal, rng) {
    const imageData = ctx.getImageData(-500, -500, 1000, 1000);
    const data = imageData.data;
    
    for (let x = 0; x < 1000; x += 2) {
      for (let y = 0; y < 1000; y += 2) {
        const wave = Math.sin(x * 0.01) * Math.cos(y * 0.01) * 20;
        const index = (x + y * 1000) * 4;
        
        if (index < data.length - 3) {
          data[index] += wave;     // R
          data[index + 1] += wave; // G
          data[index + 2] += wave; // B
        }
      }
    }
    
    ctx.putImageData(imageData, -500, -500);
  }
  
  static fractalOverlay(ctx, traits, pal, rng) {
    const iterations = 50;
    
    for (let i = 0; i < iterations; i++) {
      const angle = (i / iterations) * Math.PI * 2;
      const radius = traits.rings * (0.5 + 0.5 * Math.sin(i * 0.1));
      
      const x = radius * Math.cos(angle);
      const y = radius * Math.sin(angle);
      
      const size = 2 + 3 * Math.sin(i * 0.2);
      const hue = pal.hues[i % pal.hues.length] * 10;
      const alpha = 0.1 + 0.2 * Math.sin(i * 0.15);
      
      ctx.fillStyle = `hsla(${hue}, 80%, 70%, ${alpha})`;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fill();
    }
  }
  
  static lightRays(ctx, traits, pal, rng) {
    const rayCount = 12;
    
    for (let i = 0; i < rayCount; i++) {
      const angle = (i / rayCount) * Math.PI * 2;
      const length = traits.rings * 2;
      
      const gradient = ctx.createLinearGradient(
        0, 0,
        length * Math.cos(angle),
        length * Math.sin(angle)
      );
      
      const hue = pal.hues[i % pal.hues.length] * 10;
      gradient.addColorStop(0, `hsla(${hue}, 60%, 80%, 0.3)`);
      gradient.addColorStop(1, `hsla(${hue}, 60%, 80%, 0)`);
      
      ctx.strokeStyle = gradient;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(0, 0);
      ctx.lineTo(length * Math.cos(angle), length * Math.sin(angle));
      ctx.stroke();
    }
  }
  
  static particleStorm(ctx, traits, pal, rng) {
    const particleCount = 300;
    
    for (let i = 0; i < particleCount; i++) {
      const angle = rng.random() * Math.PI * 2;
      const radius = rng.randomRange(20, traits.rings * 3);
      const x = radius * Math.cos(angle);
      const y = radius * Math.sin(angle);
      
      const size = rng.randomRange(0.5, 3);
      const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
      const alpha = rng.randomRange(0.05, 0.25);
      
      // Parçacık hareketi simülasyonu
      const velocity = rng.randomRange(0.5, 2);
      const trail = 5;
      
      for (let t = 0; t < trail; t++) {
        const trailX = x - t * velocity * Math.cos(angle);
        const trailY = y - t * velocity * Math.sin(angle);
        const trailAlpha = alpha * (1 - t / trail);
        const trailSize = size * (1 - t / trail * 0.5);
        
        ctx.fillStyle = `hsla(${hue}, 70%, 60%, ${trailAlpha})`;
        ctx.beginPath();
        ctx.arc(trailX, trailY, trailSize, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }
}

// Ana spiral çizim fonksiyonu
function drawAdvancedSpiral(ctx, traits, pal, rng) {
  ctx.lineWidth = traits.strokeW;

  for (let r = 6; r < traits.rings; r++) {
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10 + rng.randomRange(-6, 6);

    // Gelişmiş gradyan renk hesaplama
    let saturation = 60 + traits.gradientIntensity * 30;
    let lightness = 55 + Math.sin(r * 0.1) * traits.gradientIntensity * 20;

    ctx.strokeStyle = `hsla(${hue}, ${saturation}%, ${lightness}%, ${traits.alpha})`;
    ctx.beginPath();

    let firstPoint = true;
    for (let i = 0; i < traits.steps; i++) {
      let t = i / traits.steps * Math.PI * 2;
      let coords = getAdvancedSpiralCoords(r, i, t, traits, rng);

      if (firstPoint) {
        ctx.moveTo(coords.x, coords.y);
        firstPoint = false;
      } else {
        ctx.lineTo(coords.x, coords.y);
      }
    }
    ctx.closePath();
    ctx.stroke();
  }
}

function getAdvancedSpiralCoords(r, i, t, traits, rng) {
  let baseCoords;

  // Spiral türüne göre koordinat hesapla
  switch(traits.spiralType) {
    case 'Fibonacci':
      baseCoords = SpiralCalculators.fibonacci(r, t, traits.spiralTightness, rng);
      break;
    case 'Archimedean':
      baseCoords = SpiralCalculators.archimedean(r, t, traits.spiralTightness, rng);
      break;
    case 'Logarithmic':
      baseCoords = SpiralCalculators.logarithmic(r, t, traits.spiralTightness, rng);
      break;
    case 'Hyperbolic':
      baseCoords = SpiralCalculators.hyperbolic(r, t, traits.spiralTightness, rng);
      break;
    case 'Fermat':
      baseCoords = SpiralCalculators.fermat(r, t, traits.spiralTightness, rng);
      break;
    case 'Dual Spiral':
      baseCoords = SpiralCalculators.dualSpiral(r, t, traits.spiralTightness, rng);
      break;
    case 'Triple Spiral':
      baseCoords = SpiralCalculators.tripleSpiral(r, t, traits.spiralTightness, rng);
      break;
    case 'Nested Spirals':
      baseCoords = SpiralCalculators.nestedSpirals(r, t, traits.spiralTightness, rng);
      break;
    case 'Interference Pattern':
      baseCoords = SpiralCalculators.interferencePattern(r, t, traits.spiralTightness, rng);
      break;
    default: // Classic
      baseCoords = SpiralCalculators.archimedean(r, t, 1.0, rng);
  }

  // Wobble efekti ekle
  let n = rng.perlinNoise(r * traits.flowFreqR, i * traits.flowFreqI);
  let wobbleX = traits.wobble * (n * 2 - 1) * baseCoords.x * 0.1;
  let wobbleY = traits.wobble * (n * 2 - 1) * baseCoords.y * 0.1;

  return {
    x: baseCoords.x + wobbleX,
    y: baseCoords.y + wobbleY
  };
}

// Gelişmiş efekt uygulayıcı
function applyAdvancedEffect(ctx, traits, pal, rng, centerX, centerY) {
  switch(traits.effect) {
    case 'Gradient Flow':
      AdvancedEffects.gradientFlow(ctx, traits, pal, rng, centerX, centerY);
      break;
    case 'Wave Distortion':
      AdvancedEffects.waveDistortion(ctx, traits, pal, rng);
      break;
    case 'Fractal Overlay':
      AdvancedEffects.fractalOverlay(ctx, traits, pal, rng);
      break;
    case 'Light Rays':
      AdvancedEffects.lightRays(ctx, traits, pal, rng);
      break;
    case 'Particle Storm':
      AdvancedEffects.particleStorm(ctx, traits, pal, rng);
      break;
  }
}

module.exports = {
  ADVANCED_SPIRAL_TYPES,
  ADVANCED_EFFECTS,
  ENHANCED_PALETTES,
  AdvancedSeededRandom,
  SpiralCalculators,
  AdvancedEffects,
  weightedPick,
  drawAdvancedSpiral,
  getAdvancedSpiralCoords,
  applyAdvancedEffect
};
