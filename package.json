{"name": "spiral-nft", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"generate": "node spiral.js", "analyze": "node rarity-analyzer.js", "full-generation": "npm run generate && npm run analyze", "batch": "node batch-generator.js", "batch-small": "node batch-generator.js 100", "batch-medium": "node batch-generator.js 1000", "batch-large": "node batch-generator.js 10000", "advanced": "node advanced-nft-generator.js", "quality-check": "node quality-control.js output", "quality-check-advanced": "node quality-control.js advanced-output", "full-pipeline": "npm run advanced && npm run quality-check-advanced", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"canvas": "^3.1.2", "fs": "^0.0.1-security", "gifenc": "^1.0.3", "p5": "^1.7.0", "path": "^0.12.7"}}