const fs = require('fs');
const path = require('path');

const OUTPUT_DIR = path.join(__dirname, 'output');

function analyzeRarity() {
  console.log('🔍 NFT Koleksiyonu Rarity Analizi Başlatılıyor...\n');
  
  // Tüm JSON dosyalarını oku
  const files = fs.readdirSync(OUTPUT_DIR).filter(file => file.endsWith('.json'));
  const nfts = [];
  
  for (const file of files) {
    const filePath = path.join(OUTPUT_DIR, file);
    const metadata = JSON.parse(fs.readFileSync(filePath, 'utf8'));
    nfts.push(metadata);
  }
  
  console.log(`📊 Toplam NFT Sayısı: ${nfts.length}\n`);
  
  // Trait analizi
  const traitAnalysis = analyzeTraits(nfts);
  
  // Rarity skorlarına göre sırala
  const sortedByRarity = nfts.sort((a, b) => {
    const aScore = a.attributes.find(attr => attr.trait_type === 'Rarity Score')?.value || 0;
    const bScore = b.attributes.find(attr => attr.trait_type === 'Rarity Score')?.value || 0;
    return bScore - aScore;
  });
  
  // En nadir 10 NFT
  console.log('🏆 EN NADİR 10 NFT:');
  console.log('='.repeat(50));
  for (let i = 0; i < Math.min(10, sortedByRarity.length); i++) {
    const nft = sortedByRarity[i];
    const rarityScore = nft.attributes.find(attr => attr.trait_type === 'Rarity Score')?.value || 0;
    const palette = nft.attributes.find(attr => attr.trait_type === 'Palette')?.value || 'Unknown';
    const spiralType = nft.attributes.find(attr => attr.trait_type === 'Spiral Type')?.value || 'Unknown';
    const effect = nft.attributes.find(attr => attr.trait_type === 'Effect')?.value || 'Unknown';
    
    console.log(`${i + 1}. ${nft.name}`);
    console.log(`   Rarity Score: ${rarityScore}`);
    console.log(`   Palette: ${palette} | Spiral: ${spiralType} | Effect: ${effect}`);
    console.log('');
  }
  
  // Trait dağılımı
  console.log('\n📈 TRAIT DAĞILIMI:');
  console.log('='.repeat(50));
  
  Object.entries(traitAnalysis).forEach(([traitType, values]) => {
    console.log(`\n${traitType}:`);
    Object.entries(values).forEach(([value, count]) => {
      const percentage = ((count / nfts.length) * 100).toFixed(1);
      console.log(`  ${value}: ${count} (${percentage}%)`);
    });
  });
  
  // Rarity tier analizi
  console.log('\n🎯 RARITY TIER DAĞILIMI:');
  console.log('='.repeat(50));
  
  const rarityTiers = {
    'Legendary (90-100)': 0,
    'Epic (80-89)': 0,
    'Rare (70-79)': 0,
    'Uncommon (60-69)': 0,
    'Common (0-59)': 0
  };
  
  nfts.forEach(nft => {
    const score = nft.attributes.find(attr => attr.trait_type === 'Rarity Score')?.value || 0;
    if (score >= 90) rarityTiers['Legendary (90-100)']++;
    else if (score >= 80) rarityTiers['Epic (80-89)']++;
    else if (score >= 70) rarityTiers['Rare (70-79)']++;
    else if (score >= 60) rarityTiers['Uncommon (60-69)']++;
    else rarityTiers['Common (0-59)']++;
  });
  
  Object.entries(rarityTiers).forEach(([tier, count]) => {
    const percentage = ((count / nfts.length) * 100).toFixed(1);
    console.log(`${tier}: ${count} (${percentage}%)`);
  });
  
  // Rarity raporu dosyası oluştur
  const report = {
    totalNFTs: nfts.length,
    analysisDate: new Date().toISOString(),
    topRarest: sortedByRarity.slice(0, 10).map(nft => ({
      name: nft.name,
      seed: nft.properties.seed,
      rarityScore: nft.attributes.find(attr => attr.trait_type === 'Rarity Score')?.value || 0,
      traits: nft.attributes.reduce((acc, attr) => {
        if (attr.trait_type !== 'Rarity Score') {
          acc[attr.trait_type] = attr.value;
        }
        return acc;
      }, {})
    })),
    traitDistribution: traitAnalysis,
    rarityTiers: rarityTiers
  };
  
  fs.writeFileSync(path.join(OUTPUT_DIR, 'rarity-report.json'), JSON.stringify(report, null, 2));
  console.log('\n✅ Rarity raporu output/rarity-report.json dosyasına kaydedildi.');
}

function analyzeTraits(nfts) {
  const traitAnalysis = {};
  
  nfts.forEach(nft => {
    nft.attributes.forEach(attr => {
      if (attr.trait_type === 'Rarity Score') return; // Rarity score'u analiz etme
      
      if (!traitAnalysis[attr.trait_type]) {
        traitAnalysis[attr.trait_type] = {};
      }
      
      const value = typeof attr.value === 'number' ? 
        (attr.value > 100 ? Math.floor(attr.value / 50) * 50 + '-' + (Math.floor(attr.value / 50) * 50 + 49) : attr.value.toString()) : 
        attr.value;
      
      if (!traitAnalysis[attr.trait_type][value]) {
        traitAnalysis[attr.trait_type][value] = 0;
      }
      traitAnalysis[attr.trait_type][value]++;
    });
  });
  
  return traitAnalysis;
}

// Analizi çalıştır
if (require.main === module) {
  analyzeRarity();
}

module.exports = { analyzeRarity };
