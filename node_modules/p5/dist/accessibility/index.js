import describe from './describe.js';
import gridOutput from './gridOutput.js';
import textOutput from './textOutput.js';
import outputs from './outputs.js';
import colorNamer from './color_namer.js';
import '../color/color_conversion.js';
import '../main-rEhlsQtb.js';
import '../constants-C2DVjshm.js';
import '../core/transform.js';
import '../core/structure.js';
import '../core/environment.js';
import '../math/p5.Vector.js';
import '../rendering-CpHn8PfG.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../dom/p5.Element.js';
import '../dom/p5.File.js';
import '../io/p5.XML.js';
import '../p5.Renderer-DO9wIL55.js';
import '../image/filters.js';
import '../shape/custom_shapes.js';
import '../core/States.js';
import '../io/utilities.js';
import 'file-saver';
import '../dom/p5.MediaElement.js';
import '../shape/2d_primitives.js';
import '../core/helpers.js';
import '../shape/attributes.js';
import '../shape/curves.js';
import '../shape/vertex.js';
import '../color/setting.js';
import 'omggif';
import '../io/csv.js';
import 'gifenc';
import '../image/pixels.js';
import '../webgl/GeometryBuilder.js';
import '../math/p5.Matrix.js';
import '../math/Matrices/Matrix.js';
import '../math/Matrices/MatrixInterface.js';
import '../webgl/p5.Geometry.js';
import '../webgl/p5.DataArray.js';
import '../webgl/p5.Quat.js';
import '../webgl/p5.RenderBuffer.js';
import '../webgl/ShapeBuilder.js';
import 'libtess';
import '../webgl/GeometryBufferCache.js';
import '../image/const.js';
import '../math/trigonometry.js';
import '../image/filterRenderer2D.js';

function accessibility(p5){
  p5.registerAddon(describe);
  p5.registerAddon(gridOutput);
  p5.registerAddon(textOutput);
  p5.registerAddon(outputs);
  p5.registerAddon(colorNamer);
}

export { accessibility as default };
