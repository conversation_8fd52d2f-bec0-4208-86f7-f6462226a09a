import { i as image$1, b as loadingDisplaying, s as shader, t as texture } from '../rendering-CpHn8PfG.js';
import { i as image$2 } from '../p5.Renderer-DO9wIL55.js';
import pixels from './pixels.js';
import '../constants-C2DVjshm.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../dom/p5.Element.js';
import '../dom/p5.File.js';
import '../io/p5.XML.js';
import '../dom/p5.MediaElement.js';
import '../shape/2d_primitives.js';
import '../core/helpers.js';
import '../shape/attributes.js';
import '../shape/curves.js';
import '../shape/vertex.js';
import '../color/setting.js';
import 'omggif';
import '../io/csv.js';
import '../io/utilities.js';
import 'file-saver';
import 'gifenc';
import '../core/transform.js';
import '../webgl/GeometryBuilder.js';
import '../math/p5.Matrix.js';
import '../math/Matrices/Matrix.js';
import '../math/p5.Vector.js';
import '../math/Matrices/MatrixInterface.js';
import '../webgl/p5.Geometry.js';
import '../webgl/p5.DataArray.js';
import '../webgl/p5.Quat.js';
import '../webgl/p5.RenderBuffer.js';
import '../webgl/ShapeBuilder.js';
import 'libtess';
import '../webgl/GeometryBufferCache.js';
import './const.js';
import '../shape/custom_shapes.js';
import '../math/trigonometry.js';
import './filters.js';
import '../core/States.js';

function image(p5){
  p5.registerAddon(image$1);
  p5.registerAddon(loadingDisplaying);
  p5.registerAddon(image$2);
  p5.registerAddon(pixels);
  p5.registerAddon(shader);
  p5.registerAddon(texture);
}

export { image as default };
