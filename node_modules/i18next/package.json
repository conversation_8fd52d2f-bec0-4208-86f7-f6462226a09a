{"name": "i18next", "version": "19.9.2", "description": "i18next internationalization framework", "main": "./dist/cjs/i18next.js", "module": "./dist/esm/i18next.js", "types": "./index.d.ts", "keywords": ["i18next", "internationalization", "i18n", "translation", "localization", "l10n", "globalization", "gettext"], "homepage": "https://www.i18next.com", "bugs": "https://github.com/i18next/i18next/issues", "repository": {"type": "git", "url": "https://github.com/i18next/i18next.git"}, "dependencies": {"@babel/runtime": "^7.12.0"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/plugin-proposal-async-generator-functions": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.3.2", "@babel/plugin-transform-modules-commonjs": "^7.2.0", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "@babel/preset-react": "^7.0.0", "@babel/register": "^7.0.0", "babel-eslint": "^10.0.1", "babelify": "^10.0.0", "browserify": "14.0.0", "browserify-istanbul": "2.0.0", "chai": "3.5.0", "coveralls": "2.11.16", "cpy-cli": "^2.0.0", "dtslint": "^0.4.2", "eslint": "3.15.0", "eslint-config-airbnb": "14.1.0", "eslint-config-prettier": "^3.6.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-jsx-a11y": "4.0.0", "eslint-plugin-react": "6.9.0", "husky": "^1.3.1", "i18next-browser-languagedetector": "1.0.1", "i18next-localstorage-cache": "0.3.0", "i18next-sprintf-postprocessor": "0.2.2", "i18next-xhr-backend": "1.3.0", "istanbul": "gotwarlost/istanbul#source-map", "karma": "2.0.0", "karma-browserify": "5.1.1", "karma-chai": "0.1.0", "karma-chrome-launcher": "2.2.0", "karma-cli": "1.0.1", "karma-coverage": "douglasduteil/karma-coverage#next", "karma-coveralls": "1.1.2", "karma-expect": "1.1.3", "karma-mocha": "1.3.0", "karma-rollup-preprocessor": "3.0.3", "karma-sinon": "1.0.5", "karma-spec-reporter": "0.0.26", "lint-staged": "^8.1.0", "mocha": "3.2.0", "prettier": "^1.15.3", "rimraf": "2.5.4", "rollup": "^2.34.1", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "rollup-plugin-terser": "^4.0.4", "sinon": "1.17.7", "tslint": "^5.12.1", "typescript": "^3.6.4", "watchify": "3.9.0"}, "scripts": {"pretest": "npm run test:typescript && npm run test:typescript:noninterop", "test": "npm run test:new && npm run test:compat", "test:new": "karma start karma.conf.js --singleRun", "test:compat": "karma start karma.backward.conf.js --singleRun", "test:typescript": "tslint --project tsconfig.json", "test:typescript:noninterop": "tslint --project tsconfig.nonEsModuleInterop.json", "tdd": "karma start karma.conf.js", "tdd:compat": "karma start karma.backward.conf.js", "build": "rimraf dist && rollup -c && cpy \"./dist/umd/*.js\" ./", "preversion": "npm run test && npm run build && git push", "postversion": "git push && git push --tags", "prettier": "prettier --write \"{,**/}*.{ts,tsx,js,json,md}\""}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"linters": {"*.{ts,tsx,js,json,md}": ["prettier --write", "git add"]}, "ignore": ["**/i18next.js", "**/i18next.min.js"]}}