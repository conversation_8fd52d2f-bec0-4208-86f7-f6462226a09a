!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.BezierPath=e():t.Bezier<PERSON>ath=e()}(self,(()=>(()=>{"use strict";var t={177:(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.<PERSON>=void 0;const i=s(558),h=s(188);e.<PERSON>=class{constructor(t){this.samples=[],this.segments=t;const e=t.map((t=>t.getTotalLength())),s=[0];for(let t=1;t<e.length;t++)s.push(s[t-1]+e[t-1]);this._totalLength=s[s.length-1]+e[e.length-1];const a=t.map((t=>Math.max(4,Math.ceil(t.getTotalLength()/i.BezierSegment.sampleSpacing())))),n=a.reduce(((t,e)=>t+e)),r=1/n/10,o=this._totalLength/n;this.samples.push({dist:0,pt:this.segments[0].A,tan:this.segments[0].tangentAtParameter(0),segIdx:0,t:0}),t.forEach(((t,e)=>{const i=a[e],n=(0,h.times)(i+1).map((t=>t/i)),x=n.map((e=>t.pointAtParameter(e)));let y;for(let e=0;e<4;e++){y=(0,h.times)(i).map((t=>Math.hypot(x[t+1].x-x[t].x,x[t+1].y-x[t].y)));const e=y.map((t=>t-o));let s=0;for(let i=1;i<n.length-1;i++)s+=e[i-1],n[i]-=r*s,x[i]=t.pointAtParameter(n[i])}let p=0;x.slice(1).forEach(((t,i)=>{p+=y[i],this.samples.push({dist:s[e]+p,pt:t,tan:this.segments[e].tangentAtParameter(n[i+1]),segIdx:e,t:n[i+1]})}))})),this._jumps=[],this.segmentStartEnds=[{start:0,end:0}];for(let t=1;t<this.samples.length;t++){const e=this.samples[t-1],s=this.samples[t];if(s.segIdx===e.segIdx?this.segmentStartEnds[this.segmentStartEnds.length-1].end=s.dist:this.segmentStartEnds[s.segIdx]={start:s.dist,end:s.dist},e.segIdx!==s.segIdx&&(this.segments[e.segIdx].D.x!==this.segments[s.segIdx].A.x||this.segments[e.segIdx].D.y!==this.segments[s.segIdx].A.y)){const i=(e.dist+s.dist)/2,h={dist:i-1e-8,pt:this.segments[e.segIdx].D,tan:this.segments[e.segIdx].tangentAtParameter(1),segIdx:e.segIdx,t:1},a={dist:i+1e-8,pt:this.segments[s.segIdx].A,tan:this.segments[s.segIdx].tangentAtParameter(0),segIdx:s.segIdx,t:0};this._jumps.push(i),this.samples.splice(t,0,h,a),t+=2}}}jumps(){return[...this._jumps]}getTotalLength(){return this._totalLength}findClosestSampleIdx(t){let e=0,s=this.samples.length-1;for(;e<s;){const i=Math.floor((e+s)/2);if(this.samples[i].dist>t)s=i-1;else{if(!(this.samples[i].dist<t))return i;e=i+1}}return Math.max(0,Math.min(this.samples.length-1,Math.floor((e+s)/2)))}getPointAtLength(t,e=!1){if(t<=0)return this.samples[0].pt;if(t>=this._totalLength)return this.samples[this.samples.length-1].pt;const s=this.findClosestSampleIdx(t),i=this.samples[s].dist<t?Math.min(s+1,this.samples.length-1):Math.max(0,s-1),h=Math.abs(this.samples[i].dist-this.samples[s].dist)<1e-6?0:(t-this.samples[s].dist)/(this.samples[i].dist-this.samples[s].dist);if(e||this.samples[s].segIdx>this.samples[i].segIdx)return{x:(1-h)*this.samples[s].pt.x+h*this.samples[i].pt.x,y:(1-h)*this.samples[s].pt.y+h*this.samples[i].pt.y};if(this.samples[s].segIdx!==this.samples[i].segIdx){if(h<.5){const t=this.segments[this.samples[s].segIdx],e=2*h,i=(1-e)*this.samples[s].t+e;return t.pointAtParameter(i)}{const t=this.segments[this.samples[i].segIdx],e=2*(h-.5)*this.samples[i].t;return t.pointAtParameter(e)}}{const t=this.segments[this.samples[s].segIdx],e=(1-h)*this.samples[s].t+h*this.samples[i].t;return t.pointAtParameter(e)}}getAngleAtLength(t,e=!1){const s=this.getTangentAtLength(t,e);return Math.atan2(s.y,s.x)}getTangentAtLength(t,e=!1){if(t<=0)return this.samples[0].tan;if(t>=this._totalLength)return this.samples[this.samples.length-1].tan;const s=this.findClosestSampleIdx(t),i=this.samples[s].dist<t?Math.min(s+1,this.samples.length-1):Math.max(0,s-1),h=(t-this.samples[s].dist)/(this.samples[i].dist-this.samples[s].dist);if(e||this.samples[s].segIdx>this.samples[i].segIdx){let t=(1-h)*this.samples[s].tan.x+h*this.samples[i].tan.x,e=(1-h)*this.samples[s].tan.y+h*this.samples[i].tan.y;const a=Math.max(Math.hypot(t,e),1e-4);return t/=a,e/=a,{x:t,y:e}}if(this.samples[s].segIdx!==this.samples[i].segIdx){if(h<.5){const t=this.segments[this.samples[s].segIdx],e=2*h,i=(1-e)*this.samples[s].t+e;return t.tangentAtParameter(i)}{const t=this.segments[this.samples[i].segIdx],e=2*(h-.5)*this.samples[i].t;return t.tangentAtParameter(e)}}{const t=this.segments[this.samples[s].segIdx],e=(1-h)*this.samples[s].t+h*this.samples[i].t;return t.tangentAtParameter(e)}}}},558:(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BezierSegment=void 0;const i=s(188);class h{constructor(t,e,s,i){this._totalLength=void 0,this.A=t,this.B=e,this.C=s,this.D=i}static sampleSpacing(){return 2}tangentAtParameter(t){const e=Math.max(0,Math.min(1,t));if(0===e||1===e){let t,s;0===e?this.A.x===this.B.x&&this.A.y===this.B.y?(t=this.C.x-this.A.x,s=this.C.y-this.A.y):(t=this.B.x-this.A.x,s=this.B.y-this.A.y):this.D.x===this.C.x&&this.D.y===this.C.y?(t=this.D.x-this.B.x,s=this.D.y-this.B.y):(t=this.D.x-this.C.x,s=this.D.y-this.C.y);const i=Math.hypot(t,s);return Math.abs(i)>1e-4&&(t/=i,s/=i),{x:t,y:s}}const s=1-e;let i=3*this.D.x*Math.pow(e,2)-3*this.C.x*Math.pow(e,2)+6*this.C.x*s*e-6*this.B.x*s*e+3*this.B.x*Math.pow(s,2)-3*this.A.x*Math.pow(s,2),h=3*this.D.y*Math.pow(e,2)-3*this.C.y*Math.pow(e,2)+6*this.C.y*s*e-6*this.B.y*s*e+3*this.B.y*Math.pow(s,2)-3*this.A.y*Math.pow(s,2);const a=Math.hypot(i,h);return Math.abs(a)>1e-4&&(i/=a,h/=a),{x:i,y:h}}isLinear(){return this.A.x===this.B.x&&this.A.y===this.B.y&&this.C.x===this.D.x&&this.C.y===this.D.y}pointAtParameter(t){const e=Math.max(0,Math.min(1,t));return{x:Math.pow(1-e,3)*this.A.x+3*Math.pow(1-e,2)*e*this.B.x+3*(1-e)*Math.pow(e,2)*this.C.x+Math.pow(e,3)*this.D.x,y:Math.pow(1-e,3)*this.A.y+3*Math.pow(1-e,2)*e*this.B.y+3*(1-e)*Math.pow(e,2)*this.C.y+Math.pow(e,3)*this.D.y}}getTotalLength(){if(void 0===this._totalLength)if(this.isLinear())this._totalLength=Math.hypot(this.D.x-this.A.x,this.D.y-this.A.y);else{const t=Math.max(10,Math.ceil((Math.hypot(this.B.x-this.A.x,this.B.y-this.A.y)+Math.hypot(this.C.x-this.B.x,this.C.y-this.B.y)+Math.hypot(this.D.x-this.C.x,this.D.y-this.C.y))/h.sampleSpacing())),e=(0,i.times)(t).map((e=>this.pointAtParameter(e/(t-1))));let s=0;for(let t=1;t<e.length;t++)s+=Math.hypot(e[t].x-e[t-1].x,e[t].y-e[t-1].y);this._totalLength=s}return this._totalLength}}e.BezierSegment=h},729:(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.createFromCommands=e.create=e.createFromElement=e.createFromCircle=e.createFromLine=e.createFromPath=void 0;const i=s(821),h=s(558),a={M:["x","y"],m:["dx","dy"],H:["x"],h:["dx"],V:["y"],v:["dy"],L:["x","y"],l:["dx","dy"],Z:[],C:["x1","y1","x2","y2","x","y"],c:["dx1","dy1","dx2","dy2","dx","dy"],S:["x2","y2","x","y"],s:["dx2","dy2","dx","dy"],Q:["x1","y1","x","y"],q:["dx1","dy1","dx","dy"],T:["x","y"],t:["dx","dy"],A:["rx","ry","rotation","large-arc","sweep","x","y"],a:["rx","ry","rotation","large-arc","sweep","dx","dy"]};e.createFromPath=t=>{const e=function(t){const e=t.replace(/[\n\r]/g,"").replace(/-/g," -").replace(/(\d*\.)(\d+)(?=\.)/g,"$1$2 ").replace(/(\d)([A-Za-z])/g,"$1 $2").replace(/([A-Za-z])(\d)/g,"$1 $2").trim().split(/\s*,|\s+/),s=[];let i="",h={};for(;e.length>0;){let t=e.shift();a.hasOwnProperty(t)?i=t:e.unshift(t),h={type:i},a[i].forEach((s=>{t=e.shift(),h[s]=parseFloat(t)})),"M"===i?i="L":"m"===i&&(i="l"),s.push(h)}return s}(t.getAttribute("d"));if(e.length<2)throw new Error(`Path doesn't have enough commands: ${JSON.stringify(e)}`);if("M"!==e[0].type)throw new Error(`Path starts with ${e[0].type} instead of M!`);let s={x:e[0].x,y:e[0].y};e.shift();const n=[];for(;e.length>0;){const t=e.shift();if("C"===t.type)n.push(new h.BezierSegment(s,{x:t.x1,y:t.y1},{x:t.x2,y:t.y2},{x:t.x,y:t.y})),s={x:t.x,y:t.y};else if("L"===t.type)n.push(new h.BezierSegment(s,s,{x:t.x,y:t.y},{x:t.x,y:t.y})),s={x:t.x,y:t.y};else if("H"===t.type)n.push(new h.BezierSegment(s,s,{x:t.x,y:s.y},{x:t.x,y:s.y})),s={x:t.x,y:s.y};else if("V"===t.type)n.push(new h.BezierSegment(s,s,{x:s.x,y:t.y},{x:s.x,y:t.y})),s={x:s.x,y:t.y};else if("Z"!==t.type)throw new Error(`Unsupported path command ${t.type}; use only H, V, M, L, C, Z!`)}return new i.BezierPath(n)},e.createFromLine=t=>{const[e,s,a,n]=["x1","x2","y1","y2"].map((e=>parseFloat(t.getAttribute(e)||"0")));return new i.BezierPath([new h.BezierSegment({x:e,y:a},{x:e,y:a},{x:s,y:n},{x:s,y:n})])},e.createFromCircle=t=>{const[e,s,a]=["cx","cy","r"].map((e=>parseFloat(t.getAttribute(e)||"0"))),n=1.3;return new i.BezierPath([new h.BezierSegment({x:e-a,y:s},{x:e-a,y:s-n*a},{x:e+a,y:s-n*a},{x:e+a,y:s}),new h.BezierSegment({x:e+a,y:s},{x:e+a,y:s+n*a},{x:e-a,y:s+n*a},{x:e-a,y:s})])},e.createFromElement=t=>{const s=t.tagName.toLowerCase();if("path"===s)return(0,e.createFromPath)(t);if("line"===s)return(0,e.createFromLine)(t);if("circle"===s)return(0,e.createFromCircle)(t);throw new Error(`Unsupported SVG tag: ${s}`)},e.create=t=>{const e=[];for(let s=1;s<t.length;s++){const i=t[s-1],a=t[s];e.push(new h.BezierSegment(i.pt,i.right||i.pt,a.left||a.pt,a.pt))}return new i.BezierPath(e)},e.createFromCommands=t=>{const e=t.slice();if(e.length<2)throw new Error(`Path doesn't have enough commands: ${JSON.stringify(e)}`);if("M"!==e[0].type)throw new Error(`Path starts with ${e[0].type} instead of M!`);let s={x:e[0].x,y:e[0].y},a=Object.assign({},s);const n=[];for(;e.length>0;){const t=e.shift();if("M"===t.type)a={x:t.x,y:t.y},s=a;else if("C"===t.type)n.push(new h.BezierSegment(s,{x:t.x1,y:t.y1},{x:t.x2,y:t.y2},{x:t.x,y:t.y})),s={x:t.x,y:t.y};else if("L"===t.type)t.x===s.x&&t.y===s.y||n.push(new h.BezierSegment(s,s,{x:t.x,y:t.y},{x:t.x,y:t.y})),s={x:t.x,y:t.y};else if("H"===t.type)t.x!==s.x&&n.push(new h.BezierSegment(s,s,{x:t.x,y:s.y},{x:t.x,y:s.y})),s={x:t.x,y:s.y};else if("V"===t.type)t.y!==s.y&&n.push(new h.BezierSegment(s,s,{x:s.x,y:t.y},{x:s.x,y:t.y})),s={x:s.x,y:t.y};else if("Q"===t.type)n.push(new h.BezierSegment(s,{x:s.x+2/3*(t.x1-s.x),y:s.y+2/3*(t.y1-s.y)},{x:t.x+2/3*(t.x1-t.x),y:t.y+2/3*(t.y1-t.y)},{x:t.x,y:t.y})),s={x:t.x,y:t.y};else{if("Z"!==t.type)throw new Error(`Unsupported path command ${t.type}; use only H, V, M, L, C, Z!`);Math.hypot(s.x-a.x,s.y-a.y)>0&&n.push(new h.BezierSegment(s,s,a,a))}}return new i.BezierPath(n)}},821:function(t,e,s){var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var h=Object.getOwnPropertyDescriptor(e,s);h&&!("get"in h?!e.__esModule:h.writable||h.configurable)||(h={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,h)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),h=this&&this.__exportStar||function(t,e){for(var s in t)"default"===s||Object.prototype.hasOwnProperty.call(e,s)||i(e,t,s)};Object.defineProperty(e,"__esModule",{value:!0}),h(s(177),e),h(s(558),e),h(s(729),e),h(s(856),e)},856:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0})},188:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.times=void 0,e.times=function(t){const e=[];for(let s=0;s<t;s++)e.push(s);return e}}},e={};return function s(i){var h=e[i];if(void 0!==h)return h.exports;var a=e[i]={exports:{}};return t[i].call(a.exports,a,a.exports,s),a.exports}(821)})()));