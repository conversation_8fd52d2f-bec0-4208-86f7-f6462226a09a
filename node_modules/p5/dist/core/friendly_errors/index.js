import fesCore from './fes_core.js';
import stacktrace from './stacktrace.js';
import validateParams from './param_validator.js';
import sketchVerifier from './sketch_verifier.js';
import fileErrors from './file_errors.js';
import '../internationalization.js';
import 'i18next';
import 'i18next-browser-languagedetector';
import './browser_errors.js';
import '../../constants-C2DVjshm.js';
import 'zod/v4';
import 'acorn';
import 'acorn-walk';

function friendlyErrors (p5) {
  p5.registerAddon(fesCore);
  p5.registerAddon(stacktrace);
  p5.registerAddon(validateParams);
  p5.registerAddon(sketchVerifier);
  p5.registerAddon(fileErrors);
}

export { friendlyErrors as default };
