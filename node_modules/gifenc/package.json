{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.3", "description": "very fast JS GIF encoder", "main": "./dist/gifenc.js", "browser": "./dist/gifenc.js", "source": "./src/index.js", "unpkg": "./dist/gifenc.esm.js", "jsdelivr": "./dist/gifenc.esm.js", "module": "./dist/gifenc.esm.js", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattdesl"}, "dependencies": {}, "devDependencies": {"ava": "^3.15.0", "canvas-sketch-util": "^1.10.0", "esbuild": "^0.8.52", "esm": "^3.2.25", "get-pixels": "^3.3.2", "serve": "^11.3.2"}, "scripts": {"prepublishOnly": "npm run dist:esm && npm run dist:cjs", "serve": "serve .", "dist:esm": "esbuild ./src/index.js --bundle --format=esm --outfile=dist/gifenc.esm.js --minify --sourcemap --target=chrome58,firefox57,safari11,edge16,node12", "dist:cjs": "esbuild ./src/index.js --bundle --format=cjs --outfile=dist/gifenc.js --sourcemap --target=chrome58,firefox57,safari11,edge16,node12"}, "ava": {"require": ["esm"], "ignoredByWatcher": ["test/output"]}, "keywords": ["gif", "encoder"], "repository": {"type": "git", "url": "git://github.com/mattdesl/gifenc.git"}, "homepage": "https://github.com/mattdesl/gifenc", "bugs": {"url": "https://github.com/mattdesl/gifenc/issues"}}