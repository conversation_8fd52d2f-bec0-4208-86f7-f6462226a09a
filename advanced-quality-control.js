const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');

// Advanced Quality Control System for NFT Generation
class AdvancedQualityControl {
  constructor(outputDir) {
    this.outputDir = outputDir;
    this.qualityMetrics = {
      visual: {},
      technical: {},
      artistic: {},
      rarity: {}
    };
    this.standards = this.defineQualityStandards();
  }

  // Define comprehensive quality standards
  defineQualityStandards() {
    return {
      technical: {
        minResolution: { width: 1000, height: 1000 },
        maxFileSize: 50 * 1024 * 1024, // 50MB
        requiredFormats: ['png', 'jpg'],
        colorDepth: 24,
        compressionQuality: 0.9
      },
      visual: {
        minContrast: 0.3,
        maxNoise: 0.1,
        minSaturation: 0.2,
        maxBrightness: 0.95,
        minBrightness: 0.05,
        colorBalance: { tolerance: 0.2 }
      },
      artistic: {
        compositionBalance: { tolerance: 0.3 },
        visualComplexity: { min: 0.2, max: 0.9 },
        symmetryScore: { min: 0.1, max: 0.8 },
        uniqueness: { threshold: 0.85 },
        aestheticScore: { min: 0.6 }
      },
      rarity: {
        minScore: 10,
        maxScore: 100,
        distributionBalance: true
      }
    };
  }

  // Main quality control function
  async runQualityControl() {
    console.log('🔍 Advanced Quality Control başlatılıyor...');
    console.log(`📁 Dizin: ${this.outputDir}`);

    if (!fs.existsSync(this.outputDir)) {
      throw new Error(`Output directory not found: ${this.outputDir}`);
    }

    const files = fs.readdirSync(this.outputDir);
    const imageFiles = files.filter(file => 
      file.toLowerCase().endsWith('.png') || file.toLowerCase().endsWith('.jpg')
    );
    const metadataFiles = files.filter(file => file.toLowerCase().endsWith('.json'));

    console.log(`🖼️  ${imageFiles.length} görsel dosyası bulundu`);
    console.log(`📄 ${metadataFiles.length} metadata dosyası bulundu`);

    const results = {
      totalFiles: imageFiles.length,
      passed: 0,
      failed: 0,
      warnings: 0,
      details: [],
      summary: {},
      recommendations: []
    };

    // Process each image file
    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i];
      console.log(`\n🔍 Analiz ediliyor: ${imageFile} (${i + 1}/${imageFiles.length})`);

      try {
        const analysis = await this.analyzeImage(imageFile);
        const metadataAnalysis = this.analyzeMetadata(imageFile);
        
        const overallScore = this.calculateOverallScore(analysis, metadataAnalysis);
        const qualityLevel = this.determineQualityLevel(overallScore);

        results.details.push({
          filename: imageFile,
          analysis: analysis,
          metadata: metadataAnalysis,
          overallScore: overallScore,
          qualityLevel: qualityLevel,
          passed: qualityLevel !== 'FAILED'
        });

        if (qualityLevel === 'FAILED') {
          results.failed++;
          console.log(`❌ BAŞARISIZ: ${imageFile} (Skor: ${overallScore.toFixed(2)})`);
        } else if (qualityLevel === 'WARNING') {
          results.warnings++;
          console.log(`⚠️  UYARI: ${imageFile} (Skor: ${overallScore.toFixed(2)})`);
        } else {
          results.passed++;
          console.log(`✅ BAŞARILI: ${imageFile} (Skor: ${overallScore.toFixed(2)})`);
        }

      } catch (error) {
        console.error(`❌ Hata: ${imageFile} - ${error.message}`);
        results.failed++;
        results.details.push({
          filename: imageFile,
          error: error.message,
          passed: false
        });
      }
    }

    // Generate summary and recommendations
    results.summary = this.generateSummary(results);
    results.recommendations = this.generateRecommendations(results);

    // Save quality report
    const reportPath = path.join(this.outputDir, 'quality-control-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));

    this.printQualityReport(results);
    return results;
  }

  // Analyze individual image
  async analyzeImage(filename) {
    const imagePath = path.join(this.outputDir, filename);
    const stats = fs.statSync(imagePath);
    
    // Load image for analysis
    const image = await loadImage(imagePath);
    const canvas = createCanvas(image.width, image.height);
    const ctx = canvas.getContext('2d');
    ctx.drawImage(image, 0, 0);
    
    const imageData = ctx.getImageData(0, 0, image.width, image.height);
    
    return {
      technical: this.analyzeTechnicalQuality(stats, image, imageData),
      visual: this.analyzeVisualQuality(imageData),
      artistic: this.analyzeArtisticQuality(imageData),
      uniqueness: this.analyzeUniqueness(imageData)
    };
  }

  // Technical quality analysis
  analyzeTechnicalQuality(stats, image, imageData) {
    const fileSize = stats.size;
    const resolution = { width: image.width, height: image.height };
    
    return {
      fileSize: fileSize,
      resolution: resolution,
      aspectRatio: resolution.width / resolution.height,
      pixelCount: resolution.width * resolution.height,
      fileSizeScore: this.scoreFileSize(fileSize),
      resolutionScore: this.scoreResolution(resolution),
      overallTechnicalScore: this.calculateTechnicalScore(fileSize, resolution)
    };
  }

  // Visual quality analysis
  analyzeVisualQuality(imageData) {
    const pixels = imageData.data;
    const pixelCount = pixels.length / 4;
    
    let totalBrightness = 0;
    let totalSaturation = 0;
    let colorVariance = 0;
    let contrastSum = 0;
    
    const colorHistogram = { r: new Array(256).fill(0), g: new Array(256).fill(0), b: new Array(256).fill(0) };
    
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      
      // Brightness calculation
      const brightness = (r + g + b) / (3 * 255);
      totalBrightness += brightness;
      
      // Saturation calculation
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const saturation = max === 0 ? 0 : (max - min) / max;
      totalSaturation += saturation;
      
      // Color histogram
      colorHistogram.r[r]++;
      colorHistogram.g[g]++;
      colorHistogram.b[b]++;
    }
    
    const avgBrightness = totalBrightness / pixelCount;
    const avgSaturation = totalSaturation / pixelCount;
    const contrast = this.calculateContrast(colorHistogram);
    const colorBalance = this.calculateColorBalance(colorHistogram);
    
    return {
      averageBrightness: avgBrightness,
      averageSaturation: avgSaturation,
      contrast: contrast,
      colorBalance: colorBalance,
      brightnessScore: this.scoreBrightness(avgBrightness),
      saturationScore: this.scoreSaturation(avgSaturation),
      contrastScore: this.scoreContrast(contrast),
      overallVisualScore: this.calculateVisualScore(avgBrightness, avgSaturation, contrast, colorBalance)
    };
  }

  // Artistic quality analysis
  analyzeArtisticQuality(imageData) {
    const complexity = this.calculateVisualComplexity(imageData);
    const symmetry = this.calculateSymmetry(imageData);
    const composition = this.analyzeComposition(imageData);
    const aestheticScore = this.calculateAestheticScore(complexity, symmetry, composition);
    
    return {
      visualComplexity: complexity,
      symmetryScore: symmetry,
      compositionBalance: composition,
      aestheticScore: aestheticScore,
      overallArtisticScore: (complexity + symmetry + composition + aestheticScore) / 4
    };
  }

  // Calculate visual complexity using edge detection
  calculateVisualComplexity(imageData) {
    const pixels = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    
    let edgeCount = 0;
    const threshold = 30;
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        const current = (pixels[idx] + pixels[idx + 1] + pixels[idx + 2]) / 3;
        
        // Check surrounding pixels
        const neighbors = [
          ((y - 1) * width + x) * 4,
          ((y + 1) * width + x) * 4,
          (y * width + (x - 1)) * 4,
          (y * width + (x + 1)) * 4
        ];
        
        for (const neighborIdx of neighbors) {
          const neighbor = (pixels[neighborIdx] + pixels[neighborIdx + 1] + pixels[neighborIdx + 2]) / 3;
          if (Math.abs(current - neighbor) > threshold) {
            edgeCount++;
            break;
          }
        }
      }
    }
    
    return Math.min(edgeCount / (width * height), 1);
  }

  // Calculate symmetry score
  calculateSymmetry(imageData) {
    const pixels = imageData.data;
    const width = imageData.width;
    const height = imageData.height;
    
    let symmetryScore = 0;
    let comparisons = 0;
    
    // Horizontal symmetry
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width / 2; x++) {
        const leftIdx = (y * width + x) * 4;
        const rightIdx = (y * width + (width - 1 - x)) * 4;
        
        const leftColor = [pixels[leftIdx], pixels[leftIdx + 1], pixels[leftIdx + 2]];
        const rightColor = [pixels[rightIdx], pixels[rightIdx + 1], pixels[rightIdx + 2]];
        
        const colorDiff = Math.sqrt(
          Math.pow(leftColor[0] - rightColor[0], 2) +
          Math.pow(leftColor[1] - rightColor[1], 2) +
          Math.pow(leftColor[2] - rightColor[2], 2)
        ) / (255 * Math.sqrt(3));
        
        symmetryScore += 1 - colorDiff;
        comparisons++;
      }
    }
    
    return symmetryScore / comparisons;
  }

  // Analyze composition using rule of thirds
  analyzeComposition(imageData) {
    const width = imageData.width;
    const height = imageData.height;
    const pixels = imageData.data;
    
    // Divide image into 9 sections (rule of thirds)
    const sectionWidth = width / 3;
    const sectionHeight = height / 3;
    const sectionBrightness = [];
    
    for (let sectionY = 0; sectionY < 3; sectionY++) {
      for (let sectionX = 0; sectionX < 3; sectionX++) {
        let totalBrightness = 0;
        let pixelCount = 0;
        
        const startX = Math.floor(sectionX * sectionWidth);
        const endX = Math.floor((sectionX + 1) * sectionWidth);
        const startY = Math.floor(sectionY * sectionHeight);
        const endY = Math.floor((sectionY + 1) * sectionHeight);
        
        for (let y = startY; y < endY; y++) {
          for (let x = startX; x < endX; x++) {
            const idx = (y * width + x) * 4;
            const brightness = (pixels[idx] + pixels[idx + 1] + pixels[idx + 2]) / (3 * 255);
            totalBrightness += brightness;
            pixelCount++;
          }
        }
        
        sectionBrightness.push(totalBrightness / pixelCount);
      }
    }
    
    // Calculate balance score
    const avgBrightness = sectionBrightness.reduce((a, b) => a + b, 0) / 9;
    const variance = sectionBrightness.reduce((sum, brightness) => 
      sum + Math.pow(brightness - avgBrightness, 2), 0) / 9;
    
    return 1 - Math.min(variance * 4, 1); // Normalize variance to 0-1 scale
  }

  // Calculate aesthetic score using golden ratio and other principles
  calculateAestheticScore(complexity, symmetry, composition) {
    // Weighted combination of aesthetic factors
    const complexityWeight = 0.3;
    const symmetryWeight = 0.3;
    const compositionWeight = 0.4;
    
    // Ideal complexity is around 0.6 (not too simple, not too chaotic)
    const idealComplexity = 0.6;
    const complexityScore = 1 - Math.abs(complexity - idealComplexity) / idealComplexity;
    
    return (complexityScore * complexityWeight + 
            symmetry * symmetryWeight + 
            composition * compositionWeight);
  }

  // Helper scoring functions
  scoreFileSize(fileSize) {
    const maxSize = this.standards.technical.maxFileSize;
    if (fileSize > maxSize) return 0;
    return Math.min(fileSize / (maxSize * 0.5), 1); // Optimal at 50% of max size
  }

  scoreResolution(resolution) {
    const minRes = this.standards.technical.minResolution;
    if (resolution.width < minRes.width || resolution.height < minRes.height) return 0;
    return 1; // Pass/fail for resolution
  }

  scoreBrightness(brightness) {
    const min = this.standards.visual.minBrightness;
    const max = this.standards.visual.maxBrightness;
    if (brightness < min || brightness > max) return 0;
    return 1 - Math.abs(brightness - 0.5) / 0.5; // Optimal at 0.5
  }

  scoreSaturation(saturation) {
    const min = this.standards.visual.minSaturation;
    if (saturation < min) return 0;
    return Math.min(saturation / 0.7, 1); // Optimal around 0.7
  }

  scoreContrast(contrast) {
    const min = this.standards.visual.minContrast;
    if (contrast < min) return 0;
    return Math.min(contrast / 0.8, 1); // Optimal around 0.8
  }

  // Calculate various metrics
  calculateContrast(colorHistogram) {
    // Simple contrast calculation based on color distribution
    const rVariance = this.calculateVariance(colorHistogram.r);
    const gVariance = this.calculateVariance(colorHistogram.g);
    const bVariance = this.calculateVariance(colorHistogram.b);
    
    return (rVariance + gVariance + bVariance) / (3 * 255 * 255);
  }

  calculateVariance(histogram) {
    const total = histogram.reduce((a, b) => a + b, 0);
    const mean = histogram.reduce((sum, count, value) => sum + count * value, 0) / total;
    
    return histogram.reduce((sum, count, value) => 
      sum + count * Math.pow(value - mean, 2), 0) / total;
  }

  calculateColorBalance(colorHistogram) {
    const rAvg = this.calculateMean(colorHistogram.r);
    const gAvg = this.calculateMean(colorHistogram.g);
    const bAvg = this.calculateMean(colorHistogram.b);
    
    const overall = (rAvg + gAvg + bAvg) / 3;
    const balance = 1 - (Math.abs(rAvg - overall) + Math.abs(gAvg - overall) + Math.abs(bAvg - overall)) / (3 * 255);
    
    return balance;
  }

  calculateMean(histogram) {
    const total = histogram.reduce((a, b) => a + b, 0);
    return histogram.reduce((sum, count, value) => sum + count * value, 0) / total;
  }

  // Calculate overall scores
  calculateTechnicalScore(fileSize, resolution) {
    const fileSizeScore = this.scoreFileSize(fileSize);
    const resolutionScore = this.scoreResolution(resolution);
    return (fileSizeScore + resolutionScore) / 2;
  }

  calculateVisualScore(brightness, saturation, contrast, colorBalance) {
    const brightnessScore = this.scoreBrightness(brightness);
    const saturationScore = this.scoreSaturation(saturation);
    const contrastScore = this.scoreContrast(contrast);
    const balanceScore = colorBalance;
    
    return (brightnessScore + saturationScore + contrastScore + balanceScore) / 4;
  }

  calculateOverallScore(analysis, metadataAnalysis) {
    const weights = {
      technical: 0.2,
      visual: 0.3,
      artistic: 0.4,
      metadata: 0.1
    };
    
    const technicalScore = analysis.technical.overallTechnicalScore;
    const visualScore = analysis.visual.overallVisualScore;
    const artisticScore = analysis.artistic.overallArtisticScore;
    const metadataScore = metadataAnalysis ? metadataAnalysis.completenessScore : 0.5;
    
    return (technicalScore * weights.technical +
            visualScore * weights.visual +
            artisticScore * weights.artistic +
            metadataScore * weights.metadata);
  }

  determineQualityLevel(score) {
    if (score >= 0.8) return 'EXCELLENT';
    if (score >= 0.6) return 'GOOD';
    if (score >= 0.4) return 'WARNING';
    return 'FAILED';
  }

  // Analyze metadata
  analyzeMetadata(imageFile) {
    const metadataFile = imageFile.replace(/\.(png|jpg)$/i, '.json');
    const metadataPath = path.join(this.outputDir, metadataFile);
    
    if (!fs.existsSync(metadataPath)) {
      return { exists: false, completenessScore: 0 };
    }
    
    try {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      return this.validateMetadata(metadata);
    } catch (error) {
      return { exists: true, valid: false, error: error.message, completenessScore: 0 };
    }
  }

  validateMetadata(metadata) {
    const requiredFields = ['name', 'description', 'image', 'attributes'];
    const optionalFields = ['external_url', 'properties'];
    
    let score = 0;
    const issues = [];
    
    // Check required fields
    for (const field of requiredFields) {
      if (metadata[field]) {
        score += 0.2;
      } else {
        issues.push(`Missing required field: ${field}`);
      }
    }
    
    // Check optional fields
    for (const field of optionalFields) {
      if (metadata[field]) {
        score += 0.1;
      }
    }
    
    // Validate attributes
    if (metadata.attributes && Array.isArray(metadata.attributes)) {
      if (metadata.attributes.length >= 5) {
        score += 0.1;
      }
    } else {
      issues.push('Attributes should be an array with at least 5 items');
    }
    
    return {
      exists: true,
      valid: issues.length === 0,
      completenessScore: Math.min(score, 1),
      issues: issues
    };
  }

  analyzeUniqueness(imageData) {
    // Simple uniqueness check based on color distribution
    const colorSignature = this.generateColorSignature(imageData);
    return {
      colorSignature: colorSignature,
      uniquenessScore: 0.8 // Placeholder - would need comparison with other images
    };
  }

  generateColorSignature(imageData) {
    const pixels = imageData.data;
    const signature = [];
    
    // Sample every 100th pixel for signature
    for (let i = 0; i < pixels.length; i += 400) { // Every 100th pixel * 4 channels
      signature.push([pixels[i], pixels[i + 1], pixels[i + 2]]);
    }
    
    return signature.slice(0, 50); // Keep first 50 samples
  }

  generateSummary(results) {
    const passRate = (results.passed / results.totalFiles) * 100;
    const avgScore = results.details.reduce((sum, detail) => 
      sum + (detail.overallScore || 0), 0) / results.details.length;
    
    return {
      totalFiles: results.totalFiles,
      passRate: passRate,
      averageScore: avgScore,
      qualityDistribution: this.calculateQualityDistribution(results.details)
    };
  }

  calculateQualityDistribution(details) {
    const distribution = { EXCELLENT: 0, GOOD: 0, WARNING: 0, FAILED: 0 };
    
    details.forEach(detail => {
      if (detail.qualityLevel) {
        distribution[detail.qualityLevel]++;
      }
    });
    
    return distribution;
  }

  generateRecommendations(results) {
    const recommendations = [];
    
    if (results.summary.passRate < 80) {
      recommendations.push('Overall pass rate is low. Consider adjusting generation parameters.');
    }
    
    if (results.summary.averageScore < 0.6) {
      recommendations.push('Average quality score is below acceptable threshold. Review artistic algorithms.');
    }
    
    const failedCount = results.summary.qualityDistribution.FAILED;
    if (failedCount > 0) {
      recommendations.push(`${failedCount} files failed quality control. Manual review recommended.`);
    }
    
    return recommendations;
  }

  printQualityReport(results) {
    console.log('\n📊 QUALITY CONTROL REPORT');
    console.log('========================');
    console.log(`📁 Total Files: ${results.totalFiles}`);
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`⚠️  Warnings: ${results.warnings}`);
    console.log(`📈 Pass Rate: ${results.summary.passRate.toFixed(1)}%`);
    console.log(`🎯 Average Score: ${results.summary.averageScore.toFixed(2)}`);
    
    console.log('\n📊 Quality Distribution:');
    Object.entries(results.summary.qualityDistribution).forEach(([level, count]) => {
      console.log(`  ${level}: ${count}`);
    });
    
    if (results.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      results.recommendations.forEach(rec => console.log(`  • ${rec}`));
    }
  }
}

// CLI usage
async function runQualityControl() {
  const outputDir = process.argv[2] || './output';
  
  try {
    const qc = new AdvancedQualityControl(outputDir);
    await qc.runQualityControl();
  } catch (error) {
    console.error('❌ Quality control failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  runQualityControl();
}

module.exports = { AdvancedQualityControl };
