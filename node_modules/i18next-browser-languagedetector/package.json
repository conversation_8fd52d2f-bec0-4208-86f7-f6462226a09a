{"name": "i18next-browser-languagedetector", "version": "4.3.1", "description": "language detector used in browser environment for i18next", "main": "./dist/cjs/i18nextBrowserLanguageDetector.js", "module": "./dist/esm/i18nextBrowserLanguageDetector.js", "types": "./index.d.ts", "keywords": ["i18next", "i18next-languageDetector"], "homepage": "https://github.com/i18next/i18next-browser-languageDetector", "bugs": "https://github.com/i18next/i18next-browser-languageDetector/issues", "repository": {"type": "git", "url": "https://github.com/i18next/i18next-browser-languageDetector.git"}, "dependencies": {"@babel/runtime": "^7.5.5"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "babel-eslint": "^10.0.2", "babel-polyfill": "^6.26.0", "babelify": "^10.0.0", "browserify": "16.3.0", "browserify-istanbul": "3.0.1", "chai": "4.2.0", "coveralls": "3.0.5", "cpy-cli": "^2.0.0", "dtslint": "^0.9.1", "eslint": "6.1.0", "eslint-config-airbnb": "17.1.1", "expect.js": "0.3.1", "i18next": "^19.0.0", "mkdirp": "0.5.1", "mocha": "7.1.2", "rimraf": "2.6.3", "rollup": "^1.18.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "tslint": "^5.18.0", "typescript": "^3.5.3", "yargs": "13.3.0"}, "scripts": {"pretest": "npm run test:typescript && npm run test:typescript:noninterop", "test": "npm run build && mocha test -R spec --exit", "test:typescript": "tslint --project tsconfig.json", "test:typescript:noninterop": "tslint --project tsconfig.nonEsModuleInterop.json", "build": "rimraf dist && rollup -c && cpy \"./dist/umd/*.js\" ./", "preversion": "npm run build && git push", "postversion": "git push && git push --tags"}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT"}