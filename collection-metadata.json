{"name": "Spiral Genesis Collection", "description": "A generative art collection featuring advanced spiral algorithms with dynamic effects. Each piece is uniquely generated using mathematical principles including <PERSON><PERSON><PERSON><PERSON> sequences, Archimedean spirals, and logarithmic curves combined with particle systems, geometric overlays, and texture effects.", "image": "https://your-website.com/collection-banner.png", "external_link": "https://your-website.com", "seller_fee_basis_points": 750, "fee_recipient": "0x0000000000000000000000000000000000000000", "properties": {"category": "art", "files": [{"uri": "collection-banner.png", "type": "image/png"}], "creators": [{"address": "0x0000000000000000000000000000000000000000", "share": 100}]}, "attributes": {"trait_types": {"Palette": {"type": "string", "values": ["Muted <PERSON>", "Nocturne", "Beachglass", "<PERSON><PERSON>", "Cosmic Purple", "Forest Dream"]}, "Spiral Type": {"type": "string", "values": ["Classic", "<PERSON><PERSON><PERSON><PERSON>", "Archimedean", "Logarithmic"]}, "Effect": {"type": "string", "values": ["None", "Particles", "Geometric", "Texture"]}, "Ring Count": {"type": "number", "min": 360, "max": 520}, "Rarity Score": {"type": "number", "min": 0, "max": 100}}}, "collection": {"name": "Spiral Genesis", "family": "Generative Art"}}