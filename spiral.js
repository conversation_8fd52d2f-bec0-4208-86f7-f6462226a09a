// generate.js
const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Ayarlar
const OUTPUT_DIR = path.join(__dirname, 'output');
const NFT_COUNT = 50;
const WIDTH = 3000;
const HEIGHT = 4000;

const PALETTES = [
  {name:'Muted Pastel', bg:'#f7f2ea', hues:[18,28,35,42,50], weight:0.25},
  {name:'Nocturne',     bg:'#111217', hues:[12,20,28,36,48], weight:0.2},
  {name:'Beachglass',   bg:'#f3f6f7', hues:[165,185,15,28,40], weight:0.2},
  {name:'<PERSON><PERSON>',   bg:'#f6efe2', hues:[16,22,26,30,34], weight:0.1},
  {name:'Cosmic Purple', bg:'#0a0a0f', hues:[260,280,300,320,340], weight:0.15},
  {name:'Forest Dream', bg:'#f8faf6', hues:[90,110,130,150,170], weight:0.1},
];

const SPIRAL_TYPES = [
  {name: 'Classic', weight: 0.4},
  {name: 'Fibonacci', weight: 0.25},
  {name: 'Archimedean', weight: 0.2},
  {name: 'Logarithmic', weight: 0.15}
];

const EFFECTS = [
  {name: 'None', weight: 0.3},
  {name: 'Particles', weight: 0.25},
  {name: 'Geometric', weight: 0.25},
  {name: 'Texture', weight: 0.2}
];

function weightedPick(list, rnd) {
  let sum = list.reduce((a,b)=>a+b.weight,0);
  let r = rnd()*sum, acc = 0;
  for (let item of list) {
    acc += item.weight;
    if (r <= acc) return item;
  }
}

function ensureDir(dir) {
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}
ensureDir(OUTPUT_DIR);

// Rastgele sayı üretici sınıfı
class SeededRandom {
  constructor(seed) {
    this.seed = seed;
  }

  random() {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }

  randomRange(min, max) {
    return min + this.random() * (max - min);
  }

  randomInt(min, max) {
    return Math.floor(this.randomRange(min, max));
  }

  noise(x, y = 0, z = 0) {
    // Basit noise fonksiyonu
    let n = Math.sin(x * 12.9898 + y * 78.233 + z * 37.719) * 43758.5453;
    return (n - Math.floor(n));
  }
}

let SEED, traits, pal, rng;

function generateNFTs() {
  for (let i = 0; i < NFT_COUNT; i++) {
    SEED = Math.floor(Math.random() * 1e9);
    rng = new SeededRandom(SEED);
    makeTraits();
    drawArt();
    saveFiles();
    console.log(`✅ NFT ${i+1}/${NFT_COUNT} üretildi: Spiral #${SEED}`);
  }
}

function makeTraits() {
  pal = weightedPick(PALETTES, () => rng.random());
  const spiralType = weightedPick(SPIRAL_TYPES, () => rng.random());
  const effect = weightedPick(EFFECTS, () => rng.random());

  traits = {
    seed: SEED,
    palette: pal.name,
    spiralType: spiralType.name,
    effect: effect.name,
    rings: rng.random() < 0.3 ? rng.randomInt(480,520) : rng.randomInt(360,380),
    steps: rng.randomInt(70,110),
    wobble: rng.random() < 0.2 ? rng.randomRange(0.14,0.16) : rng.randomRange(0.08,0.12),
    flowFreqR: rng.randomRange(0.015,0.028),
    flowFreqI: rng.randomRange(0.015,0.028),
    swirlOffset: rng.randomRange(0.42,0.62),
    strokeW: rng.random() < 0.3 ? rng.randomRange(2.0,2.6) : rng.randomRange(1.4,1.8),
    alpha: rng.randomRange(0.28,0.52),
    // Yeni özellikler
    gradientIntensity: rng.randomRange(0.1, 0.8),
    particleCount: effect.name === 'Particles' ? rng.randomInt(50, 200) : 0,
    geometricDensity: effect.name === 'Geometric' ? rng.randomRange(0.1, 0.4) : 0,
    textureScale: effect.name === 'Texture' ? rng.randomRange(0.005, 0.02) : 0,
    spiralTightness: spiralType.name === 'Fibonacci' ? rng.randomRange(0.1, 0.3) :
                    spiralType.name === 'Logarithmic' ? rng.randomRange(0.05, 0.15) :
                    rng.randomRange(0.8, 1.2),
    centerOffset: {x: rng.randomRange(-0.1, 0.1), y: rng.randomRange(-0.1, 0.1)}
  };
}

let canvas, ctx;

function drawArt() {
  canvas = createCanvas(WIDTH, HEIGHT);
  ctx = canvas.getContext('2d');

  // Arka plan
  ctx.fillStyle = pal.bg;
  ctx.fillRect(0, 0, WIDTH, HEIGHT);

  // Merkez noktasını ayarla
  const centerX = WIDTH * (0.5 + traits.centerOffset.x);
  const centerY = HEIGHT * (traits.swirlOffset + traits.centerOffset.y);

  ctx.save();
  ctx.translate(centerX, centerY);

  // Doku efekti için arka plan
  if (traits.effect === 'Texture') {
    drawTextureBackground();
  }

  // Ana spiral çizimi
  drawSpiral();

  // Efekt katmanları
  if (traits.effect === 'Particles') {
    drawParticles();
  } else if (traits.effect === 'Geometric') {
    drawGeometricElements();
  }

  ctx.restore();
}

function drawSpiral() {
  ctx.lineWidth = traits.strokeW;
  ctx.fillStyle = 'transparent';

  for (let r = 6; r < traits.rings; r++) {
    let base = getSpiralRadius(r, traits.spiralType);
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10 + rng.randomRange(-6, 6);

    // Gradyan renk hesaplama
    let saturation = 60 + traits.gradientIntensity * 30;
    let lightness = 55 + Math.sin(r * 0.1) * traits.gradientIntensity * 20;

    ctx.strokeStyle = `hsla(${hue}, ${saturation}%, ${lightness}%, ${traits.alpha})`;
    ctx.beginPath();

    let firstPoint = true;
    for (let i = 0; i < traits.steps; i++) {
      let t = i / traits.steps * Math.PI * 2;
      let coords = getSpiralCoords(r, i, t, base);

      if (firstPoint) {
        ctx.moveTo(coords.x, coords.y);
        firstPoint = false;
      } else {
        ctx.lineTo(coords.x, coords.y);
      }
    }
    ctx.closePath();
    ctx.stroke();
  }
}

  function getSpiralRadius(r, spiralType) {
    switch(spiralType) {
      case 'Fibonacci':
        return r * r * 0.1 * traits.spiralTightness;
      case 'Archimedean':
        return r * 2.4 * traits.spiralTightness;
      case 'Logarithmic':
        return Math.exp(r * 0.1) * traits.spiralTightness;
      default: // Classic
        return r * 2.4;
    }
  }

function getSpiralCoords(r, i, t, base) {
  let n = rng.noise(r * traits.flowFreqR, i * traits.flowFreqI);
  let rr = base * (1 + traits.wobble * (n * 2 - 1));

  if (traits.spiralType === 'Fibonacci') {
    // Fibonacci spiral için özel hesaplama
    let angle = t + r * 0.618034; // Golden ratio
    return {
      x: rr * Math.cos(angle),
      y: rr * Math.sin(angle)
    };
  } else if (traits.spiralType === 'Logarithmic') {
    // Logaritmik spiral için özel hesaplama
    let angle = t * (1 + r * 0.1);
    return {
      x: rr * Math.cos(angle),
      y: rr * Math.sin(angle)
    };
  } else {
    // Klasik ve Archimedean spiral
    return {
      x: rr * Math.cos(t),
      y: rr * Math.sin(t)
    };
  }
}

function drawParticles() {
  for (let i = 0; i < traits.particleCount; i++) {
    let angle = rng.random() * Math.PI * 2;
    let radius = rng.randomRange(50, traits.rings * 2);
    let x = radius * Math.cos(angle);
    let y = radius * Math.sin(angle);

    // Parçacık boyutu ve opaklığı
    let size = rng.randomRange(1, 4);
    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
    let alpha = rng.randomRange(0.1, 0.4);

    ctx.fillStyle = `hsla(${hue}, 70%, 60%, ${alpha})`;
    ctx.beginPath();
    ctx.arc(x, y, size/2, 0, Math.PI * 2);
    ctx.fill();
  }
}

function drawGeometricElements() {
  ctx.lineWidth = 1;

  let elementCount = traits.geometricDensity * 100;

  for (let i = 0; i < elementCount; i++) {
    let angle = (i / elementCount) * Math.PI * 2;
    let radius = rng.randomRange(100, traits.rings * 1.5);
    let x = radius * Math.cos(angle);
    let y = radius * Math.sin(angle);

    let hue = pal.hues[Math.floor(rng.random() * pal.hues.length)] * 10;
    ctx.strokeStyle = `hsla(${hue}, 50%, 50%, 0.3)`;

    // Rastgele geometrik şekil seç
    let shapes = ['triangle', 'square', 'hexagon'];
    let shapeType = shapes[Math.floor(rng.random() * shapes.length)];
    drawGeometricShape(x, y, shapeType, rng.randomRange(5, 15));
  }
}

function drawGeometricShape(x, y, type, size) {
  ctx.save();
  ctx.translate(x, y);
  ctx.rotate(rng.random() * Math.PI * 2);

  ctx.beginPath();
  switch(type) {
    case 'triangle':
      ctx.moveTo(-size/2, size/2);
      ctx.lineTo(size/2, size/2);
      ctx.lineTo(0, -size/2);
      ctx.closePath();
      break;
    case 'square':
      ctx.rect(-size/2, -size/2, size, size);
      break;
    case 'hexagon':
      for (let i = 0; i < 6; i++) {
        let angle = (i / 6) * Math.PI * 2;
        let px = size/2 * Math.cos(angle);
        let py = size/2 * Math.sin(angle);
        if (i === 0) ctx.moveTo(px, py);
        else ctx.lineTo(px, py);
      }
      ctx.closePath();
      break;
  }
  ctx.stroke();
  ctx.restore();
}

function drawTextureBackground() {
  const imageData = ctx.getImageData(0, 0, WIDTH, HEIGHT);
  const data = imageData.data;

  for (let x = 0; x < WIDTH; x += 2) {
    for (let y = 0; y < HEIGHT; y += 2) {
      let noiseVal = rng.noise(x * traits.textureScale, y * traits.textureScale);
      let brightness = noiseVal * 50;

      let index = (x + y * WIDTH) * 4;
      if (index < data.length - 3) {
        data[index] += brightness;     // R
        data[index + 1] += brightness; // G
        data[index + 2] += brightness; // B
      }
    }
  }

  ctx.putImageData(imageData, 0, 0);
}

function saveFiles() {
  const imgBuffer = canvas.toBuffer('image/png');
  fs.writeFileSync(path.join(OUTPUT_DIR, `spiral_${SEED}.png`), imgBuffer);

    // Rarity skorunu hesapla
    const rarityScore = calculateRarity();

    const meta = {
      name: `Spiral #${SEED}`,
      description: "Advanced generative spiral art with dynamic effects and multiple spiral algorithms",
      image: `spiral_${SEED}.png`,
      external_url: `https://your-website.com/spiral/${SEED}`,
      attributes: [
        {trait_type: "Palette", value: traits.palette},
        {trait_type: "Spiral Type", value: traits.spiralType},
        {trait_type: "Effect", value: traits.effect},
        {trait_type: "Ring Count", value: traits.rings},
        {trait_type: "Stroke Weight", value: parseFloat(traits.strokeW.toFixed(2))},
        {trait_type: "Wobble Intensity", value: parseFloat(traits.wobble.toFixed(3))},
        {trait_type: "Gradient Intensity", value: parseFloat(traits.gradientIntensity.toFixed(2))},
        {trait_type: "Rarity Score", value: rarityScore, display_type: "number"}
      ],
      properties: {
        seed: SEED,
        algorithm: "Advanced Spiral Generator v2.0",
        resolution: `${WIDTH}x${HEIGHT}`,
        created: new Date().toISOString()
      }
    };

    fs.writeFileSync(path.join(OUTPUT_DIR, `spiral_${SEED}.json`), JSON.stringify(meta, null, 2));
  }

  function calculateRarity() {
    let score = 0;

    // Spiral türü nadirligi
    if (traits.spiralType === 'Logarithmic') score += 40;
    else if (traits.spiralType === 'Fibonacci') score += 30;
    else if (traits.spiralType === 'Archimedean') score += 20;
    else score += 10;

    // Efekt nadirligi
    if (traits.effect === 'Texture') score += 35;
    else if (traits.effect === 'Geometric') score += 25;
    else if (traits.effect === 'Particles') score += 20;
    else score += 5;

    // Renk paleti nadirligi
    if (traits.palette === 'Sepia Silk') score += 30;
    else if (traits.palette === 'Cosmic Purple') score += 25;
    else if (traits.palette === 'Forest Dream') score += 20;
    else score += 10;

    // Özel özellikler
    if (traits.rings > 500) score += 15;
    if (traits.wobble > 0.14) score += 10;
    if (traits.gradientIntensity > 0.7) score += 10;
    if (traits.strokeW > 2.0) score += 8;

    return Math.min(score, 100); // Maksimum 100 puan
  }

// NFT üretimini başlat
generateNFTs();