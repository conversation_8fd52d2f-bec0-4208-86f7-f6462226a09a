import dom$1 from './dom.js';
import element from './p5.Element.js';
import media from './p5.MediaElement.js';
import file from './p5.File.js';
import '../creating_reading-BdolPjuO.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../constants-C2DVjshm.js';
import '../io/p5.XML.js';

function dom(p5){
  p5.registerAddon(dom$1);
  p5.registerAddon(element);
  p5.registerAddon(media);
  p5.registerAddon(file);
}

export { dom as default };
