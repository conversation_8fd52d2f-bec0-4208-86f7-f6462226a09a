const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const {
  PS5_CONFIG,
  PS5_SPIRAL_TYPES,
  PS5_EFFECTS,
  PS5_HDR_PALETTES,
  PS5SeededRandom,
  PS5SpiralCalculators,
  PS5EffectsEngine,
  makePS5Traits,
  weightedPick
} = require('./ps5-advanced-nft-generator');

function ensureDir(dir) {
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
}

// PS5 GPU Accelerated Spiral Drawing
function drawPS5Spiral(ctx, traits, pal, rng) {
  const spiralFunc = getSpiralFunction(traits.spiralType);
  const centerX = 0;
  const centerY = 0;
  
  // HDR color enhancement
  const hdrMultiplier = traits.hdrIntensity;
  
  // Anti-aliasing setup for PS5
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  // Draw multiple layers for depth
  const layers = traits.neuralComplexity;
  
  for (let layer = 0; layer < layers; layer++) {
    const layerAlpha = traits.alpha * (1 - layer / layers) * 0.7;
    const layerOffset = layer * 2;
    
    ctx.save();
    ctx.translate(layerOffset, layerOffset);
    
    // Main spiral path
    ctx.beginPath();
    let firstPoint = true;
    
    for (let i = 0; i < traits.rings; i++) {
      const t = i * traits.steps;
      const coords = spiralFunc(t, traits, rng);
      
      // Apply wobble and quantum effects
      const wobbleX = Math.sin(t * traits.wobble) * traits.quantumCoherence * 10;
      const wobbleY = Math.cos(t * traits.wobble * 1.1) * traits.quantumCoherence * 10;
      
      const x = coords.r * Math.cos(coords.theta) + wobbleX;
      const y = coords.r * Math.sin(coords.theta) + wobbleY;
      
      if (firstPoint) {
        ctx.moveTo(x, y);
        firstPoint = false;
      } else {
        ctx.lineTo(x, y);
      }
      
      // Dynamic color based on position and layer
      if (i % 10 === 0) {
        const hueIndex = Math.floor((i / traits.rings) * pal.hues.length);
        const hue = pal.hues[hueIndex % pal.hues.length];
        const saturation = pal.saturation[0] + (pal.saturation[1] - pal.saturation[0]) * (layer / layers);
        const lightness = pal.lightness[0] + (pal.lightness[1] - pal.lightness[0]) * (1 - layer / layers);
        
        ctx.strokeStyle = `hsla(${hue}, ${saturation}%, ${lightness * hdrMultiplier}%, ${layerAlpha})`;
        ctx.lineWidth = traits.strokeW * (1 + layer * 0.1);
        ctx.stroke();
        ctx.beginPath();
        ctx.moveTo(x, y);
      }
    }
    
    ctx.stroke();
    ctx.restore();
  }
  
  // Apply HDR bloom effect
  if (pal.hdr && pal.bloom > 0) {
    applyHDRBloom(ctx, traits, pal);
  }
}

function getSpiralFunction(spiralType) {
  switch(spiralType) {
    case 'Quantum Fibonacci':
      return PS5SpiralCalculators.quantumFibonacci;
    case 'Neural Network Spiral':
      return PS5SpiralCalculators.neuralNetworkSpiral;
    case 'Fractal Mandelbrot Spiral':
      return PS5SpiralCalculators.fractalMandelbrotSpiral;
    case 'Hyperdimensional Spiral':
      return PS5SpiralCalculators.hyperdimensionalSpiral;
    case 'Plasma Field Spiral':
      return PS5SpiralCalculators.plasmaFieldSpiral;
    default:
      return PS5SpiralCalculators.quantumFibonacci;
  }
}

function applyHDRBloom(ctx, traits, pal) {
  const bloomRadius = traits.bloomRadius;
  const bloomIntensity = pal.bloom * traits.hdrIntensity;
  
  // Create bloom effect using multiple blur passes
  for (let pass = 0; pass < 3; pass++) {
    ctx.save();
    ctx.globalCompositeOperation = 'screen';
    ctx.globalAlpha = bloomIntensity * (1 - pass * 0.3);
    
    // Simulate blur by drawing multiple offset copies
    for (let offset = 1; offset <= bloomRadius; offset += 2) {
      for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 4) {
        const x = Math.cos(angle) * offset;
        const y = Math.sin(angle) * offset;
        
        ctx.save();
        ctx.translate(x, y);
        ctx.globalAlpha = bloomIntensity / (offset * 2);
        // Re-draw the spiral with bloom
        drawSpiralForBloom(ctx, traits, pal);
        ctx.restore();
      }
    }
    
    ctx.restore();
  }
}

function drawSpiralForBloom(ctx, traits, pal) {
  // Simplified spiral drawing for bloom effect
  const spiralFunc = getSpiralFunction(traits.spiralType);
  
  ctx.beginPath();
  for (let i = 0; i < traits.rings; i += 5) { // Lower resolution for bloom
    const t = i * traits.steps;
    const coords = spiralFunc(t, traits, new PS5SeededRandom(traits.seed));
    
    const x = coords.r * Math.cos(coords.theta);
    const y = coords.r * Math.sin(coords.theta);
    
    if (i === 0) {
      ctx.moveTo(x, y);
    } else {
      ctx.lineTo(x, y);
    }
  }
  
  const hue = pal.hues[0];
  ctx.strokeStyle = `hsla(${hue}, 90%, 80%, 0.1)`;
  ctx.lineWidth = traits.strokeW * 2;
  ctx.stroke();
}

// PS5 Effect Application
function applyPS5Effect(ctx, traits, pal, rng, centerX, centerY) {
  ctx.save();
  
  switch(traits.effect) {
    case 'Ray Traced Reflections':
      PS5EffectsEngine.rayTracedReflections(ctx, traits, pal, rng, centerX, centerY);
      break;
    case 'Volumetric Lighting':
      PS5EffectsEngine.volumetricLighting(ctx, traits, pal, rng, centerX, centerY);
      break;
    case 'Particle Physics Simulation':
      PS5EffectsEngine.particlePhysicsSimulation(ctx, traits, pal, rng, centerX, centerY);
      break;
    case 'Fluid Dynamics':
      PS5EffectsEngine.fluidDynamics(ctx, traits, pal, rng, centerX, centerY);
      break;
    case 'Holographic Interference':
      drawHolographicInterference(ctx, traits, pal, rng);
      break;
    case 'Quantum Field Fluctuations':
      drawQuantumFieldFluctuations(ctx, traits, pal, rng);
      break;
    case 'Electromagnetic Distortion':
      drawElectromagneticDistortion(ctx, traits, pal, rng);
      break;
    case 'Gravitational Lensing':
      drawGravitationalLensing(ctx, traits, pal, rng);
      break;
    case 'Plasma Storm':
      drawPlasmaStorm(ctx, traits, pal, rng);
      break;
    case 'Neural Network Visualization':
      drawNeuralNetworkVisualization(ctx, traits, pal, rng);
      break;
  }
  
  ctx.restore();
}

// Additional effect functions
function drawHolographicInterference(ctx, traits, pal, rng) {
  const interferenceLines = 50;
  
  for (let i = 0; i < interferenceLines; i++) {
    const angle = (i / interferenceLines) * Math.PI * 2;
    const phase = rng.random() * Math.PI * 2;
    
    ctx.beginPath();
    for (let r = 50; r < 400; r += 5) {
      const interference = Math.sin(r * 0.1 + phase) * Math.cos(r * 0.05 + phase * 2);
      const x = r * Math.cos(angle) + interference * 20;
      const y = r * Math.sin(angle) + interference * 20;
      
      if (r === 50) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
    ctx.strokeStyle = `hsla(${hue}, 70%, 60%, 0.3)`;
    ctx.lineWidth = 1;
    ctx.stroke();
  }
}

function drawQuantumFieldFluctuations(ctx, traits, pal, rng) {
  const fieldPoints = 100;
  
  for (let i = 0; i < fieldPoints; i++) {
    const x = rng.randomRange(-400, 400);
    const y = rng.randomRange(-400, 400);
    const fluctuation = rng.quantumRandom();
    
    const size = Math.abs(fluctuation) * 15 + 2;
    const hue = pal.hues[Math.floor(rng.random() * pal.hues.length)];
    const alpha = Math.abs(fluctuation) * 0.5;
    
    ctx.fillStyle = `hsla(${hue}, 80%, 70%, ${alpha})`;
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
    
    // Quantum entanglement lines
    if (rng.random() < 0.1) {
      const targetX = rng.randomRange(-400, 400);
      const targetY = rng.randomRange(-400, 400);
      
      ctx.strokeStyle = `hsla(${hue}, 60%, 50%, 0.2)`;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(targetX, targetY);
      ctx.stroke();
    }
  }
}

module.exports = {
  drawPS5Spiral,
  applyPS5Effect,
  ensureDir
};
