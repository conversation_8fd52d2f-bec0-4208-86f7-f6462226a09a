import { BezierPath, DrawingCommand } from ".";
import { BezierControlPoint } from "./types";
export declare const createFromPath: (el: SVGPathElement) => BezierPath;
export declare const createFromLine: (el: SVGLineElement) => BezierPath;
export declare const createFromCircle: (el: SVGCircleElement) => BezierPath;
export declare const createFromElement: (el: SVGElement) => BezierPath;
export declare const create: (points: BezierControlPoint[]) => BezierPath;
export declare const createFromCommands: (rawCommands: DrawingCommand[]) => BezierPath;
